from decimal import Decimal
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from django.db.models import Sum, F, Q, ExpressionWrapper, FloatField
from django.utils import timezone
from django.db import transaction

from apps.inventory.models import InventoryItem, InventoryMovement, Location, Lot
from apps.products.models import Product, ProductVariant
from .base import BaseInventoryService
from utils.enums import InventoryMovementType

class InventoryService(BaseInventoryService):
    def get_product_availability(
        self, 
        product_id: int,
        variant_id: Optional[int] = None,
        location_id: Optional[int] = None
    ) -> Dict:
        """
        Get detailed product availability across locations
        Including reserved quantities and available to promise
        """
        query = InventoryItem.objects.filter(
            company=self.company,
            product_id=product_id
        )
        
        if variant_id:
            query = query.filter(variant_id=variant_id)
        if location_id:
            query = query.filter(location_id=location_id)

        items = query.select_related('location', 'lot').annotate(
            available=F('quantity') - F('reserved_quantity')
        )

        total_stock = sum(item.quantity for item in items)
        total_reserved = sum(item.reserved_quantity for item in items)
        
        return {
            'total_quantity': total_stock,
            'total_reserved': total_reserved,
            'available_to_promise': total_stock - total_reserved,
            'location_breakdown': [
                {
                    'location': item.location,
                    'quantity': item.quantity,
                    'reserved': item.reserved_quantity,
                    'available': item.available,
                    'lot': item.lot
                }
                for item in items
            ]
        }

    def get_low_stock_alerts(self) -> List[Dict]:
        """
        Get products that are below their minimum stock level
        Returns list of products with their current stock levels and deficits
        """
        low_stock_items = (
            Product.objects.filter(company=self.company)
            .annotate(
                total_stock=Sum('inventory_items__quantity'),
                total_reserved=Sum('inventory_items__reserved_quantity'),
                available_stock=F('total_stock') - F('total_reserved')
            )
            .filter(available_stock__lt=F('min_stock_level'))
        )

        return [
            {
                'product': item,
                'min_level': item.min_stock_level,
                'current_stock': item.total_stock,
                'reserved': item.total_reserved,
                'available': item.available_stock,
                'deficit': item.min_stock_level - item.available_stock
            }
            for item in low_stock_items
        ]

    def get_stock_valuation(self, valuation_method: str = 'FIFO') -> Dict:
        """
        Calculate total inventory value using specified valuation method
        """
        total_value = Decimal('0')
        valuation_details = []

        inventory_items = InventoryItem.objects.filter(
            company=self.company,
            quantity__gt=0
        ).select_related('product', 'variant')

        for item in inventory_items:
            if valuation_method == 'FIFO':
                item_value = item.quantity * item.cost_price
            elif valuation_method == 'AVERAGE':
                # Get average cost from movements
                movements = InventoryMovement.objects.filter(
                    company=self.company,
                    lines__product=item.product,
                    lines__variant=item.variant
                ).order_by('-created_at')[:10]  # Last 10 movements
                
                if movements:
                    avg_cost = sum(m.lines.first().cost_price for m in movements) / len(movements)
                    item_value = item.quantity * avg_cost
                else:
                    item_value = item.quantity * item.cost_price
            else:
                raise ValueError(f"Unsupported valuation method: {valuation_method}")

            total_value += item_value
            valuation_details.append({
                'product': item.product,
                'variant': item.variant,
                'location': item.location,
                'quantity': item.quantity,
                'value': item_value
            })

        return {
            'total_value': total_value,
            'valuation_method': valuation_method,
            'details': valuation_details
        }

    def get_inventory_aging_report(self, age_brackets: List[int] = [30, 60, 90]) -> Dict:
        """
        Generate inventory aging report
        age_brackets is list of days [30, 60, 90] means 0-30, 31-60, 61-90, >90
        """
        now = timezone.now()
        aging_data = []

        inventory_items = InventoryItem.objects.filter(
            company=self.company,
            quantity__gt=0
        ).select_related('product', 'variant', 'lot')

        for item in inventory_items:
            # Get the date this stock was received
            last_receipt = InventoryMovement.objects.filter(
                company=self.company,
                lines__product=item.product,
                lines__variant=item.variant,
                lines__lot=item.lot,
                to_location=item.location
            ).order_by('-created_at').first()

            if last_receipt:
                age_days = (now - last_receipt.created_at).days
                bracket = next(
                    (i for i, days in enumerate(age_brackets) if age_days <= days),
                    len(age_brackets)
                )
            else:
                bracket = 0
                age_days = 0

            aging_data.append({
                'product': item.product,
                'variant': item.variant,
                'location': item.location,
                'quantity': item.quantity,
                'age_days': age_days,
                'age_bracket': bracket,
                'value': item.quantity * item.cost_price
            })

        # Group by age brackets
        summary = {}
        for bracket in range(len(age_brackets) + 1):
            bracket_items = [item for item in aging_data if item['age_bracket'] == bracket]
            if bracket < len(age_brackets):
                bracket_name = f"0-{age_brackets[bracket]} days"
            else:
                bracket_name = f">{age_brackets[-1]} days"

            summary[bracket_name] = {
                'item_count': len(bracket_items),
                'total_value': sum(item['value'] for item in bracket_items),
                'items': bracket_items
            }

        return summary

    def get_stock_movements_history(
        self,
        product_id: Optional[int] = None,
        location_id: Optional[int] = None,
        date_from: Optional[datetime] = None,
        date_to: Optional[datetime] = None
    ) -> List[Dict]:
        """
        Get stock movement history with filtering options
        """
        query = InventoryMovement.objects.filter(company=self.company)

        if product_id:
            query = query.filter(lines__product_id=product_id)
        if location_id:
            query = query.filter(
                Q(from_location_id=location_id) | Q(to_location_id=location_id)
            )
        if date_from:
            query = query.filter(created_at__gte=date_from)
        if date_to:
            query = query.filter(created_at__lte=date_to)

        movements = query.select_related(
            'from_location', 'to_location'
        ).prefetch_related('lines__product', 'lines__variant', 'lines__lot')

        return [
            {
                'date': movement.created_at,
                'type': movement.movement_type,
                'from_location': movement.from_location,
                'to_location': movement.to_location,
                'lines': [
                    {
                        'product': line.product,
                        'variant': line.variant,
                        'quantity': line.quantity,
                        'lot': line.lot
                    }
                    for line in movement.lines.all()
                ]
            }
            for movement in movements
        ]

    @transaction.atomic
    def reserve_stock(
        self,
        product_id: int,
        quantity: Decimal,
        source_type: str,
        source_id: str,
        variant_id: Optional[int] = None,
        lot_id: Optional[int] = None,
        location_id: Optional[int] = None
    ) -> Dict:
        """
        Reserve stock for a specific purpose (e.g., sales order, quality hold)
        """
        # Find available stock
        query = InventoryItem.objects.filter(
            company=self.company,
            product_id=product_id,
            quantity__gt=F('reserved_quantity')
        )

        if variant_id:
            query = query.filter(variant_id=variant_id)
        if lot_id:
            query = query.filter(lot_id=lot_id)
        if location_id:
            query = query.filter(location_id=location_id)

        items = list(query.order_by('created_at'))  # FIFO
        remaining = quantity
        reservations = []

        for item in items:
            available = item.quantity - item.reserved_quantity
            if available <= 0:
                continue

            reserve_qty = min(remaining, available)
            item.reserved_quantity = F('reserved_quantity') + reserve_qty
            item.save()

            reservations.append({
                'item': item,
                'quantity': reserve_qty,
                'source_type': source_type,
                'source_id': source_id
            })

            remaining -= reserve_qty
            if remaining <= 0:
                break

        if remaining > 0:
            raise ValueError(f"Insufficient stock to reserve {quantity} units")

        return {
            'product_id': product_id,
            'total_reserved': quantity,
            'reservations': reservations
        }

    @transaction.atomic
    def release_stock_reservation(
        self,
        product_id: int,
        quantity: Decimal,
        source_type: str,
        source_id: str
    ) -> None:
        """
        Release previously reserved stock
        """
        items = InventoryItem.objects.filter(
            company=self.company,
            product_id=product_id,
            reserved_quantity__gt=0
        ).order_by('created_at')

        remaining = quantity
        for item in items:
            if item.reserved_quantity <= 0:
                continue

            release_qty = min(remaining, item.reserved_quantity)
            item.reserved_quantity = F('reserved_quantity') - release_qty
            item.save()

            remaining -= release_qty
            if remaining <= 0:
                break

        if remaining > 0:
            raise ValueError(f"Could not find {remaining} units to release from reservation")