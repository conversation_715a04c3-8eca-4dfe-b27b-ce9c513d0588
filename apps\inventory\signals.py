# inventory/signals.py
from django.db.models.signals import post_save
from django.dispatch import receiver
from django.db import transaction
from apps.core.models import Warehouse
from apps.inventory.models import Location

@receiver(post_save, sender=Warehouse)
def setup_warehouse_locations(sender, instance, created, **kwargs):
    """
    Sets up required special locations when a new warehouse is created
    """
    if not created:  # Only run on creation, not on updates
        return
        
    try:
        with transaction.atomic():
            # Create Quality Control location
            Location.objects.create(
                company=instance.company,
                warehouse=instance,
                name="Quality Control",
                cnumberode=f"{instance.number}-QC",
                is_quality_control=True,
                is_active=True,
                is_receiving=True  # Since returns need to be received here
            )

            # Create Returns Stock location
            Location.objects.create(
                company=instance.company,
                warehouse=instance,
                name="Returns Stock",
                number=f"{instance.number}-RET",
                is_returns=True,
                is_active=True,
                is_storage=True  # Since items will be stored here
            )

            # Create other essential locations
            Location.objects.create(
                company=instance.company,
                warehouse=instance,
                name="Receiving",
                number=f"{instance.number}-RCV",
                is_receiving=True,
                is_active=True
            )

            Location.objects.create(
                company=instance.company,
                warehouse=instance,
                name="Shipping",
                number=f"{instance.number}-SHP",
                is_shipping=True,
                is_active=True
            )

    except Exception as e:
        # Log the error - implement proper logging
        print(f"Error setting up warehouse locations: {str(e)}")
        # You might want to delete the warehouse if location setup fails
        # Or implement a proper rollback mechanism
        raise