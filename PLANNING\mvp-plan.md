# **WMS MVP Backend Development Plan**

## **1\. Introduction**

This document outlines the detailed development plan for the Minimum Viable Product (MVP) of the Warehouse Management System (WMS) backend. The plan is based on the "Warehouse Management System (WMS) Platform Research" document and focuses on delivering the essential features required for an initial market release. The backend will be built using Python, Django, and Django REST Framework (DRF), with PostgreSQL as the database, Redis for caching, and Celery for asynchronous tasks.

## **2\. Core Technologies & Setup**

* **Language:** Python 3.11+  
* **Framework:** Django 5.x  
* **API:** Django REST Framework (DRF) 3.14+  
* **Database:** PostgreSQL 15+  
* **Caching:** Redis 7.x  
* **Asynchronous Tasks:** Celery 5.x with <PERSON><PERSON> as the message broker and results backend.  
* **Environment Management:** venv or Poetry.  
* **Version Control:** Git.

## **3\. API Design & Architecture Principles**

* **Design:** RESTful API adhering to standard HTTP methods (GET, POST, PUT, PATCH, DELETE).  
* **Data Format:** JSON for all request and response bodies.  
* **Authentication:**  
  * **Scheme:** DRF's built-in TokenAuthentication. Each authenticated user will receive a token upon login, which must be included in the Authorization header for subsequent requests (e.g., Authorization: Token \<user\_token\>).  
  * **Token Management:** Tokens will be generated upon user login and will not have an explicit expiry for the MVP to simplify initial development. Secure token storage on the client-side will be crucial.  
* **Versioning:** API versioning via URL path (e.g., /api/v1/).  
* **Error Handling:**  
  * Use standard HTTP status codes (e.g., 200 OK, 201 Created, 204 No Content, 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 500 Internal Server Error).  
  * DRF's default exception handling will be utilized, providing JSON error responses.  
  * Custom error codes or messages will be added for specific business logic failures where needed.  
* **Permissions:** DRF's permission classes (e.g., IsAuthenticated, IsAdminUser, custom permissions) will be used to control access to endpoints based on user roles.

## **4\. Database Schema (Django Models)**

### **4.1. users.User (Extending django.contrib.auth.models.AbstractUser)**

* id (UUID, Primary Key, default)  
* username (CharField, unique, from AbstractUser)  
* password (CharField, from AbstractUser)  
* email (EmailField, optional)  
* first\_name (CharField, optional)  
* last\_name (CharField, optional)  
* role (CharField, choices: ADMIN, WAREHOUSE\_WORKER, MANAGER \- default: WAREHOUSE\_WORKER)  
* is\_active (BooleanField, default: True)  
* is\_staff (BooleanField, default: False \- for Django admin access)  
* date\_joined (DateTimeField, auto\_now\_add=True)

### **4.2. inventory.Product**

* id (UUID, Primary Key, default)  
* sku (CharField, max\_length=100, unique, db\_index=True, help\_text="Stock Keeping Unit")  
* name (CharField, max\_length=255)  
* description (TextField, blank=True, null=True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.3. inventory.Location**

* id (UUID, Primary Key, default)  
* code (CharField, max\_length=50, unique, help\_text="e.g., A-01-01-A (Zone-Aisle-Rack-Bin)")  
* zone (CharField, max\_length=50, blank=True, null=True)  
* aisle (CharField, max\_length=50, blank=True, null=True)  
* rack (CharField, max\_length=50, blank=True, null=True)  
* bin (CharField, max\_length=50, blank=True, null=True)  
* description (CharField, max\_length=255, blank=True, null=True)  
* is\_active (BooleanField, default: True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.4. inventory.InventoryItem**

* id (UUID, Primary Key, default)  
* product (ForeignKey to Product, on\_delete=models.CASCADE, related\_name='inventory\_items')  
* location (ForeignKey to Location, on\_delete=models.PROTECT, related\_name='inventory\_items')  
* quantity (PositiveIntegerField, default=0)  
* last\_counted\_at (DateTimeField, null=True, blank=True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)  
  * **Constraint:** Unique together (product, location)

### **4.5. orders.Order**

* id (UUID, Primary Key, default)  
* order\_number (CharField, max\_length=50, unique, db\_index=True, help\_text="External order identifier or auto-generated")  
* status (CharField, max\_length=20, choices: PENDING, ALLOCATED, PICKING, PACKED, SHIPPED, CANCELLED, default: PENDING)  
* customer\_name (CharField, max\_length=255, blank=True, null=True, help\_text="For MVP, simple name. Future: Link to Customer model")  
* shipping\_address (TextField, blank=True, null=True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.6. orders.OrderLineItem**

* id (UUID, Primary Key, default)  
* order (ForeignKey to Order, on\_delete=models.CASCADE, related\_name='line\_items')  
* product (ForeignKey to Product, on\_delete=models.PROTECT)  
* quantity\_ordered (PositiveIntegerField)  
* quantity\_picked (PositiveIntegerField, default=0)  
* unit\_price (DecimalField, max\_digits=10, decimal\_places=2, null=True, blank=True, help\_text="Price at time of order")  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.7. fulfillment.PickList**

* id (UUID, Primary Key, default)  
* order (ForeignKey to Order, on\_delete=models.CASCADE, related\_name='pick\_lists')  
* assigned\_to (ForeignKey to User, null=True, blank=True, on\_delete=models.SET\_NULL, related\_name='pick\_tasks')  
* status (CharField, max\_length=20, choices: PENDING, IN\_PROGRESS, COMPLETED, CANCELLED, default: PENDING)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.8. fulfillment.PickListItem**

* id (UUID, Primary Key, default)  
* pick\_list (ForeignKey to PickList, on\_delete=models.CASCADE, related\_name='items')  
* order\_line\_item (ForeignKey to OrderLineItem, on\_delete=models.CASCADE)  
* product (ForeignKey to Product, on\_delete=models.PROTECT)  
* location (ForeignKey to Location, on\_delete=models.PROTECT, help\_text="Suggested pick location")  
* quantity\_to\_pick (PositiveIntegerField)  
* quantity\_picked (PositiveIntegerField, default=0)  
* status (CharField, max\_length=20, choices: PENDING, PICKED, SHORT\_PICKED, NOT\_FOUND, default: PENDING)  
* picked\_by (ForeignKey to User, null=True, blank=True, on\_delete=models.SET\_NULL)  
* picked\_at (DateTimeField, null=True, blank=True)

### **4.9. receiving.InboundReceipt**

* id (UUID, Primary Key, default)  
* reference\_number (CharField, max\_length=100, blank=True, null=True, help\_text="e.g., PO number, ASN")  
* status (CharField, max\_length=20, choices: PENDING, IN\_PROGRESS, COMPLETED, CANCELLED, default: PENDING)  
* received\_by (ForeignKey to User, null=True, blank=True, on\_delete=models.SET\_NULL)  
* received\_at (DateTimeField, null=True, blank=True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.10. receiving.InboundReceiptItem**

* id (UUID, Primary Key, default)  
* receipt (ForeignKey to InboundReceipt, on\_delete=models.CASCADE, related\_name='items')  
* product (ForeignKey to Product, on\_delete=models.PROTECT)  
* expected\_quantity (PositiveIntegerField, default=0)  
* received\_quantity (PositiveIntegerField, default=0)  
* putaway\_location (ForeignKey to Location, null=True, blank=True, on\_delete=models.SET\_NULL, help\_text="Location where item was put away")  
* notes (TextField, blank=True, null=True)

### **4.11. returns.ReturnMerchandiseAuthorization (RMA)**

* id (UUID, Primary Key, default)  
* rma\_number (CharField, max\_length=50, unique, db\_index=True)  
* order (ForeignKey to Order, null=True, blank=True, on\_delete=models.SET\_NULL, help\_text="Original order if known")  
* customer\_name (CharField, max\_length=255, blank=True, null=True)  
* status (CharField, max\_length=20, choices: PENDING\_RECEIPT, RECEIVED, INSPECTING, PROCESSED, CANCELLED, default: PENDING\_RECEIPT)  
* reason\_code (CharField, max\_length=50, blank=True, null=True, help\_text="e.g., DAMAGED, WRONG\_ITEM, CUSTOMER\_REMORSE")  
* created\_by (ForeignKey to User, on\_delete=models.SET\_NULL, null=True)  
* created\_at (DateTimeField, auto\_now\_add=True)  
* updated\_at (DateTimeField, auto\_now=True)

### **4.12. returns.RMAItem**

* id (UUID, Primary Key, default)  
* rma (ForeignKey to ReturnMerchandiseAuthorization, on\_delete=models.CASCADE, related\_name='items')  
* product (ForeignKey to Product, on\_delete=models.PROTECT)  
* quantity\_expected (PositiveIntegerField)  
* quantity\_received (PositiveIntegerField, default=0)  
* disposition (CharField, max\_length=20, choices: RESTOCK, QUARANTINE, DISPOSE, null=True, blank=True)  
* notes (TextField, blank=True, null=True)

## **5\. API Endpoint Breakdown (MVP)**

All endpoints are prefixed with /api/v1/. Permissions: IsAuthenticated by default unless specified.

### **5.1. Authentication**

* **POST /auth/token/login/**  
  * **Description:** Obtain authentication token.  
  * **Request Body:** {"username": "user", "password": "password"}  
  * **Response (200 OK):** {"token": "\<user\_token\>", "user\_id": "\<user\_uuid\>", "role": "WAREHOUSE\_WORKER"}  
  * **Permissions:** AllowAny  
* **POST /auth/token/logout/**  
  * **Description:** Invalidate authentication token (server-side token invalidation if using DRF's TokenAuthentication, or simply instruct client to delete token).  
  * **Response (204 No Content)**  
  * **Permissions:** IsAuthenticated

### **5.2. Inventory Management**

* **GET, POST /products/**  
  * **GET Description:** List all products. Supports pagination.  
    * **Query Params:** sku, name (for filtering/searching).  
    * **Response (200 OK):** \[{"id": "uuid", "sku": "SKU001", "name": "Product A", ...}, ...\]  
  * **POST Description:** Create a new product.  
    * **Request Body:** {"sku": "SKU002", "name": "Product B", "description": "Optional desc"}  
    * **Response (201 Created):** {"id": "uuid", "sku": "SKU002", ...}  
    * **Permissions (POST):** Admin/Manager role.  
* **GET, PUT, PATCH /products/{id}/** (id is product UUID)  
  * **GET Description:** Retrieve a specific product.  
    * **Response (200 OK):** {"id": "uuid", "sku": "SKU001", ...}  
  * **PUT/PATCH Description:** Update a specific product.  
    * **Request Body:** Fields to update, e.g., {"name": "Updated Product Name"}  
    * **Response (200 OK):** Updated product data.  
    * **Permissions (PUT/PATCH):** Admin/Manager role.  
* **GET, POST /locations/**  
  * **GET Description:** List all warehouse locations. Supports pagination.  
    * **Query Params:** code, zone (for filtering).  
    * **Response (200 OK):** \[{"id": "uuid", "code": "A-01-01", ...}, ...\]  
  * **POST Description:** Create a new location.  
    * **Request Body:** {"code": "B-02-03", "zone": "B", ...}  
    * **Response (201 Created):** {"id": "uuid", "code": "B-02-03", ...}  
    * **Permissions (POST):** Admin/Manager role.  
* **GET, PUT, PATCH /locations/{id}/** (id is location UUID)  
  * **GET Description:** Retrieve a specific location.  
  * **PUT/PATCH Description:** Update a specific location.  
    * **Permissions (PUT/PATCH):** Admin/Manager role.  
* **GET /inventory-items/**  
  * **Description:** List all inventory items (stock levels per product per location). Supports pagination.  
  * **Query Params:** product\_sku, location\_code, min\_quantity.  
  * **Response (200 OK):** \[{"id": "uuid", "product": {"sku": "SKU001", ...}, "location": {"code": "A-01-01", ...}, "quantity": 100}, ...\]  
* **GET /reports/inventory-snapshot/** (Potentially Async)  
  * **Description:** Generate a snapshot of current inventory levels across all products and locations.  
  * **Response (200 OK or 202 Accepted if async):** \[{"sku": "SKU001", "product\_name": "Product A", "total\_quantity": 150, "locations": \[{"code": "A-01", "quantity": 100}, ...\]}, ...\]

### **5.3. Receiving (Basic Inbound)**

* **POST /receiving/receipts/**  
  * **Description:** Create a new inbound receipt record.  
  * **Request Body:** {"reference\_number": "PO12345", "received\_by\_id": "\<user\_uuid\>"}  
  * **Response (201 Created):** {"id": "\<receipt\_uuid\>", "status": "PENDING", ...}  
* **GET /receiving/receipts/{id}/**  
  * **Description:** Get details of an inbound receipt.  
  * **Response (200 OK):** {"id": "\<receipt\_uuid\>", "items": \[...\], ...}  
* **POST /receiving/receipts/{id}/items/**  
  * **Description:** Add an item to an inbound receipt and update inventory.  
  * **Request Body:** {"product\_sku": "SKU001", "received\_quantity": 50, "putaway\_location\_code": "A-01-02"}  
  * **Response (201 Created):** Receipt item details.  
  * **Logic:** This endpoint will handle the actual inventory increase in InventoryItem. If InventoryItem for product+location doesn't exist, it's created.  
* **PATCH /receiving/receipts/{id}/complete/**  
  * **Description:** Mark an inbound receipt as completed.  
  * **Response (200 OK):** Updated receipt details.

### **5.4. Order Management**

* **POST /orders/**  
  * **Description:** Manually create a new order.  
  * **Request Body:** {"order\_number": "ORD1001", "customer\_name": "John Doe", "line\_items": \[{"product\_sku": "SKU001", "quantity\_ordered": 2}, ...\]}  
  * **Response (201 Created):** {"id": "\<order\_uuid\>", "status": "PENDING", ...}  
* **GET /orders/**  
  * **Description:** List all orders. Supports pagination.  
  * **Query Params:** status, order\_number, customer\_name.  
  * **Response (200 OK):** \[{"id": "\<order\_uuid\>", "order\_number": "ORD1001", "status": "PENDING", ...}, ...\]  
* **GET, PATCH /orders/{id}/** (id is order UUID)  
  * **GET Description:** Retrieve a specific order with its line items.  
    * **Response (200 OK):** {"id": "\<order\_uuid\>", ..., "line\_items": \[...\]}  
  * **PATCH Description:** Update order status or details (e.g., shipping address).  
    * **Request Body:** {"status": "CANCELLED"} or {"shipping\_address": "New Address"}  
    * **Response (200 OK):** Updated order data.  
* **POST /orders/{id}/allocate/**  
  * **Description:** Allocate inventory to an order.  
  * **Logic:** Checks available stock in InventoryItem and updates OrderLineItem.quantity\_picked (conceptually, for MVP, this might directly lead to pick list generation or be a status change). Changes order status to ALLOCATED.  
  * **Response (200 OK):** {"message": "Inventory allocated successfully", "order\_status": "ALLOCATED"}

### **5.5. Fulfillment (Picking & Packing)**

* **POST /fulfillment/picklists/generate/**  
  * **Description:** Generate a pick list for an ALLOCATED order.  
  * **Request Body:** {"order\_id": "\<order\_uuid\>"}  
  * **Response (201 Created):** {"id": "\<picklist\_uuid\>", "status": "PENDING", "order\_id": "\<order\_uuid\>", "items": \[...\]}  
  * **Logic:** Creates PickList and PickListItem records. Suggests pick locations based on InventoryItem data (e.g., oldest stock, or simplest: any location with stock). Changes order status to PICKING.  
* **GET /fulfillment/picklists/next/**  
  * **Description:** Get the next available PENDING pick list for a worker (simple assignment for MVP).  
  * **Query Params:** assigned\_to\_me=true (optional, if worker ID is known via token).  
  * **Response (200 OK):** {"id": "\<picklist\_uuid\>", ...} or 204 No Content if no pending pick lists.  
* **PATCH /fulfillment/picklists/{id}/assign/**  
  * **Description:** Assign a pick list to the current user (or a specified user by admin).  
  * **Request Body (optional for self-assign):** {"user\_id": "\<user\_uuid\>"}  
  * **Response (200 OK):** Updated pick list with assigned\_to and status IN\_PROGRESS.  
* **POST /fulfillment/pickitems/{item\_id}/pick/** (item\_id is PickListItem UUID)  
  * **Description:** Confirm a pick for a specific item on a pick list.  
  * **Request Body:** {"picked\_quantity": 5, "location\_scanned\_code": "A-01-01"} (location scan for confirmation)  
  * **Response (200 OK):** Updated PickListItem details.  
  * **Logic:**  
    * Updates PickListItem.quantity\_picked, status to PICKED.  
    * Decrements InventoryItem.quantity from the picked location.  
    * If all items in PickList are PICKED or SHORT\_PICKED, updates PickList.status to COMPLETED.  
* **POST /fulfillment/pickitems/{item\_id}/exception/**  
  * **Description:** Report a picking exception (e.g., item not found, short pick).  
  * **Request Body:** {"exception\_type": "SHORT\_PICKED", "actual\_quantity": 3, "notes": "Only 3 found"}  
  * **Response (200 OK):** Updated PickListItem with exception details.  
* **POST /orders/{id}/pack/**  
  * **Description:** Confirm that an order (whose pick list is COMPLETED) has been packed.  
  * **Response (200 OK):** {"message": "Order packed successfully", "order\_status": "PACKED"}  
  * **Logic:** Changes order status to PACKED.  
* **POST /orders/{id}/ship/**  
  * **Description:** Basic endpoint to log the generation of a shipping label and mark order as shipped.  
  * **Request Body:** {"carrier": "PostNord", "tracking\_number": "PN123456789SE"}  
  * **Response (200 OK):** {"message": "Order marked as shipped", "order\_status": "SHIPPED"}  
  * **Logic:** Changes order status to SHIPPED.

### **5.6. Returns Management**

* **POST /returns/rmas/**  
  * **Description:** Create a simple Return Merchandise Authorization.  
  * **Request Body:** {"rma\_number": "RMA001", "order\_id": "\<optional\_order\_uuid\>", "customer\_name": "Jane Doe", "reason\_code": "DAMAGED", "items": \[{"product\_sku": "SKU001", "quantity\_expected": 1}\]}  
  * **Response (201 Created):** {"id": "\<rma\_uuid\>", "status": "PENDING\_RECEIPT", ...}  
* **GET /returns/rmas/**  
  * **Description:** List all RMAs.  
  * **Query Params:** status, rma\_number.  
  * **Response (200 OK):** List of RMA objects.  
* **GET, PATCH /returns/rmas/{id}/** (id is RMA UUID)  
  * **GET Description:** Retrieve RMA details.  
  * **PATCH Description:** Update RMA status or details.  
    * **Response (200 OK):** Updated RMA data.  
* **POST /returns/rmas/{id}/receive-item/**  
  * **Description:** Process a received item for an RMA, including restocking inventory if applicable.  
  * **Request Body:** {"rma\_item\_id": "\<rma\_item\_uuid\>", "quantity\_received": 1, "disposition": "RESTOCK", "restock\_location\_code": "RETURNS\_AREA"}  
  * **Response (200 OK):** Updated RMA item and RMA status.  
  * **Logic:** Updates RMAItem.quantity\_received and disposition. If disposition is RESTOCK, increases InventoryItem.quantity at the specified restock\_location\_code.

### **5.7. Admin & User Management**

* **GET, POST /admin/users/**  
  * **GET Description:** List all users.  
  * **POST Description:** Create a new user.  
    * **Request Body:** {"username": "newuser", "password": "securepassword", "role": "WAREHOUSE\_WORKER"}  
  * **Permissions:** Admin role.  
* **GET, PUT, PATCH /admin/users/{id}/** (id is user UUID)  
  * **Description:** Retrieve, update, or partially update user details (e.g., assign roles, activate/deactivate).  
  * **Permissions:** Admin role.

## **6\. Asynchronous Task Management (Celery)**

* **Reporting:**  
  * tasks.generate\_daily\_operations\_report(): Aggregates orders processed, picks completed, items shipped. (Output to a simple format or internal log for MVP).  
  * tasks.generate\_inventory\_snapshot\_report(): As described in API endpoint. Could be triggered periodically or on demand.  
* **Notifications (Future MVP consideration, not core for initial launch unless critical):**  
  * tasks.send\_low\_stock\_alert\_email(): If inventory for a product drops below a threshold (thresholds not defined in MVP, but task structure can be).  
* **Task Monitoring:** Basic Celery Flower setup for monitoring task queues and worker status.

## **7\. Data Validation & Serialization**

* DRF Serializers will be used for validating incoming data and formatting outgoing data for all API endpoints.  
* Serializers will define required fields, data types, length constraints, and custom validation rules (e.g., ensuring quantity\_picked does not exceed quantity\_ordered).  
* Nested serializers will be used for related objects (e.g., OrderSerializer including OrderLineItemSerializer).

## **8\. Security Considerations (MVP)**

* **Input Validation:** Handled by DRF serializers.  
* **Authentication & Authorization:** As described via DRF TokenAuthentication and permission classes.  
* **HTTPS:** Mandatory for all communication in staging/production (handled by deployment environment).  
* **Django Security Features:** Utilize built-in protections against XSS, CSRF (though CSRF less relevant for token-based APIs), SQL injection.  
* **Dependencies:** Keep Python and Django packages updated to patch known vulnerabilities.  
* **Sensitive Data:** Passwords will be hashed by Django. Avoid logging sensitive information like tokens.

## **9\. Testing Strategy (MVP)**

* **Unit Tests (PyTest or Django's unittest):**  
  * Test individual model methods and custom manager logic.  
  * Test utility functions.  
  * Test Celery task logic (individual functions within tasks).  
* **API/Integration Tests (DRF APITestCase or PyTest with an API client):**  
  * Test each API endpoint for:  
    * Correct responses for valid requests (2xx status codes).  
    * Correct error responses for invalid requests (4xx status codes).  
    * Authentication and permission enforcement.  
    * Side effects (e.g., database changes).  
* **Test Coverage:** Aim for a reasonable level of coverage for critical business logic and API endpoints. Tools like coverage.py will be used.

## **10\. Infrastructure & Deployment (MVP)**

* **Containerization:** Docker and docker-compose.yml to define services (app, db, redis, celery worker, celery beat \- if scheduled tasks are needed).  
* **Database:** PostgreSQL running in a Docker container for local development. Cloud-managed PostgreSQL (e.g., AWS RDS, Google Cloud SQL) for staging/production.  
* **Web Server:** Gunicorn to serve the Django application.  
* **Reverse Proxy:** Nginx in front of Gunicorn to handle static files, SSL termination, and load balancing (if scaled beyond single instance).  
* **CI/CD:**  
  * Basic pipeline using GitHub Actions or GitLab CI.  
  * Steps: Linting (Flake8, Black), Run Tests, Build Docker Image, Push to Registry (e.g., Docker Hub, AWS ECR), Deploy to Staging/Production.  
* **Logging:** Django's built-in logging configured to output to console (for Docker) and potentially to a centralized logging service in the future (e.g., ELK stack, CloudWatch Logs).  
* **Monitoring:** Basic monitoring via Celery Flower for tasks, and potentially a simple health check endpoint in Django.

## **11\. MVP Scope Limitations & Future Considerations**

* **No advanced receiving:** ASN processing, quality control workflows.  
* **No advanced picking strategies:** Wave picking, batch picking, zone picking, path optimization.  
* **No advanced inventory management:** Cycle counting, ABC analysis, lot/serial tracking.  
* **Limited reporting:** Only basic operational and inventory snapshots.  
* **No direct carrier integration:** Shipping label generation is logged, not actually created via API.  
* **No multi-warehouse:** Assumes a single warehouse context.  
* **No UI for admin tasks:** Assumes Django Admin or API usage for initial setup.

This detailed plan provides a solid foundation for building the WMS backend MVP. Regular reviews and adjustments will be necessary as development progresses.