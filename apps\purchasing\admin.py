from django.contrib import admin
from django.db.models import Count, Sum, F
from django.utils.html import format_html
from django.contrib.humanize.templatetags.humanize import intcomma
from .models import (
    Supplier, 
    SupplierProduct, 
    PurchaseOrder, 
    PurchaseOrderLine,
    GoodsReceipt,
    GoodsReceiptLine,
    SupplierInvoice,
    SupplierInvoiceLine,
    SupplierAddress,
    SupplierContact,
    SupplierCategory
)
from django.db import models

class SupplierAddressInline(admin.TabularInline):
    model = SupplierAddress
    extra = 0
    fields = ['name', 'address_type', 'street_address1', 'street_address2', 'city', 'state', 'zip_code', 'country', 'notes', 'is_default']

class SupplierContactInline(admin.TabularInline):
    model = SupplierContact
    extra = 0
    fields = ['name', 'email', 'phone', 'mobile', 'role', 'notes', 'is_primary']



class SupplierProductInline(admin.TabularInline):
    model = SupplierProduct
    extra = 0
    raw_id_fields = ['product', 'variant']
    fields = ['product', 'variant', 'supplier_sku', 'price', 'min_order_quantity', 'lead_time_days', 'is_preferred']

@admin.register(Supplier)
class SupplierAdmin(admin.ModelAdmin):
    list_display = [
        'name', 
        'number', 
        'email', 
        'phone', 
        'currency',
        'payment_terms',
        'active_products_count',
        'open_orders_count',
        'is_active'
    ]
    list_filter = ['company', 'currency', 'is_active']
    search_fields = ['name', 'number', 'email', 'phone', 'tax_number']
    inlines = [SupplierProductInline, SupplierAddressInline, SupplierContactInline]
    
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'number', 'category', 'is_active')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'street_address1', 'street_address2', 'city', 'state', 'zip_code', 'country')
        }),
        ('Business Details', {
            'fields': ('tax_number', 'payment_terms', 'currency', 'vat_zone')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            active_products_count=Count('products', filter=models.Q(products__product__is_active=True)),
            open_orders_count=Count(
                'purchase_orders',
                filter=~models.Q(purchase_orders__status__in=['COMPLETED', 'CANCELLED'])
            )
        )
    
    def save_formset(self, request, form, formset, change):
        instances = formset.save(commit=False)
        for instance in instances:
            instance.supplier = form.instance
            instance.company = form.instance.company
            instance.save()
        formset.save_m2m()
    
    def active_products_count(self, obj):
        return obj.active_products_count
    active_products_count.short_description = 'Active Products'
    active_products_count.admin_order_field = 'active_products_count'
    
    def open_orders_count(self, obj):
        return obj.open_orders_count
    open_orders_count.short_description = 'Open Orders'
    open_orders_count.admin_order_field = 'open_orders_count'


@admin.register(SupplierCategory)
class SupplierCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'number', 'parent']
    search_fields = ['name', 'number']
    list_filter = ['company']

@admin.register(SupplierProduct)
class SupplierProductAdmin(admin.ModelAdmin):
    list_display = [
        'supplier',
        'product',
        'variant',
        'supplier_sku',
        'price',
        'min_order_quantity',
        'lead_time_days',
        'is_preferred'
    ]
    list_filter = ['company', 'supplier', 'is_preferred']
    search_fields = [
        'supplier__name',
        'product__name',
        'product__sku',
        'supplier_sku'
    ]
    raw_id_fields = ['supplier', 'product', 'variant']

class PurchaseOrderLineInline(admin.TabularInline):
    model = PurchaseOrderLine
    extra = 0
    raw_id_fields = ['product', 'variant']
    fields = [
        'product', 
        'variant', 
        'quantity', 
        'received_quantity', 
        'unit_price', 
        'tax_rate', 
        'net_amount',
        'tax_amount', 
        'total_amount',
        'expected_delivery_date'
    ]
    readonly_fields = [
        'received_quantity',
        'net_amount',
        'tax_amount',
        'total_amount'
    ]

    def net_amount(self, obj):
        return format_html('${:,.2f}', obj.net_amount) if obj.net_amount else '-'
    net_amount.short_description = 'Net Amount'

    def tax_amount(self, obj):
        return format_html('${:,.2f}', obj.tax_amount) if obj.tax_amount else '-'
    tax_amount.short_description = 'Tax'

    def total_amount(self, obj):
        return format_html('${:,.2f}', obj.total_amount) if obj.total_amount else '-'
    total_amount.short_description = 'Total'

class GoodsReceiptInline(admin.TabularInline):
    model = GoodsReceipt
    extra = 0
    fields = ['reference_number', 'receipt_date', 'receiver']
    show_change_link = True

@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = [
        'number',
        'supplier',
        'order_date',
        'expected_delivery_date',
        'status',
        'receiving_location',
        'receipt_status',
        'total_net_amount',
        'display_total_net',
        'display_total_tax',
        'display_total_amount'
    ]
    list_filter = ['company', 'status', 'order_date', 'expected_delivery_date']
    search_fields = ['number', 'supplier__name', 'notes']
    raw_id_fields = ['supplier', 'receiving_location']
    inlines = [PurchaseOrderLineInline, GoodsReceiptInline]
    readonly_fields = [
        'display_total_net',
        'display_total_tax',
        'display_total_amount',
        'display_total_discount',
        'receipt_status'
    ]
    date_hierarchy = 'order_date'
    
    fieldsets = (
        (None, {
            'fields': ('company', 'number', 'supplier', 'status')
        }),
        ('Dates', {
            'fields': ('order_date', 'expected_delivery_date')
        }),
        ('Delivery', {
            'fields': ('receiving_location',)
        }),
        ('Financial Details', {
            'fields': (
                'currency',
                'currency_rate',
                'payment_term',
                'delivery_term',
                'delivery_method',
                'prices_include_tax',
                'display_total_net',
                'display_total_tax',
                'display_total_discount',
                'display_total_amount'
            )
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        })
    )

    def display_total_net(self, obj):
        return format_html(
            '<span style="white-space: nowrap">{} {}</span>',
            obj.currency.currency if obj.currency else '$',
            '{:,.2f}'.format(obj.total_net_amount)
        )
    display_total_net.short_description = 'Net Amount'
    display_total_net.admin_order_field = 'total_net_amount'

    def display_total_tax(self, obj):
        return format_html(
            '<span style="white-space: nowrap">{} {}</span>',
            obj.currency.currency if obj.currency else '$',
            '{:,.2f}'.format(obj.total_tax_amount)
        )
    display_total_tax.short_description = 'Tax'
    display_total_tax.admin_order_field = 'total_tax_amount'

    def display_total_amount(self, obj):
        return format_html(
            '<span style="white-space: nowrap; font-weight: bold">{} {}</span>',
            obj.currency.currency if obj.currency else '$',
            '{:,.2f}'.format(obj.total_amount)
        )
    display_total_amount.short_description = 'Total Amount'
    display_total_amount.admin_order_field = 'total_amount'

    def display_total_discount(self, obj):
        return format_html(
            '<span style="white-space: nowrap">{} {}</span>',
            obj.currency.symbol if obj.currency else '$',
            '{:,.2f}'.format(obj.total_discount)
        )
    display_total_discount.short_description = 'Discount'
    display_total_discount.admin_order_field = 'total_discount'

    def receipt_status(self, obj):
        total_lines = obj.lines.count()
        if total_lines == 0:
            return 'No lines'
            
        fully_received = obj.lines.filter(
            quantity=F('received_quantity')
        ).count()
        
        if fully_received == 0:
            return format_html(
                '<span style="color: red;">Not received</span>'
            )
        elif fully_received == total_lines:
            return format_html(
                '<span style="color: green;">Fully received</span>'
            )
        else:
            return format_html(
                '<span style="color: orange;">Partially received ({}/{})</span>',
                fully_received,
                total_lines
            )
    receipt_status.short_description = 'Receipt Status'

class GoodsReceiptLineInline(admin.TabularInline):
    model = GoodsReceiptLine
    extra = 0
    raw_id_fields = ['purchase_order_line']
    fields = ['purchase_order_line', 'quantity', 'lot_number', 'expiry_date']

@admin.register(GoodsReceipt)
class GoodsReceiptAdmin(admin.ModelAdmin):
    list_display = [
        'reference_number',
        'purchase_order',
        'receipt_date',
        'receiver',
        'line_count',
        'total_quantity'
    ]
    list_filter = ['company', 'receipt_date', 'receiver']
    search_fields = [
        'reference_number',
        'purchase_order__number',
        'notes'
    ]
    raw_id_fields = ['purchase_order', 'receiver']
    inlines = [GoodsReceiptLineInline]
    date_hierarchy = 'receipt_date'
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            line_count=Count('lines'),
            total_quantity=Sum('lines__quantity')
        )
    
    def line_count(self, obj):
        return obj.line_count
    line_count.admin_order_field = 'line_count'
    
    def total_quantity(self, obj):
        return obj.total_quantity or 0
    total_quantity.admin_order_field = 'total_quantity'

class SupplierInvoiceLineInline(admin.TabularInline):
    model = SupplierInvoiceLine
    extra = 0
    raw_id_fields = ['purchase_order_line']
    fields = ['purchase_order_line', 'quantity', 'unit_price', 'tax_rate', 'net_amount', 'tax_amount', 'total_amount']

@admin.register(SupplierInvoice)
class SupplierInvoiceAdmin(admin.ModelAdmin):
    list_display = ['number', 'supplier', 'invoice_date', 'due_date', 'status', 'total_amount']
    list_filter = ['company', 'invoice_date', 'due_date']
    search_fields = ['number', 'supplier__name', 'notes']
    raw_id_fields = ['supplier']
    inlines = [SupplierInvoiceLineInline]
