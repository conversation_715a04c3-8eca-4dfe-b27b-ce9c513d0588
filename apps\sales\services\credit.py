# sales/services/credit.py
from decimal import Decimal
from typing import Dict
from django.db.models import Sum, F, Q
from django.utils import timezone
from ..models import Customer, SalesOrder, CreditLimitChange, SalesInvoice
from .base import BaseSalesService
from django.db import transaction
from datetime import timedelta, date
from utils.enums import SalesOrderStatus, CustomerCreditStatus, InvoiceStatus


class CreditManagementService(BaseSalesService):
    @transaction.atomic
    def get_credit_status(self, customer_id: int) -> Dict:
        """Get customer's current credit status"""
        customer = Customer.objects.get(
            company=self.company,
            id=customer_id
        )
        
        outstanding_invoices = SalesInvoice.objects.filter(
            company=self.company,
            customer=customer,
            status__in=[
                InvoiceStatus.SENT.name, InvoiceStatus.PARTIAL_PAID.name, 
            ]
        )
        
        outstanding_amount = outstanding_invoices.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0')
        
        # Calculate overdue using a subquery instead of F() expression
        today = timezone.now().date()
        overdue_invoices = outstanding_invoices.filter(due_date__lt=today)
        
        overdue_amount = overdue_invoices.aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0')

        return {
            'credit_limit': customer.credit_limit,
            'available_credit': customer.credit_limit - outstanding_amount if customer.credit_limit else None,
            'outstanding_amount': outstanding_amount,
            'overdue_amount': overdue_amount,
            'payment_terms': customer.payment_terms.name if customer.payment_terms else None,
            'status': self._determine_credit_status(
                customer.credit_limit,
                outstanding_amount,
                overdue_amount
            )
        }

    def _determine_credit_status(
        self,
        credit_limit: Decimal,
        outstanding: Decimal,
        overdue: Decimal
    ) -> str:
        if overdue > 0:
            return CustomerCreditStatus.ON_HOLD.name
        if not credit_limit:
            return CustomerCreditStatus.NO_LIMIT.name
        if outstanding >= credit_limit:
            return CustomerCreditStatus.LIMIT_REACHED.name
        if outstanding >= (credit_limit * Decimal('0.8')):
            return CustomerCreditStatus.NEAR_LIMIT.name
        return CustomerCreditStatus.GOOD.name

    @transaction.atomic
    def adjust_credit_limit(
        self,
        customer_id: int,
        new_limit: Decimal,
        reason: str
    ) -> Customer:
        """Adjust customer's credit limit with audit trail"""
        customer = Customer.objects.get(
            company=self.company,
            id=customer_id
        )
        
        old_limit = customer.credit_limit
        customer.credit_limit = new_limit
        customer.save()
        
        # Create audit log entry
        CreditLimitChange.objects.create(
            company=self.company,
            customer=customer,
            old_limit=old_limit,
            new_limit=new_limit,
            changed_by=self.user,
            reason=reason
        )
        
        return customer

    def get_payment_history(
        self,
        customer_id: int,
        months: int = 12
    ) -> Dict:
        """Analyze customer's payment history"""
        start_date = timezone.now().date() - timedelta(days=30 * months)
        
        completed_orders = SalesOrder.objects.filter(
            company=self.company,
            customer_id=customer_id,
            status=SalesOrderStatus.COMPLETED.name,
            order_date__gte=start_date
        )
        
        return {
            'total_orders': completed_orders.count(),
            'total_value': completed_orders.aggregate(
                total=Sum('total_amount')
            )['total'] or 0,
            'average_payment_time': self._calculate_average_payment_time(completed_orders),
            'overdue_history': self._get_overdue_history(completed_orders)
        }

    def _calculate_average_payment_time(self, orders) -> float:
        payment_times = []
        for order in orders:
            if order.final_paid_date:
                days = (order.final_paid_date - order.order_date).days
                payment_times.append(days)
        
        return sum(payment_times) / len(payment_times) if payment_times else 0