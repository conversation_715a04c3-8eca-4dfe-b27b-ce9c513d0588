# sales/models.py
from django.db import models
from apps.base.models import CompanyFKRel
from apps.core.models import Warehouse
from decimal import Decimal
from utils.enums import DiscountType, SalesOrderStatus,ShipmentStatus, ReturnStatus, ReturnLineStatus, InspectionResult, ReturnCreditNoteStatus
from django.utils import timezone
from apps.base.models import BaseInvoice, BaseTransactionLine, BaseTransaction, RelationBase, ContactBase, AddressBase, BasePriceHistory, CategoryBase
from django.db import transaction
from apps.sales.mixins import PriceHistoryMixin
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType


class CustomerCategory(CategoryBase):
    class Meta:
        unique_together = ['company', 'number']


class Customer(CompanyFKRel, RelationBase):
    category = models.ForeignKey(
        CustomerCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customers'
    )
    credit_limit = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )
    payment_term = models.ForeignKey(
        'core.PaymentTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_payment_terms'
    )
    delivery_term = models.ForeignKey(
        'core.DeliveryTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_delivery_terms'
    )
    language = models.ForeignKey(
        'core.Language',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customers'
    )
    vat_zone = models.CharField(max_length=50, blank=True)
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='customer'
    )

    class Meta:
        unique_together = ['company', 'number']

    def save(self, *args, **kwargs):
        if not self.pk:  # Only set defaults for new records
            settings = self.company.company_settings
            if not self.payment_term and settings.default_customer_payment_term:
                self.payment_term = settings.default_customer_payment_term
            if not self.delivery_term and settings.default_customer_delivery_term:
                self.delivery_term = settings.default_customer_delivery_term
            if not self.language and settings.default_customer_language:
                self.language = settings.default_customer_language
            if not self.vat_zone and settings.default_customer_vat_zone:
                self.vat_zone = settings.default_customer_vat_zone
            if not self.category and settings.default_customer_category:
                self.category = settings.default_customer_category
            if not self.currency and settings.default_currency:
                self.currency = settings.default_currency
        super().save(*args, **kwargs)


class CustomerContact(CompanyFKRel, ContactBase):
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='contacts'
    )

    def save(self, *args, **kwargs):
        if self.is_primary:
            CustomerContact.objects.filter(customer=self.customer, company=self.company).update(is_primary=False)
        super().save(*args, **kwargs)

class CustomerAddress(CompanyFKRel, AddressBase):
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='addresses'
    )

    def save(self, *args, **kwargs):
        if self.is_default:
            CustomerAddress.objects.filter(customer=self.customer, company=self.company).update(is_default=False)
        super().save(*args, **kwargs)

class CreditLimitChange(CompanyFKRel):
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='credit_limit_changes'
    )
    old_limit = models.DecimalField(max_digits=15, decimal_places=2)
    new_limit = models.DecimalField(max_digits=15, decimal_places=2)
    changed_by = models.ForeignKey(
        'core.User',
        on_delete=models.PROTECT,
        related_name='credit_limit_changes'
    )
    reason = models.TextField()


class CustomerPriceList(CompanyFKRel):
    """
    Price lists for customers and customer categories.
    
    Priority order for price determination:
    1. Customer-specific price lists
    2. Customer category price lists
    3. Standard product/variant pricing
    """
    name = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    price_valid_from = models.DateField()
    price_valid_to = models.DateField(null=True, blank=True)
    currency = models.ForeignKey(
        'core.Currency',
        on_delete=models.PROTECT,
        related_name='price_lists'
    )
    
    # Existing customer relationship
    customers = models.ManyToManyField(
        Customer,
        related_name='price_lists',
        blank=True
    )
    
    # Customer category relationship
    customer_categories = models.ManyToManyField(
        CustomerCategory,
        related_name='price_lists',
        blank=True
    )

    class Meta:
        unique_together = ['company', 'name']


class PriceListItem(CompanyFKRel, PriceHistoryMixin):
    price_list = models.ForeignKey(
        CustomerPriceList,
        on_delete=models.CASCADE,
        related_name='items'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='price_list_items'
    )
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='price_list_items'
    )
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    min_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=1
    )

    def save(self, *args, **kwargs):
        creating = not self.pk
        super().save(*args, **kwargs)
        
        if self.price_list.customers.exists():
            # Create customer price history
            for customer in self.price_list.customers.all():
                self.create_price_history(
                    self,
                    {
                        'unit_price': self.unit_price,
                        'currency': self.price_list.currency,
                        'changed_by': kwargs.get('changed_by'),
                        'change_reason': 'Price list update'
                    },
                    CustomerPriceHistory,
                    customer=customer,
                    product=self.product,
                    variant=self.variant,
                    price_list=self.price_list
                )
        
        if self.price_list.customer_categories.exists():
            # Create category price history
            for category in self.price_list.customer_categories.all():
                self.create_price_history(
                    self,
                    {
                        'unit_price': self.unit_price,
                        'currency': self.price_list.currency,
                        'changed_by': kwargs.get('changed_by'),
                        'change_reason': 'Price list update'
                    },
                    CategoryPriceHistory,
                    category=category,
                    product=self.product,
                    variant=self.variant,
                    price_list=self.price_list
                )


class DiscountRule(CompanyFKRel):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    discount_type = models.CharField(
        max_length=40,
        choices=DiscountType.choices()
    )
    value = models.DecimalField(max_digits=15, decimal_places=2)
    valid_from = models.DateField()
    valid_to = models.DateField(null=True, blank=True)
    
    # Conditions
    min_order_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_order_value = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )
    max_uses = models.PositiveIntegerField(null=True, blank=True)
    max_uses_per_customer = models.PositiveIntegerField(null=True, blank=True)
    
    # Relations
    products = models.ManyToManyField(
        'products.Product',
        related_name='discount_rules',
        blank=True
    )
    product_categories = models.ManyToManyField(
        'products.ProductCategory',
        related_name='discount_rules',
        blank=True
    )
    customers = models.ManyToManyField(
        Customer,
        related_name='discount_rules',
        blank=True
    )
    customer_categories = models.ManyToManyField(
        CustomerCategory,
        related_name='discount_rules',
        blank=True
    )

    class Meta:
        ordering = ['-valid_from', 'name']

    def is_valid_for_order(self, order_value):
        if not self.is_active:
            return False
            
        today = timezone.now().date()
        if self.valid_from > today:
            return False
        if self.valid_to and self.valid_to < today:
            return False
            
        if self.min_order_value and order_value < self.min_order_value:
            return False
        if self.max_order_value and order_value > self.max_order_value:
            return False
            
        return True


class SalesOrder(CompanyFKRel, BaseTransaction):
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='sales_orders'
    )
    fulfilling_warehouse = models.ForeignKey(
        Warehouse,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='fulfilling_sales_orders'
    )
    requested_delivery_date = models.DateField()
    price_list = models.ForeignKey(
        CustomerPriceList,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='sales_orders'
    )
    status = models.CharField(
        max_length=30,
        choices=SalesOrderStatus.choices(),
        default=SalesOrderStatus.DRAFT.name
    )
    shipping_address = models.TextField()
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='sales_order'
    )

    class Meta:
        unique_together = ['company', 'number']


class SalesOrderLine(CompanyFKRel, BaseTransactionLine):
    sales_order = models.ForeignKey(
        SalesOrder,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    shipped_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0
    )


class Shipment(CompanyFKRel):
    sales_order = models.ForeignKey(
        'sales.SalesOrder',
        on_delete=models.PROTECT,
        related_name='shipments'
    )
    number = models.CharField(max_length=50)
    shipping_method = models.CharField(max_length=100)
    carrier = models.CharField(max_length=100)
    tracking_number = models.CharField(max_length=100)
    status = models.CharField(
        max_length=40,
        choices=ShipmentStatus.choices(),
        default=ShipmentStatus.PENDING.name
    )
    shipping_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    package_weight = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True
    )
    package_dimensions = models.JSONField(
        null=True,
        blank=True
    )  # {'length': x, 'width': y, 'height': z}
    shipped_by = models.ForeignKey(
        'core.User',
        on_delete=models.PROTECT,
        related_name='shipped_shipments'
    )
    shipped_at = models.DateTimeField(default=timezone.now)
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['company', 'number']

class ShipmentLine(CompanyFKRel):
    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    sales_order_line = models.ForeignKey(
        'sales.SalesOrderLine',
        on_delete=models.PROTECT,
        related_name='shipment_lines'
    )
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2
    )
    picked_from = models.ForeignKey(
        'inventory.Location',
        on_delete=models.PROTECT,
        related_name='shipment_lines'
    )
    lot_number = models.CharField(
        max_length=100,
        blank=True
    )
    serial_numbers = models.JSONField(
        null=True,
        blank=True
    )  # Array of serial numbers

class ShipmentTracking(CompanyFKRel):
    shipment = models.ForeignKey(
        Shipment,
        on_delete=models.CASCADE,
        related_name='tracking_events'
    )
    event_date = models.DateTimeField()
    event_type = models.CharField(max_length=50)
    location = models.CharField(max_length=255)
    description = models.TextField()
    carrier_reference = models.CharField(
        max_length=100,
        blank=True
    )


class ReturnOrder(CompanyFKRel):
    number = models.CharField(max_length=50)
    receiving_warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='receiving_returns'
    )
    sales_order = models.ForeignKey(
        'sales.SalesOrder',
        on_delete=models.PROTECT,
        related_name='returns'
    )
    status = models.CharField(
        max_length=40,
        choices=ReturnStatus.choices,
        default=ReturnStatus.PENDING.name
    )
    return_reason = models.CharField(max_length=255)
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(
        'core.User',
        on_delete=models.PROTECT,
        related_name='created_returns'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    received_date = models.DateTimeField(null=True, blank=True)
    completed_date = models.DateTimeField(null=True, blank=True)
    rma_number = models.CharField(max_length=50, unique=True)

    class Meta:
        unique_together = ['company', 'number']

class ReturnLine(CompanyFKRel):
    return_order = models.ForeignKey(
        ReturnOrder,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    sales_order_line = models.ForeignKey(
        'sales.SalesOrderLine',
        on_delete=models.PROTECT,
        related_name='return_lines'
    )
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2
    )
    received_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )
    status = models.CharField(
        max_length=40,
        choices=ReturnLineStatus.choices,
        default=ReturnLineStatus.PENDING.name
    )
    reason = models.CharField(max_length=255)
    condition_on_return = models.CharField(max_length=50)
    inspection_result = models.CharField(
        max_length=40,
        choices=InspectionResult.choices,
        null=True,
        blank=True
    )
    inspection_notes = models.TextField(blank=True)
    refund_amount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        null=True,
        blank=True
    )

class ReturnInspection(CompanyFKRel):
    return_line = models.ForeignKey(
        ReturnLine,
        on_delete=models.CASCADE,
        related_name='inspections'
    )
    inspector = models.ForeignKey(
        'core.User',
        on_delete=models.PROTECT,
        related_name='return_inspections'
    )
    inspection_date = models.DateTimeField(default=timezone.now)
    result = models.CharField(
        max_length=40,
        choices=InspectionResult.choices()
    )
    notes = models.TextField(blank=True)
    images = models.JSONField(null=True, blank=True)  # Array of image URLs

class ReturnCreditNote(CompanyFKRel):
    return_order = models.ForeignKey(
        ReturnOrder,
        on_delete=models.PROTECT,
        related_name='credit_notes'
    )
    number = models.CharField(max_length=50)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    issued_date = models.DateTimeField(default=timezone.now)
    status = models.CharField(
        max_length=40,
        choices=ReturnCreditNoteStatus.choices(),
        default=ReturnCreditNoteStatus.DRAFT.name
    )
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ['company', 'number']

class SalesInvoice(BaseInvoice, CompanyFKRel):
    customer = models.ForeignKey(
        Customer,
        on_delete=models.PROTECT,
        related_name='invoices'
    )
    sales_order = models.ForeignKey(
        SalesOrder,
        on_delete=models.PROTECT,
        related_name='invoices'
    )
    invoice_number = models.CharField(max_length=50)  # Our invoice number to customer
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='sales_invoice'
    )
    
    class Meta:
        unique_together = [
            ['company', 'number'],
            ['company', 'invoice_number']
        ]

class SalesInvoiceLine(BaseTransactionLine, CompanyFKRel):
    invoice = models.ForeignKey(
        SalesInvoice,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    sales_order_line = models.ForeignKey(
        SalesOrderLine,
        on_delete=models.PROTECT,
        related_name='invoice_lines'
    )
    shipment_line = models.ForeignKey(
        'sales.ShipmentLine',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='invoice_lines'
    )

class DiscountUsage(CompanyFKRel):
    discount_rule = models.ForeignKey(
        DiscountRule,
        on_delete=models.CASCADE,
        related_name='usages'
    )
    sales_order = models.ForeignKey(
        'sales.SalesOrder',
        on_delete=models.CASCADE,
        related_name='discount_usages'
    )
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='discount_usages'
    )
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    used_at = models.DateTimeField(auto_now_add=True)

class CustomerPriceHistory(CompanyFKRel, BasePriceHistory):
    """Track price history for customer-specific prices"""
    customer = models.ForeignKey(
        Customer,
        on_delete=models.CASCADE,
        related_name='price_history'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='customer_price_history'
    )
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='customer_price_history'
    )
    price_list = models.ForeignKey(
        CustomerPriceList,
        on_delete=models.CASCADE,
        related_name='price_history'
    )

    class Meta:
        ordering = ['-price_valid_from', '-id']
        indexes = [
            models.Index(fields=['company', 'customer', 'product', 'price_valid_from']),
            models.Index(fields=['company', 'price_list', 'price_valid_from'])
        ]

class CategoryPriceHistory(CompanyFKRel, BasePriceHistory):
    """Track price history for category-specific prices"""
    category = models.ForeignKey(
        CustomerCategory,
        on_delete=models.CASCADE,
        related_name='price_history'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='category_price_history'
    )
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='category_price_history'
    )
    price_list = models.ForeignKey(
        CustomerPriceList,
        on_delete=models.CASCADE,
        related_name='category_price_history'
    )

    class Meta:
        ordering = ['-price_valid_from', '-id']
        indexes = [
            models.Index(fields=['company', 'category', 'product', 'price_valid_from']),
            models.Index(fields=['company', 'price_list', 'price_valid_from'])
        ]