from django.contrib import admin
from .models import Location, Lot, InventoryItem, InventoryMovement, InventoryMovementLine, InventoryAdjustment, InventoryAdjustmentLine

class LocationInline(admin.TabularInline):
    model = Location
    extra = 0
    fields = ['name', 'number', 'is_receiving', 'is_shipping', 'is_storage', 'is_active']
    show_change_link = True

@admin.register(Location)
class LocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'number', 'warehouse', 'company', 'is_receiving', 'is_shipping', 'is_storage', 'is_active']
    list_filter = ['warehouse', 'company', 'is_receiving', 'is_shipping', 'is_storage', 'is_active', 'is_quality_control', 'is_returns']
    search_fields = ['name', 'number', 'warehouse__name', 'company__name']
    inlines = [LocationInline]
    fieldsets = (
        (None, {
            'fields': ('company', 'warehouse', 'name', 'number', 'parent')
        }),
        ('Location Type', {
            'fields': ('is_receiving', 'is_shipping', 'is_storage', 'is_quality_control', 'is_returns', 'is_active')
        }),
    )

class InventoryItemInline(admin.TabularInline):
    model = InventoryItem
    extra = 0
    fields = ['product', 'variant', 'quantity', 'reserved_quantity', 'cost_price']
    readonly_fields = ['reserved_quantity']
    show_change_link = True

@admin.register(Lot)
class LotAdmin(admin.ModelAdmin):
    list_display = ['number', 'product', 'variant', 'company', 'manufactured_date', 'expiry_date', 'is_blocked']
    list_filter = ['company', 'is_blocked', 'manufactured_date', 'expiry_date']
    search_fields = ['number', 'supplier_lot_number', 'product__name', 'company__name']
    inlines = [InventoryItemInline]
    date_hierarchy = 'manufactured_date'

@admin.register(InventoryItem)
class InventoryItemAdmin(admin.ModelAdmin):
    list_display = ['product', 'variant', 'lot', 'location', 'quantity', 'reserved_quantity', 'cost_price']
    list_filter = ['company', 'location', 'product']
    search_fields = ['product__name', 'lot__number', 'location__name']
    readonly_fields = ['reserved_quantity']
    raw_id_fields = ['product', 'variant', 'lot', 'location']

class InventoryMovementLineInline(admin.TabularInline):
    model = InventoryMovementLine
    extra = 0
    fields = ['product', 'variant', 'lot', 'quantity', 'cost_price']
    raw_id_fields = ['product', 'variant', 'lot']

@admin.register(InventoryMovement)
class InventoryMovementAdmin(admin.ModelAdmin):
    list_display = ['reference_number', 'movement_type', 'from_location', 'to_location', 'status', 'scheduled_date', 'completed_date']
    list_filter = ['company', 'movement_type', 'status', 'scheduled_date', 'completed_date']
    search_fields = ['reference_number', 'from_location__name', 'to_location__name']
    inlines = [InventoryMovementLineInline]
    date_hierarchy = 'scheduled_date'
    readonly_fields = ['completed_date']
    fieldsets = (
        (None, {
            'fields': ('company', 'reference_number', 'movement_type', 'status')
        }),
        ('Locations', {
            'fields': ('from_location', 'to_location')
        }),
        ('Scheduling', {
            'fields': ('scheduled_date', 'completed_date')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

class InventoryAdjustmentLineInline(admin.TabularInline):
    model = InventoryAdjustmentLine
    extra = 0
    fields = ['product', 'variant', 'lot', 'previous_quantity', 'new_quantity',  'cost_price']
    raw_id_fields = ['product', 'variant', 'lot']
    readonly_fields = ['adjustment_quantity', 'adjustment_value']

@admin.register(InventoryAdjustment)
class InventoryAdjustmentAdmin(admin.ModelAdmin):
    list_display = ['uuid', 'reference_number', 'adjustment_type', 'status', 'created_at', 'approved_by']
    list_filter = ['company', 'adjustment_type', 'status', 'created_at']
    search_fields = ['reference_number', 'created_by__username']
    inlines = [InventoryAdjustmentLineInline]
    date_hierarchy = 'created_at'
