from datetime import date, timedelta, datetime
from decimal import Decimal
from typing import Optional, List
from django.db import transaction
from django.utils import timezone
from ..models import SalesOrder, SalesInvoice, SalesInvoiceLine
from .base import BaseSalesService
from utils.enums import SalesOrderStatus, InvoiceStatus, TermType

class SalesInvoiceService(BaseSalesService):
    
    @transaction.atomic
    def create_invoice_from_order(
        self,
        order_id: int,
        invoice_date: date,
        use_shipped_quantities: bool = True
    ) -> SalesInvoice:
        """
        Create a sales invoice from a sales order.
        
        Args:
            order_id: Sales order ID
            invoice_date: Date of the invoice
            use_shipped_quantities: If True, create invoice based on shipped quantities,
                                  if False, use ordered quantities
        """
        order = SalesOrder.objects.get(
            company=self.company,
            id=order_id
        )
        
        # Validate order status
        if order.status == SalesOrderStatus.CANCELLED.name:
            raise ValueError("Cannot create invoice for cancelled order")
        
        if use_shipped_quantities and not order.shipments.exists():
            raise ValueError("No shipments found for this order")
            
        # Calculate due date based on payment terms
        if order.payment_term:
            if order.payment_term.type == TermType.NET.name:
                # Convert to date if we got a string
                if isinstance(invoice_date, str):
                    invoice_date = datetime.strptime(invoice_date, '%Y-%m-%d').date()
                due_date = invoice_date + timedelta(days=order.payment_term.days)
            else:
                due_date = invoice_date
        else:
            due_date = invoice_date
        
        # Create invoice
        invoice = SalesInvoice.objects.create(
            company=self.company,
            customer=order.customer,
            sales_order=order,
            number=self._generate_invoice_number(),
            invoice_number=self._generate_invoice_number(),  # Same as internal number for sales invoices
            invoice_date=invoice_date,
            due_date=due_date,
            currency=order.currency,
            currency_rate=order.currency_rate,
            payment_term=order.payment_term,
            delivery_term=order.delivery_term,
            delivery_method=order.delivery_method,
            our_reference=order.our_reference,
            your_reference=order.your_reference,
            notes=order.notes,
            order_date=order.order_date
        )
        
        # Create invoice lines
        total_net = Decimal('0')
        total_tax = Decimal('0')
        
        for order_line in order.lines.all():
            quantity = (
                order_line.shipped_quantity if use_shipped_quantities 
                else order_line.quantity
            )
            
            if quantity > 0:
                net_amount = quantity * (order_line.unit_price - order_line.discount)
                tax_amount = net_amount * (order_line.tax_rate / 100)
                
                SalesInvoiceLine.objects.create(
                    company=self.company,
                    invoice=invoice,
                    sales_order_line=order_line,
                    product=order_line.product,
                    variant=order_line.variant,
                    description=order_line.product.name,
                    quantity=quantity,
                    unit_price=order_line.unit_price,
                    tax_rate=order_line.tax_rate,
                    net_amount=net_amount,
                    tax_amount=tax_amount,
                    total_amount=net_amount + tax_amount
                )
                
                total_net += net_amount
                total_tax += tax_amount
        
        # Update invoice totals
        invoice.total_net_amount = total_net
        invoice.total_tax_amount = total_tax
        invoice.total_amount = total_net + total_tax
        invoice.save()
        
        return invoice

    def _generate_invoice_number(self) -> str:
        """Generate next invoice number sequence"""
        prefix = 'INV'
        current_year = timezone.now().year
        
        last_invoice = SalesInvoice.objects.filter(
            company=self.company,
            number__startswith=f'{prefix}-{current_year}'
        ).order_by('-number').first()
        
        if last_invoice:
            last_sequence = int(last_invoice.number.split('-')[-1])
            new_sequence = last_sequence + 1
        else:
            new_sequence = 1
        
        return f'{prefix}-{current_year}-{str(new_sequence).zfill(5)}'

    @transaction.atomic
    def send_invoice(self, invoice: SalesInvoice) -> SalesInvoice:
        """Mark invoice as sent to customer"""
        
        if invoice.status != InvoiceStatus.DRAFT.name:
            raise ValueError("Only draft invoices can be sent")
        
        invoice.status = InvoiceStatus.SENT.name
        invoice.save()
        return invoice

    @transaction.atomic
    def mark_as_paid(
        self,
        invoice: SalesInvoice,
        payment_date: date = None,
        payment_reference: str = None,
        partial: bool = False
    ) -> SalesInvoice:
        """Mark invoice as paid"""
        
        if invoice.status == InvoiceStatus.CANCELLED.name:
            raise ValueError("Cannot pay cancelled invoices")
        
        if invoice.status == InvoiceStatus.PAID.name:
            raise ValueError("Invoice is already paid")
        
        invoice.status = InvoiceStatus.PARTIAL_PAID.name if partial else InvoiceStatus.PAID.name
        invoice.final_paid_date = payment_date or timezone.now().date()
        invoice.payment_reference = payment_reference
        invoice.save()
        return invoice

    @transaction.atomic
    def cancel_invoice(self, invoice: SalesInvoice, reason: str = None) -> SalesInvoice:
        """Cancel an invoice"""
        
        if invoice.status in [InvoiceStatus.PAID.name, InvoiceStatus.PARTIAL_PAID.name]:
            raise ValueError("Cannot cancel paid invoices")
        
        invoice.status = InvoiceStatus.CANCELLED.name
        if reason:
            invoice.notes = f"{invoice.notes}\nCancellation reason: {reason}" if invoice.notes else f"Cancellation reason: {reason}"
        invoice.save()
        return invoice 