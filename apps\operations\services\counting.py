# operations/services/counting.py
from django.db import transaction
from typing import List, Dict
from apps.inventory.models import Location, InventoryItem
from .base import BaseOperationService
from datetime import timezone, datetime
from .adjustment import AdjustmentService

class CountingService(BaseOperationService):
    def initiate_count(
        self,
        location: Location,
        products: List[Dict] = None
    ) -> Dict:
        """Initialize an inventory count for specified products or entire location"""
        query = InventoryItem.objects.filter(
            company=self.company,
            location=location
        )
        
        if products:
            query = query.filter(product_id__in=[p['id'] for p in products])
            
        return {
            'location': location,
            'items': query.select_related('product', 'variant', 'lot').all()
        }

    @transaction.atomic
    def record_count(
        self,
        location: Location,
        counted_items: List[Dict]
    ) -> None:
        """Record inventory count results and create adjustments as needed"""
        adjustment_service = AdjustmentService(self.user, self.company)
        
        for item in counted_items:
            current_item = InventoryItem.objects.get(
                company=self.company,
                location=location,
                product_id=item['product_id'],
                variant_id=item.get('variant_id'),
                lot_id=item.get('lot_id')
            )
            
            if current_item.quantity != item['counted_quantity']:
                adjustment_service.adjust_inventory(
                    location=location,
                    product_id=item['product_id'],
                    variant_id=item.get('variant_id'),
                    lot_id=item.get('lot_id'),
                    quantity=item['counted_quantity'],
                    reason=f"Inventory count adjustment on {timezone.now()}"
                )

    def schedule_count(
        self,
        location: Location,
        scheduled_date: datetime,
        products: List[Dict] = None,
        count_type: str = 'FULL'
    ) -> Dict:
        """Schedule an inventory count"""
        return {
            'location': location,
            'scheduled_date': scheduled_date,
            'products': products,
            'count_type': count_type
        }

    def verify_count(
        self,
        location: Location,
        counted_items: List[Dict]
    ) -> Dict:
        """Verify count results against system quantities"""
        discrepancies = []
        for item in counted_items:
            current_item = InventoryItem.objects.get(
                company=self.company,
                location=location,
                product_id=item['product_id'],
                variant_id=item.get('variant_id'),
                lot_id=item.get('lot_id')
            )
            
            if current_item.quantity != item['counted_quantity']:
                discrepancies.append({
                    'product': current_item.product,
                    'system_quantity': current_item.quantity,
                    'counted_quantity': item['counted_quantity'],
                    'difference': item['counted_quantity'] - current_item.quantity
                })
        
        return discrepancies