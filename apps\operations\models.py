from django.db import models
from apps.base.models import CompanyFKRel 
from apps.core.models import Warehouse
from utils.enums import PickingTaskStatus


from apps.sales.models import SalesOrder
# Create your models here.
class PickingTask(CompanyFKRel):
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='picking_tasks'
    )
    sales_order = models.ForeignKey(
        SalesOrder,
        on_delete=models.PROTECT,
        related_name='picking_tasks'
    )
    assigned_to = models.ForeignKey(
        'core.Employee',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='assigned_pickings'
    )
    status = models.CharField(
        max_length=40,
        choices=PickingTaskStatus.choices(),
        default=PickingTaskStatus.PENDING.name
    )
    priority = models.IntegerField(default=0)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    notes = models.TextField(blank=True)
    number = models.CharField(max_length=40, unique=True)

class PickingTaskLine(CompanyFKRel):
    picking_task = models.ForeignKey(
        PickingTask,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    sales_order_line = models.ForeignKey(
        'sales.SalesOrderLine',
        on_delete=models.PROTECT,
        related_name='picking_lines'
    )
    location = models.ForeignKey(
        'inventory.Location',
        on_delete=models.PROTECT,
        related_name='picking_lines'
    )
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2
    )
    picked_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0
    )
    lot = models.ForeignKey(
        'inventory.Lot',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='picking_lines'
    )