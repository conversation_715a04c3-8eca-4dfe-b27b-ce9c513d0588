from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from apps.core.api.views import BaseViewSet
from utils.mixins import BulkOperationMixin
from apps.sales.models import (
    Customer, CustomerPriceList, DiscountRule, SalesOrder,
    Shipment, ReturnOrder, ReturnInspection, SalesInvoice,
    CustomerAddress, CustomerContact, CustomerCategory, CustomerPriceHistory
)
from apps.products.models import Product, ProductVariant
from apps.sales.api.serializers import (
    CustomerSerializer, CustomerPriceListSerializer,
    DiscountRuleSerializer, SalesOrderSerializer,
    ShipmentSerializer, ReturnOrderSerializer,
    CreditLimitChangeSerializer, SalesInvoiceSerializer,
    CustomerAddressSerializer, CustomerContactSerializer,
    CustomerCategorySerializer, PriceListItemSerializer,
    CustomerPriceHistorySerializer, SalesInvoiceFromOrderSerializer
)
from utils.pagination import CustomPageNumberPagination
from utils.enums import ShipmentStatus, SalesOrderStatus
from apps.sales.services.credit import CreditManagementService
from apps.sales.services.order import SalesOrderService
from apps.sales.services.shipping import ShippingService
from apps.sales.services.returns import ReturnsService
from django.utils import timezone
from rest_framework.exceptions import PermissionDenied
from apps.sales.services.invoice import SalesInvoiceService
from rest_framework import status
from django.db.models import Count, Sum, Q
from decimal import Decimal
from apps.sales.services.price import PricingService
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from apps.documents.services.pdf_generator import PDFGenerator
from traceback_with_variables import format_exc

class CustomerFilter(filters.FilterSet):
    min_credit_limit = filters.NumberFilter(
        field_name='credit_limit',
        lookup_expr='gte'
    )
    max_credit_limit = filters.NumberFilter(
        field_name='credit_limit',
        lookup_expr='lte'
    )

    class Meta:
        model = Customer
        fields = {
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
            'tax_number': ['exact'],
            'is_active': ['exact'],
            'payment_terms': ['exact', 'gte', 'lte']
        }

class CustomerViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    filterset_class = CustomerFilter
    search_fields = ['name', 'number', 'email']
    ordering_fields = ['name', 'number', 'credit_limit']
    lookup_field = 'uuid'

    class Meta: 
        select_related_fields = ['company']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['credit_service'] = CreditManagementService(
            self.request.user,
            context.get('company', None)
        )
        return context

    @action(detail=True, methods=['post'])
    def adjust_credit_limit(self, request, uuid=None):
        customer = self.get_object()
        serializer = CreditLimitChangeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        service = CreditManagementService(request.user, request.company)
        customer = service.adjust_credit_limit(
            customer.id,
            serializer.validated_data['new_limit'],
            serializer.validated_data['reason']
        )
        
        return Response(self.get_serializer(customer).data)

    @action(detail=True)
    def credit_history(self, request, uuid=None):
        customer = self.get_object()
        changes = customer.credit_limit_changes.all()
        serializer = CreditLimitChangeSerializer(changes, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def transactions(self, request, uuid=None):
        customer = self.get_object()
        invoices = SalesInvoice.objects.filter(customer=customer, company=request.company)
        orders = SalesOrder.objects.filter(customer=customer, company=request.company)

        invoice_serializer = SalesInvoiceSerializer(invoices, many=True)
        order_serializer = SalesOrderSerializer(orders, many=True)
        return Response({
            'invoices': invoice_serializer.data,
            'orders': order_serializer.data
        })

    @action(detail=True, methods=['get', 'post'])
    def addresses(self, request, uuid=None):
        customer = self.get_object()
        
        if request.method == 'GET':
            addresses = customer.addresses.all()
            paginator = CustomPageNumberPagination()
            paginated_addresses = paginator.paginate_queryset(addresses, request)
            serializer = CustomerAddressSerializer(
                paginated_addresses, 
                many=True,
                context=self.get_serializer_context()
            )
            return paginator.get_paginated_response(serializer.data)
        
        elif request.method == 'POST':
            serializer = CustomerAddressSerializer(
                data=request.data,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save(
                company=request.company,
                customer=customer
            )
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=True, methods=['put', 'delete'], url_path='addresses/(?P<address_uuid>[^/.]+)')
    def manage_address(self, request, uuid=None, address_uuid=None):
        customer = self.get_object()
        
        try:
            address = customer.addresses.get(id=address_uuid)
        except CustomerAddress.DoesNotExist:
            return Response(
                {'detail': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.method == 'PUT':
            serializer = CustomerAddressSerializer(
                address,
                data=request.data,
                partial=True,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        
        elif request.method == 'DELETE':
            address.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get', 'post'])
    def contacts(self, request, uuid=None):
        customer = self.get_object()
        
        if request.method == 'GET':
            contacts = customer.contacts.all()
            paginator = CustomPageNumberPagination()
            paginated_contacts = paginator.paginate_queryset(contacts, request)
            serializer = CustomerContactSerializer(
                paginated_contacts, 
                many=True,
                context=self.get_serializer_context()
            )
            return paginator.get_paginated_response(serializer.data)
        
        elif request.method == 'POST':
            serializer = CustomerContactSerializer(
                data=request.data,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save(
                company=request.company,
                customer=customer
            )
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=True, methods=['put', 'delete'], url_path='contacts/(?P<contact_uuid>[^/.]+)')
    def manage_contact(self, request, uuid=None, contact_uuid=None):
        customer = self.get_object()
        
        try:
            contact = customer.contacts.get(uuid=contact_uuid)
        except CustomerContact.DoesNotExist:
            return Response(
                {'detail': 'Contact not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.method == 'PUT':
            serializer = CustomerContactSerializer(
                contact,
                data=request.data,
                partial=True,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        
        elif request.method == 'DELETE':
            contact.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get'])
    def price(self, request, uuid=None):
        """Get price for a specific product for this customer"""
        customer = self.get_object()
        
        # Validate required parameters
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            product = Product.objects.get(id=product_id, company=request.company)
        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )
            
        # Optional parameters
        variant_id = request.query_params.get('variant_id')
        quantity = Decimal(request.query_params.get('quantity', '1'))
        currency = request.query_params.get('currency')
        
        variant = None
        if variant_id:
            try:
                variant = ProductVariant.objects.get(id=variant_id, product=product)
            except ProductVariant.DoesNotExist:
                return Response(
                    {'error': 'Variant not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        # Get price using pricing service
        pricing_service = PricingService(request.user, request.company)
        price_info = pricing_service.get_customer_price(
            customer=customer,
            product=product,
            variant=variant,
            quantity=quantity,
            currency=currency
        )
        
        return Response(price_info)

    @action(detail=True, url_path='price-history')
    def price_history(self, request, uuid=None):
        """Get price history for a customer"""
        customer = self.get_object()
        product_id = request.query_params.get('product_id')
        
        queryset = CustomerPriceHistory.objects.filter(
            customer=customer
        )
        
        if product_id:
            queryset = queryset.filter(product_id=product_id)
        
        serializer = CustomerPriceHistorySerializer(
            queryset,
            many=True,
            context=self.get_serializer_context()
        )
        return Response(serializer.data)

class PriceListViewSet(BaseViewSet, BulkOperationMixin):
    queryset = CustomerPriceList.objects.all()
    serializer_class = CustomerPriceListSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'is_active': ['exact'],
        'price_valid_from': ['exact', 'gte', 'lte'],
        'price_valid_to': ['exact', 'gte', 'lte']
    }
    search_fields = ['name']
    ordering_fields = ['name', 'price_valid_from', 'price_valid_to']

    class Meta:
        prefetch_related_fields = ['items', 'customers']

    @action(detail=True, methods=['get', 'post'])
    def items(self, request, uuid=None):
        """Manage items in a price list"""
        price_list = self.get_object()
        
        if request.method == 'GET':
            items = price_list.items.all()
            serializer = PriceListItemSerializer(
                items,
                many=True,
                context=self.get_serializer_context()
            )
            return Response(serializer.data)
            
        elif request.method == 'POST':
            # Check if we're receiving a single item or a list of items
            if isinstance(request.data, list):
                # Bulk creation
                serializer = PriceListItemSerializer(
                    data=[{**item, 'company': request.company.id} for item in request.data],
                    many=True,
                    context=self.get_serializer_context()
                )
            else:
                # Single item creation
                serializer = PriceListItemSerializer(
                    data={**request.data, 'company': request.company.id},
                    context=self.get_serializer_context()
                )
            
            serializer.is_valid(raise_exception=True)
            serializer.save(price_list=price_list)
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )

class DiscountRuleViewSet(BaseViewSet):
    queryset = DiscountRule.objects.all()
    serializer_class = DiscountRuleSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'discount_type': ['exact'],
        'valid_from': ['exact', 'gte', 'lte'],
        'valid_to': ['exact', 'gte', 'lte'],
        'is_active': ['exact'],
        'products': ['exact'],
        'product_categories': ['exact'],
        'customers': ['exact'],
        'customer_categories': ['exact']
    }
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'valid_from', 'valid_to', 'value']

    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.annotate(
            total_uses=Count('usages'),
            total_discount_amount=Sum('usages__amount')
        ).select_related(
            'company'
        ).prefetch_related(
            'products',
            'product_categories',
            'customers',
            'customer_categories'
        )

    @action(detail=False, methods=['get', 'post'])
    def product(self, request):
        """Manage discount rules for a specific product"""
        product_id = request.query_params.get('product_id') or request.data.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if request.method == 'GET':
            queryset = self.get_queryset().filter(products__id=product_id)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            discount = serializer.save(company=request.company)
            discount.products.add(product_id)
            return Response(
                self.get_serializer(discount).data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=False, methods=['get', 'post'])
    def customer(self, request):
        """Manage discount rules for a specific customer"""
        customer_id = request.query_params.get('customer_id') or request.data.get('customer_id')
        if not customer_id:
            return Response(
                {'error': 'customer_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if request.method == 'GET':
            queryset = self.get_queryset().filter(customers__id=customer_id)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            discount = serializer.save(company=request.company)
            discount.customers.add(customer_id)
            return Response(
                self.get_serializer(discount).data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=False, methods=['get', 'post'])
    def product_category(self, request):
        """Manage discount rules for a specific product category"""
        category_id = request.query_params.get('category_id') or request.data.get('category_id')
        if not category_id:
            return Response(
                {'error': 'category_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if request.method == 'GET':
            queryset = self.get_queryset().filter(product_categories__id=category_id)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            discount = serializer.save(company=request.company)
            discount.product_categories.add(category_id)
            return Response(
                self.get_serializer(discount).data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=False, methods=['get', 'post'])
    def customer_category(self, request):
        """Manage discount rules for a specific customer category"""
        category_id = request.query_params.get('category_id') or request.data.get('category_id')
        if not category_id:
            return Response(
                {'error': 'category_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if request.method == 'GET':
            queryset = self.get_queryset().filter(customer_categories__id=category_id)
            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            discount = serializer.save(company=request.company)
            discount.customer_categories.add(category_id)
            return Response(
                self.get_serializer(discount).data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=True, methods=['get'])
    def usage_stats(self, request, uuid=None):
        """Get detailed usage statistics for a discount rule"""
        discount = self.get_object()
        
        usage_by_customer = discount.usages.values(
            'customer__name'
        ).annotate(
            total_uses=Count('id'),
            total_amount=Sum('amount')
        )

        return Response({
            'total_uses': discount.usages.count(),
            'total_discount_amount': discount.usages.aggregate(
                total=Sum('amount')
            )['total'] or 0,
            'usage_by_customer': usage_by_customer,
            'active_days_remaining': (
                (discount.valid_to - timezone.now().date()).days 
                if discount.valid_to else None
            )
        })

class SalesOrderFilter(filters.FilterSet):
    min_total = filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='gte'
    )
    max_total = filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='lte'
    )
    order_date_from = filters.DateFilter(
        field_name='order_date',
        lookup_expr='gte'
    )
    order_date_to = filters.DateFilter(
        field_name='order_date',
        lookup_expr='lte'
    )

    class Meta:
        model = SalesOrder
        fields = {
            'number': ['exact', 'icontains'],
            'customer': ['exact'],
            'status': ['exact'],
            'requested_delivery_date': ['exact', 'gte', 'lte']
        }

class SalesOrderViewSet(BaseViewSet):
    queryset = SalesOrder.objects.all()
    serializer_class = SalesOrderSerializer
    filterset_class = SalesOrderFilter
    search_fields = ['number', 'customer__name', 'notes']
    ordering_fields = ['number', 'order_date', 'total_amount']
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = ['customer']
        prefetch_related_fields = ['lines']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['order_service'] = SalesOrderService(
            self.request.user,
            context.get('company', None)
        )
        return context

    @action(detail=True, methods=['put'])
    def confirm(self, request, uuid=None):
        order = self.get_object()
        service = SalesOrderService(request.user, request.company)
        try:
            order = service.confirm_order(order)
            return Response(self.get_serializer(order).data)
        except Exception as ex: 
            return Response({
                'error': str(ex),
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['put'])
    def start_shipment(self, request, uuid=None):
        """Start shipping process"""
        order = self.get_object()
        if order.status != SalesOrderStatus.PACKED.name:
            return Response({
                'error': 'Order must be packed before starting shipment',
            }, status=status.HTTP_400_BAD_REQUEST)
        service = SalesOrderService(request.user, request.company)
        try:
            order = service.start_shipment(order)
            return Response(self.get_serializer(order).data)
        except Exception as ex:
            return Response({
                'error': str(ex),
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['put'])
    def complete(self, request, uuid=None):
        """Mark order as completed"""
        order = self.get_object()
        service = SalesOrderService(request.user, request.company)
        try:
            order = service.complete_order(order)
            return Response(self.get_serializer(order).data)
        except Exception as ex:
            return Response({
                'error': str(ex),
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        """Cancel the order"""
        if not request.data.get('reason'):
            return Response(
                {'error': 'Cancellation reason is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        order = self.get_object()
        if order.status != SalesOrderStatus.DRAFT.name: 
            return Response(
                {'error': 'Only draft orders can be cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )

        service = SalesOrderService(request.user, request.company)
        order = service.cancel_order(
            order=order,
            reason=request.data['reason']
        )
        return Response(self.get_serializer(order).data)

    @action(detail=True, methods=['get'])
    def shipments(self, request, uuid=None):
        """List all shipments for this order"""
        order = self.get_object()
        shipments = Shipment.objects.filter(
            company=request.company,
            sales_order=order
        ).select_related(
            'shipped_by'
        ).prefetch_related(
            'lines',
            'lines__sales_order_line',
            'lines__picked_from'
        )
        paginator = CustomPageNumberPagination()
        paginated_shipments = paginator.paginate_queryset(shipments, request)
        serializer = ShipmentSerializer(
            paginated_shipments, 
            many=True,
            context=self.get_serializer_context()
        )
        return paginator.get_paginated_response(serializer.data)

class ShipmentViewSet(BaseViewSet):
    queryset = Shipment.objects.all()
    serializer_class = ShipmentSerializer
    filterset_fields = {
        'number': ['exact', 'icontains'],
        'sales_order': ['exact'],
        'status': ['exact'],
        'carrier': ['exact', 'icontains'],
        'shipped_at': ['exact', 'gte', 'lte']
    }
    search_fields = ['number', 'tracking_number']
    ordering_fields = ['number', 'shipped_at', 'status']
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = ['sales_order', 'shipped_by']
        prefetch_related_fields = ['lines', 'tracking_events']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['shipping_service'] = ShippingService(
            self.request.user,
            context.get('company', None)
        )
        return context

    @action(detail=False, methods=['post'])
    def plan_shipment(self, request):
        service = ShippingService(self.request.user, self.request.company)
        try:
            plan = service.plan_shipment(
                order_id=request.data['order_id'],
                shipping_method=request.data.get('shipping_method'),
                preferred_carrier=request.data.get('preferred_carrier')
            )
            return Response(plan)
        except Exception as ex:
            return Response({
                'error': str(ex),
            }, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def create_shipment(self, request):
        """Create shipment with carrier integration and inventory updates"""
        service = ShippingService(self.request.user, self.request.company)
        shipment = service.create_shipment(
            order_id=request.data['order_id'],
            shipping_method=request.data['shipping_method'],
            shipping_lines=request.data['lines'],
            carrier=request.data.get('carrier'),
            tracking_number=request.data.get('tracking_number')
        )
        return Response(self.get_serializer(shipment).data)

    @action(detail=True, methods=['post'])
    def update_tracking(self, request, uuid=None):
        shipment = self.get_object()
        if shipment.status in [ShipmentStatus.DELIVERED.name, ShipmentStatus.CANCELLED.name]:
            return Response({
                'error': 'Shipment is already delivered or cancelled',
            }, status=status.HTTP_400_BAD_REQUEST)
        service = ShippingService(self.request.user, self.request.company)
        updated_shipment = service.update_shipment_status(
            shipment.id,
            status=request.data['status'],
            tracking_info=request.data.get('tracking_info')
        )
        return Response(self.get_serializer(updated_shipment).data)

    @action(detail=True, methods=['get'])
    def tracking_history(self, request, uuid=None):
        shipment = self.get_object()
        tracking_events = shipment.tracking_events.all().order_by('-event_date')
        return Response({
            'shipment_number': shipment.number,
            'status': shipment.status,
            'tracking_number': shipment.tracking_number,
            'events': [{
                'date': event.event_date,
                'type': event.event_type,
                'location': event.location,
                'description': event.description,
                'carrier_reference': event.carrier_reference
            } for event in tracking_events]
        })

    @action(detail=True, methods=['post'])
    def mark_delivered(self, request, uuid=None):
        shipment = self.get_object()
        service = ShippingService(self.request.user, self.request.company)
        updated_shipment = service.update_shipment_status(
            shipment.id,
            status=ShipmentStatus.DELIVERED.name,
            tracking_info={
                'date': timezone.now(),
                'type': 'delivery',
                'location': request.data.get('delivery_location', ''),
                'description': 'Package delivered'
            }
        )
        return Response(self.get_serializer(updated_shipment).data)

class ReturnOrderFilter(filters.FilterSet):
    created_from = filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte'
    )
    created_to = filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte'
    )

    class Meta:
        model = ReturnOrder
        fields = {
            'number': ['exact', 'icontains'],
            'sales_order': ['exact'],
            'status': ['exact'],
            'rma_number': ['exact', 'icontains']
        }

class ReturnOrderViewSet(BaseViewSet):
    queryset = ReturnOrder.objects.all()
    serializer_class = ReturnOrderSerializer
    filterset_class = ReturnOrderFilter
    search_fields = ['number', 'rma_number', 'notes']
    ordering_fields = ['number', 'created_at', 'received_date']

    class Meta:
        select_related_fields = ['sales_order', 'created_by']
        prefetch_related_fields = ['lines']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['returns_service'] = ReturnsService(
            self.request.user,
            context.get('company', None)
        )
        return context

    @action(detail=True, methods=['post'])
    def process_receipt(self, request, uuid=None):
        return_order = self.get_object()
        service = ReturnsService(self.request.user, self.request.company)
        updated_return = service.process_return_receipt(
            return_order.id,
            received_lines=request.data['lines']
        )
        return Response(self.get_serializer(updated_return).data)

    @action(detail=True, methods=['post'])
    def process_inspection(self, request, uuid=None):
        return_order = self.get_object()
        service = ReturnsService(self.request.user, self.request.company)
        updated_return = service.process_quality_check(
            return_order.id,
            inspection_results=request.data['results']
        )
        return Response(self.get_serializer(updated_return).data)

    @action(detail=True, methods=['get'])
    def inspection_history(self, request, uuid=None):
        return_order = self.get_object()
        inspections = ReturnInspection.objects.filter(
            return_line__return_order=return_order
        ).select_related('inspector', 'return_line')
        
        return Response([{
            'date': inspection.inspection_date,
            'inspector': inspection.inspector.get_full_name(),
            'result': inspection.result,
            'notes': inspection.notes,
            'product': inspection.return_line.sales_order_line.product.name,
            'images': inspection.images
        } for inspection in inspections])

class SalesInvoiceViewSet(BaseViewSet):
    queryset = SalesInvoice.objects.all()
    serializer_class = SalesInvoiceSerializer
    filterset_fields = {
        'number': ['exact', 'icontains'],
        'invoice_number': ['exact', 'icontains'],
        'customer': ['exact'],
        'sales_order': ['exact'],
        'invoice_date': ['exact', 'gte', 'lte'],
        'due_date': ['exact', 'gte', 'lte'],
        'status': ['exact']
    }
    search_fields = ['number', 'invoice_number', 'notes']
    ordering_fields = ['invoice_date', 'due_date', 'total_amount']
    lookup_field = 'uuid'

    @action(detail=False, methods=['post'], serializer_class=SalesInvoiceFromOrderSerializer)
    def create_from_order(self, request):
        """Create invoice from sales order"""
        service = SalesInvoiceService(request.user, request.company)
        invoice = service.create_invoice_from_order(
            order_id=request.data['sales_order_id'],
            invoice_date=request.data['invoice_date'],
            use_shipped_quantities=request.data.get('use_shipped_quantities', True)
        )
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['post'])
    def send(self, request, uuid=None):
        """Mark invoice as sent"""
        invoice = self.get_object()
        service = SalesInvoiceService(request.user, request.company)
        invoice = service.send_invoice(invoice)
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['post'])
    def mark_as_paid(self, request, uuid=None):
        """Mark invoice as paid"""
        invoice = self.get_object()
        service = SalesInvoiceService(request.user, request.company)
        invoice = service.mark_as_paid(
            invoice=invoice,
            payment_date=request.data.get('payment_date', timezone.now().date()),
            payment_reference=request.data.get('payment_reference'),
            partial=request.data.get('partial', False)
        )
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, uuid=None):
        """Cancel the invoice"""
        if not request.data.get('reason'):
            return Response(
                {'error': 'Cancellation reason is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        invoice = self.get_object()
        service = SalesInvoiceService(request.user, request.company)
        invoice = service.cancel_invoice(
            invoice=invoice,
            reason=request.data['reason']
        )
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['get'])
    def pdf(self, request, uuid=None):
        """
        Generate a PDF for the sales invoice
        """
        try:
            invoice = self.get_object()
            
            # Generate the PDF
            pdf_content, filename = PDFGenerator.generate_sales_invoice_pdf(
                invoice=invoice,
                company=request.company
            )
            
            # Create the HTTP response with PDF content
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            return response
            
        except Exception as e:
            print(format_exc(e))
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class CustomerCategoryViewSet(BaseViewSet, BulkOperationMixin):
    queryset = CustomerCategory.objects.all()
    serializer_class = CustomerCategorySerializer
    search_fields = ['name', 'number', 'description']
    ordering_fields = ['name', 'number']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().annotate(
            customer_count=Count('customers')
        )

    @action(detail=False, methods=['get'])
    def root_categories(self, request):
        queryset = self.get_queryset().filter(parent__isnull=True)
        paginator = CustomPageNumberPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'])
    def tree(self, request, uuid=None):
        instance = self.get_object()
        paginator = CustomPageNumberPagination()
        descendants = instance.get_descendants(include_self=True)
        paginated_descendants = paginator.paginate_queryset(descendants, request)
        serializer = self.get_serializer(paginated_descendants, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True)
    def customers(self, request, uuid=None):
        """Get all customers in this category"""
        category = self.get_object()
        customers = category.customers.all()
        paginator = CustomPageNumberPagination()
        paginated_customers = paginator.paginate_queryset(customers, request)
        serializer = CustomerCategorySerializer(
            paginated_customers, 
            many=True,
            context=self.get_serializer_context()
        )
        return paginator.get_paginated_response(serializer.data)