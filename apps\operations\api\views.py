# operations/views.py
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from ..services.movement import MovementService
from ..services.adjustment import AdjustmentService
from ..services.counting import CountingService
from .serializers import (
    CountingInitiateSerializer, CountResultSerializer,
    PickingTaskSerializer, PickingTaskLineSerializer
)
from .filters import PickingTaskFilter
from apps.inventory.models import InventoryMovement
from utils.views import BaseServiceViewSet
from apps.operations.services.picking import PickingService
from apps.operations.models import PickingTask
from utils.views import BaseViewSet


class CountingViewSet(BaseServiceViewSet):
    service_class = CountingService

    def create(self, request):
        serializer = CountingInitiateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        service = self.get_service()
        try:
            count_data = service.schedule_count(
                location_id=serializer.validated_data['location_id'],
                scheduled_date=serializer.validated_data.get('scheduled_date'),
                products=serializer.validated_data.get('products'),
                count_type=serializer.validated_data['count_type']
            )
            return Response(count_data)
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def record_count(self, request):
        serializer = CountResultSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        service = CountingService(request.user, request.company)
        try:
            service.record_count(
                location_id=serializer.validated_data['location_id'],
                counted_items=serializer.validated_data['counted_items']
            )
            return Response(status=status.HTTP_200_OK)
        except ValueError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class PickingTaskViewSet(BaseViewSet):
    queryset = PickingTask.objects.all()
    serializer_class = PickingTaskSerializer
    filterset_fields = {
        'sales_order': ['exact'],
        'status': ['exact'],
        'assigned_to': ['exact']
    }

    @action(detail=True, methods=['post'])
    def assign_picker(self, request, pk=None):
        """Assign picker to task"""
        service = PickingService(request.user, request.company)
        task = service.assign_picker(
            picking_task_id=pk,
            employee_id=request.data['employee_id']
        )
        return Response(self.get_serializer(task).data)

    @action(detail=True, methods=['post'])
    def start_picking(self, request, pk=None):
        """Start the picking process"""
        service = PickingService(request.user, request.company)
        task = service.start_picking(pk)
        return Response(self.get_serializer(task).data)

    @action(detail=True, methods=['post'])
    def record_pick(self, request, pk=None):
        """Record picked quantity for a line"""
        service = PickingService(request.user, request.company)
        line = service.record_pick(
            picking_line_id=request.data['line_id'],
            picked_quantity=request.data['picked_quantity']
        )
        return Response(PickingTaskLineSerializer(line).data)

    @action(detail=True, methods=['get'])
    def picking_route(self, request, pk=None):
        """Get optimized picking route"""
        service = PickingService(request.user, request.company)
        route = service.get_picking_route(pk)
        return Response(route)