# sales/services/order.py
from decimal import Decimal
from datetime import date
from django.utils import timezone
from typing import Dict, List
from django.db import transaction
from django.db.models import F, Sum
from ..models import (
    SalesOrder, SalesOrderLine, Customer, Shipment, ShipmentLine
)
from apps.inventory.models import Location
from .price import PricingService
from apps.inventory.models import InventoryItem
from .base import BaseSalesService
from apps.core.models import User, Company
from utils.enums import SalesOrderStatus, InvoiceStatus
from apps.products.models import Product, ProductVariant

class SalesOrderService(BaseSalesService):
    def __init__(self, user: User, company: Company):
        super().__init__(user, company)
        self.pricing_service = PricingService(user, company)

    @transaction.atomic
    def create_order(
        self,
        customer_id: int,
        lines: List[Dict],
        requested_delivery_date: date,
        shipping_address: str,
        notes: str = None,
        currency: str = None,
        payment_term: str = None,
        delivery_term: str = None,
        currency_rate: Decimal = None,
        our_reference: str = None,
        your_reference: str = None,
        delivery_method: str = None
    ) -> SalesOrder:
        # Validate customer
        customer = Customer.objects.get(
            company=self.company,
            id=customer_id,
            is_active=True
        )
        
        # Check credit limit
        if not self._check_credit_limit(customer, lines):
            raise ValueError("Order exceeds customer credit limit")
        
        # Create order
        order = SalesOrder.objects.create(
            company=self.company,
            number=self._generate_order_number(),
            customer=customer,
            order_date=timezone.now().date(),
            requested_delivery_date=requested_delivery_date,
            shipping_address=shipping_address,
            notes=notes,
            currency=currency,
            payment_term=payment_term,
            delivery_term=delivery_term,
            currency_rate=currency_rate,
            our_reference=our_reference,
            your_reference=your_reference,
            delivery_method=delivery_method
        )
        
        # Process lines
        total_amount = Decimal('0')
        total_discount = Decimal('0')
        
        for line in lines:
            # Convert quantity to Decimal
            quantity = Decimal(str(line['quantity']))
            
            # Get product and variant objects
            product = Product.objects.get(
                company=self.company,
                id=line['product_id']
            )
            variant = None
            if line.get('variant_id'):
                variant = ProductVariant.objects.get(
                    company=self.company,
                    id=line['variant_id']
                )

            # Get pricing with actual objects
            price_info = self.pricing_service.get_customer_price(
                customer,
                product,
                variant,
                quantity,
                currency
            )
            
            # Create order line
            order_line = SalesOrderLine.objects.create(
                company=self.company,
                sales_order=order,
                product=product,
                variant=variant,
                quantity=quantity,  # Use converted quantity
                unit_price=price_info['base_price'],
                discount=price_info['discount_amount'],
                tax_rate=Decimal(str(line.get('tax_rate', '0')))
            )
            
            # Reserve stock
            self._reserve_stock(order_line)
            
            total_amount += price_info['base_price'] * quantity
            total_discount += price_info['discount_amount'] * quantity
        
        order.total_amount = total_amount
        order.total_discount = total_discount
        order.save()
        
        return order

    def _generate_order_number(self) -> str:
        """
        Generates SO number in format: SO-YYYY-XXXXX
        where XXXXX is a sequential number padded with zeros
        """
        current_year = timezone.now().year
        
        # Get last SO number for this year and company
        last_so = SalesOrder.objects.filter(
            company=self.company,
            number__startswith=f'SO-{current_year}'
        ).order_by('-number').first()
        
        if last_so:
            # Extract sequence number and increment
            last_sequence = int(last_so.number.split('-')[-1])
            new_sequence = last_sequence + 1
        else:
            new_sequence = 1
        
        return f'SO-{current_year}-{str(new_sequence).zfill(5)}'

    def confirm_order(self, order: SalesOrder) -> SalesOrder:
        """Confirm sales order and initiate fulfillment"""
        if order.status != SalesOrderStatus.DRAFT.name:
            raise ValueError("Only draft orders can be confirmed")
            
        if not self._validate_stock_availability(order):
            raise ValueError("Insufficient stock for order confirmation")
        
        order.status = SalesOrderStatus.CONFIRMED.name
        order.save()
        
        # Trigger picking process
        self._initiate_picking(order)
        
        return order

    def _check_credit_limit(
        self,
        customer: Customer,
        lines: List[Dict]
    ) -> bool:
        """Check if order would exceed customer credit limit"""
        if not customer.credit_limit:
            return True
            
        # Calculate current outstanding amount
        outstanding = SalesOrder.objects.filter(
            company=self.company,
            customer=customer,
            status__in=[SalesOrderStatus.CONFIRMED.name, SalesOrderStatus.PICKING.name, SalesOrderStatus.PACKED.name]
        ).aggregate(
            total=Sum('total_amount')
        )['total'] or Decimal('0')
        
        # Calculate new order amount
        new_order_amount = sum(
            self.pricing_service.get_customer_price(
                customer,
                line['product_id'],
                line.get('variant_id'),
                line['quantity']
            )['final_price'] * line['quantity']
            for line in lines
        )
        
        return (outstanding + new_order_amount) <= customer.credit_limit

    def _get_sales_order(self, order_id: int) -> SalesOrder:
        """Get sales order with validation"""
        return SalesOrder.objects.get(
            company=self.company,
            id=order_id
        )

    def _validate_stock_availability(self, order: SalesOrder) -> bool:
        """
        Validate if all order lines have sufficient stock
        Different from reservation check as it validates current state
        """
        for line in order.lines.all():
            available_stock = InventoryItem.objects.filter(
                company=self.company,
                product=line.product,
                variant=line.variant
            ).aggregate(
                total=Sum('quantity') - Sum('reserved_quantity')
            )['total'] or Decimal('0')
            
            if available_stock < line.quantity:
                return False
        return True

    def _initiate_picking(self, order: SalesOrder) -> None:
        """
        Initiates the picking process for an order
        Creates picking tasks and updates order status
        """
        from apps.operations.services.picking import PickingService  # Avoid circular import
        
        picking_service = PickingService(self.user, self.company)
        picking_service.create_picking_task(order)
        
        order.status = SalesOrderStatus.PICKING.name
        order.save()

    @transaction.atomic
    def _reserve_stock(self, order_line: SalesOrderLine) -> None:
        """Reserve stock for order line"""
        # Convert to Decimal for comparison
        required_quantity = Decimal(str(order_line.quantity))
        available_stock = Decimal('0')
        
        # Calculate available stock
        items = InventoryItem.objects.filter(
            company=self.company,
            product=order_line.product,
            variant=order_line.variant
        )
        for item in items:
            available_stock += Decimal(str(item.quantity)) - Decimal(str(item.reserved_quantity))
        
        if available_stock < required_quantity:
            raise ValueError(f"Insufficient stock. Available: {available_stock}, Required: {required_quantity}")
        
        # Update reservation using FIFO
        remaining = required_quantity
        for item in items.order_by('created_at'):
            available = Decimal(str(item.quantity)) - Decimal(str(item.reserved_quantity))
            if available <= 0:
                continue
                
            reserve_qty = min(remaining, available)
            item.reserved_quantity = F('reserved_quantity') + reserve_qty
            item.save()
            
            remaining -= reserve_qty
            if remaining <= 0:
                break

    @transaction.atomic
    def start_shipment(self, order: SalesOrder) -> SalesOrder:
        """Start shipping process for a packed order"""
        
        if order.status != SalesOrderStatus.PACKED.name:
            raise ValueError("Can only start shipping for packed orders")
            
        order.status = SalesOrderStatus.SHIPPING.name
        order.save()
        return order

    @transaction.atomic
    def complete_order(self, order: SalesOrder) -> SalesOrder:
        """Mark order as completed after all processing is done"""
        
        if order.status != SalesOrderStatus.SHIPPED.name:
            raise ValueError("Can only complete fully shipped orders")
            
        # Verify all invoices are paid
        unpaid_invoices = order.invoices.exclude(
            status__in=[InvoiceStatus.PAID.name, InvoiceStatus.CANCELLED.name]
        ).exists()
        
        if unpaid_invoices:
            raise ValueError("Cannot complete order with unpaid invoices")
            
        order.status = SalesOrderStatus.COMPLETED.name
        order.save()
        return order

    @transaction.atomic
    def cancel_order(self, order: SalesOrder, reason: str) -> SalesOrder:
        """Cancel an order"""
        
        if order.status != SalesOrderStatus.DRAFT.name:
            raise ValueError("Can only cancel orders that are drafts")
            
        # Release reserved stock
        for line in order.lines.all():
            self._release_stock(line)
            
        order.status = SalesOrderStatus.CANCELLED.name
        order.notes = f"{order.notes}\nCancellation reason: {reason}" if order.notes else f"Cancellation reason: {reason}"
        order.save()
        return order

    def _release_stock(self, order_line: SalesOrderLine) -> None:
        """Release reserved stock for cancelled order line"""
        items = InventoryItem.objects.filter(
            company=self.company,
            product=order_line.product,
            variant=order_line.variant,
            reserved_quantity__gt=0
        ).order_by('created_at')
        
        remaining = order_line.quantity
        for item in items:
            release_qty = min(remaining, item.reserved_quantity)
            item.reserved_quantity = F('reserved_quantity') - release_qty
            item.save()
            
            remaining -= release_qty
            if remaining <= 0:
                break