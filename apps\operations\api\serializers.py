from rest_framework import serializers
from utils.serializers import BaseModelSerializer
from apps.operations.models import PickingTask, PickingTaskLine

class PickingTaskLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='sales_order_line.product.name',
        read_only=True
    )
    location_name = serializers.CharField(
        source='location.name',
        read_only=True
    )
    remaining_quantity = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        read_only=True,
        source='get_remaining_quantity'
    )

    class Meta:
        model = PickingTaskLine
        fields = [
            'id', 'picking_task', 'sales_order_line',
            'product_name', 'location', 'location_name',
            'quantity', 'picked_quantity', 'remaining_quantity',
            'lot'
        ]
        read_only_fields = ['id', 'remaining_quantity']

class PickingTaskSerializer(BaseModelSerializer):
    lines = PickingTaskLineSerializer(many=True, read_only=True)
    assigned_to_name = serializers.Char<PERSON>ield(
        source='assigned_to.user.get_full_name',
        read_only=True,
        allow_null=True
    )
    sales_order_number = serializers.CharField(
        source='sales_order.number',
        read_only=True
    )
    completion_percentage = serializers.FloatField(read_only=True)

    class Meta:
        model = PickingTask
        fields = [
            'id', 'sales_order', 'sales_order_number',
            'assigned_to', 'assigned_to_name', 'status',
            'priority', 'started_at', 'completed_at',
            'notes', 'lines', 'completion_percentage'
        ]
        read_only_fields = [
            'id', 'started_at', 'completed_at',
            'completion_percentage'
        ]



class CountingInitiateSerializer(serializers.Serializer):
    location_id = serializers.IntegerField()
    scheduled_date = serializers.DateTimeField(required=False)
    products = serializers.ListField(
        child=serializers.DictField(),
        required=False
    )
    count_type = serializers.CharField(default='FULL')

class CountResultSerializer(serializers.Serializer):
    location_id = serializers.IntegerField()
    counted_items = serializers.ListField(
        child=serializers.DictField()
    )
