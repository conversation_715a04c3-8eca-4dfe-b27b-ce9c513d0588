from rest_framework import status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Prefetch, Count, Sum
from django_filters import rest_framework as filters
from apps.core.api.views import BaseViewSet, BulkOperationMixin
from apps.core.models import Employee
from apps.products.models import (
    Product, ProductCategory, ProductVariant, BundlePricing,
    ProductPricing, ProductBundle, BundleComponent, ProductType,
    PriceHistoryEntry, 
)
from .serializers import (
    ProductCategorySerializer, ProductSerializer,
    ProductVariantSerializer, ProductBundleSerializer,
    BundleComponentSerializer, ProductTypeSerializer,
    PriceHistoryEntrySerializer, ProductPricingSerializer,
    RemoveOrAddBundleComponentSerializer, ProductBundlePriceUpdateSerializer,
    ProductPriceUpdateListSerializer, ProductTranslationListSerializer,
    BundlePriceUpdateListSerializer, BundleComponentUpdateListSerializer
)
from .filters import (
    ProductCategoryFilter, ProductFilter,
    ProductVariantFilter, ProductBundleFilter,
    ProductTypeFilter
)
from apps.base.serializers import EmptyDataSerializer
from apps.inventory.models import InventoryItem, Location
from apps.core.models import Language, Translation
from apps.core.api.serializers import LanguageSerializer, TranslationSerializer
from django.contrib.contenttypes.models import ContentType
from utils.pagination import CustomPageNumberPagination
from drf_yasg.utils import swagger_auto_schema


class ProductTypeViewSet(BaseViewSet, BulkOperationMixin):
    swagger_tags = ['Products'] 
    queryset = ProductType.objects.all()
    serializer_class = ProductTypeSerializer
    filterset_class = ProductTypeFilter
    search_fields = ['name', 'number', 'description']
    ordering_fields = ['name', 'number']
    lookup_field = 'uuid'

class ProductCategoryViewSet(BaseViewSet, BulkOperationMixin):
    swagger_tags = ['Products'] 
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    filterset_class = ProductCategoryFilter
    search_fields = ['name', 'number', 'description']
    ordering_fields = ['name', 'number']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'parent', 'type'
        ).annotate(
            product_count=Count('products', distinct=True)
        )

    @action(detail=False, methods=['get'])
    def root_categories(self, request):
        queryset = self.get_queryset().filter(parent__isnull=True)
        paginator = CustomPageNumberPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'])
    def tree(self, request, uuid=None):
        instance = self.get_object()
        paginator = CustomPageNumberPagination()
        descendants = instance.get_descendants(include_self=True)
        paginated_descendants = paginator.paginate_queryset(descendants, request)
        serializer = self.get_serializer(paginated_descendants, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    @action(detail=True)
    def products(self, request, uuid=None):
        """Get all products in this category"""
        category = self.get_object()
        products = category.products.all()
        paginator = CustomPageNumberPagination()
        paginated_products = paginator.paginate_queryset(products, request)
        serializer = ProductCategorySerializer(
            paginated_products, 
            many=True,
            context=self.get_serializer_context()
        )
        return paginator.get_paginated_response(serializer.data)

class ProductViewSet(BaseViewSet, BulkOperationMixin):
    swagger_tags = ['Products']
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    filterset_class = ProductFilter
    search_fields = ['name', 'sku', 'barcode', 'description']
    ordering_fields = ['name', 'sku', 'created_at']
    lookup_field = 'uuid'

    def get_queryset(self):
        queryset = super().get_queryset().select_related(
            'category', 'type', 'unit_of_measure'
        ).prefetch_related(
            Prefetch('prices', queryset=ProductPricing.objects.select_related('currency')),
            Prefetch('variants', queryset=ProductVariant.objects.prefetch_related(
                'prices', 'inventory_items'
            ))
        ).annotate(
            total_variants=Count('variants', distinct=True),
            total_stock=Sum('inventory_items__quantity')
        )
        return queryset

    @action(detail=True, methods=['post'], serializer_class=EmptyDataSerializer)
    def toggle_active(self, request, uuid=None):
        instance = self.get_object()
        instance.is_active = not instance.is_active
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], serializer_class=ProductPriceUpdateListSerializer)
    def update_prices(self, request, uuid=None):
        instance = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        prices_data = serializer.validated_data.get('prices', [])
        # Get the employee instance for the current user and company
        employee = Employee.objects.get(
            user=request.user,
            company=request.company,
            is_active=True
        )
        
        for price_data in prices_data:
            price, created = ProductPricing.objects.update_or_create(
                company=request.company,
                product=instance,
                currency=price_data['currency'],
                defaults={
                    # Core prices
                    'base_selling_price': price_data.get('base_selling_price'),
                    'base_purchasing_price': price_data.get('base_purchasing_price'),
                    'actual_purchasing_price': price_data.get('actual_purchasing_price'),
                    'recommended_selling_price': price_data.get('recommended_selling_price'),
                    
                    # Cost and margin fields
                    'extra_cost_percentage': price_data.get('extra_cost_percentage', 0),
                    'extra_cost_fixed': price_data.get('extra_cost_fixed', 0),
                    'profit_margin_percentage': price_data.get('profit_margin_percentage', 0),
                    'contribution_margin_percentage': price_data.get('contribution_margin_percentage', 0),
                    
                    # Status and validity
                    'is_active': price_data.get('is_active', True),
                    'price_valid_from': price_data.get('price_valid_from'),
                    'price_valid_to': price_data.get('price_valid_to'),
                    
                    # Change tracking
                    'changed_by': employee,
                    'price_change_reason': price_data.get('price_change_reason'),
                }
            )
        
        instance.refresh_from_db()
        return_serializer = ProductSerializer(instance)
        return Response(return_serializer.data)
    
    @action(detail=True, methods=['get'], serializer_class=ProductTranslationListSerializer)
    def translations(self, request, uuid=None):
        product = self.get_object()
        content_type = ContentType.objects.get_for_model(Product)
        translations = Translation.objects.filter(
            company=request.company,
            content_type=content_type,
            object_id=product.id
        ).select_related('language')
        paginator = CustomPageNumberPagination()
        paginated_translations = paginator.paginate_queryset(translations, request)
        serializer = TranslationSerializer(paginated_translations, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['post'], serializer_class=ProductTranslationListSerializer)
    def update_translations(self, request, uuid=None):
        print("---------------------------------")
        product = self.get_object()
        print(product)
        content_type = ContentType.objects.get_for_model(Product)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        translations_data = serializer.validated_data.get('translations', [])
        result = []

        for trans_data in translations_data:
            translation, created = Translation.objects.update_or_create(
                company=request.company,
                content_type=content_type,
                object_id=product.id,
                language=trans_data['language'],
                defaults={
                    'name': trans_data['name'],
                    'description': trans_data.get('description', '')
                }
            )
            result.append(translation)

        return_serializer = TranslationSerializer(result, many=True)
        return Response(return_serializer.data, status=status.HTTP_201_CREATED)
        
    @action(detail=True, methods=['get'])
    def prices(self, request, uuid=None):
        product = self.get_object()
        prices = ProductPricing.objects.filter(
            company=request.company,
            product=product,
            is_active=True
        ).select_related('currency')
        
        # Initialize paginator
        paginator = CustomPageNumberPagination()
        paginated_prices = paginator.paginate_queryset(prices, request)
        
        serializer = ProductPricingSerializer(paginated_prices, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def variants(self, request, uuid=None):
        product = self.get_object()
        variants = ProductVariant.objects.filter(
            company=request.company,
            product=product
        )
        paginator = CustomPageNumberPagination()    
        paginated_variants = paginator.paginate_queryset(variants, request)
        serializer = ProductVariantSerializer(paginated_variants, many=True)
        return paginator.get_paginated_response(serializer.data)


    @action(detail=True, methods=['get'])
    def price_history(self, request, uuid=None):
        product = self.get_object()
        prices = PriceHistoryEntry.objects.filter(
            company=request.company,
            product=product
        )
        paginator = CustomPageNumberPagination()
        paginated_prices = paginator.paginate_queryset(prices, request)
        serializer = PriceHistoryEntrySerializer(paginated_prices, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def inventory_by_location(self, request, uuid=None):
        product = self.get_object()
        
        # Get all inventory items for this product
        inventory_items = InventoryItem.objects.filter(
            company=request.company,
            product=product
        ).select_related('location').order_by('location__name')

        # Build the response data
        data = []
        for item in inventory_items:
            data.append({
                'location_id': item.location.id,
                'location_name': item.location.name,
                'in_stock': float(item.quantity),  # I lager
                'in_orders': float(item.reserved_quantity),  # I order
                'available': float(item.quantity - item.reserved_quantity),  # Tillgängligt
                'ordered': 0,  # Beställt - You might need to link this to purchase orders
                'min_stock_level': float(product.min_stock_level),  # Min. lageranatal
                'min_order_quantity': float(product.min_order_quantity) if product.min_order_quantity else 0.00,  # Min. beställningsantal
            })

        # If you want to show locations even with no inventory
        all_locations = Location.objects.filter(
            company=request.company,
            is_active=True
        ).exclude(
            id__in=[item['location_id'] for item in data]
        )

        # Add empty records for locations without inventory
        for location in all_locations:
            data.append({
                'location_id': location.id,
                'location_name': location.name,
                'in_stock': 0.00,
                'in_orders': 0.00,
                'available': 0.00,
                'ordered': 0.00,
                'min_stock_level': float(product.min_stock_level),
                'min_order_quantity': float(product.min_order_quantity) if product.min_order_quantity else 0.00,
            })

        paginator = CustomPageNumberPagination()
        paginated_data = paginator.paginate_queryset(data, request)

        return paginator.get_paginated_response(paginated_data)

class ProductVariantViewSet(BaseViewSet, BulkOperationMixin):
    swagger_tags = ['Products'] 
    queryset = ProductVariant.objects.all()
    serializer_class = ProductVariantSerializer
    filterset_class = ProductVariantFilter
    search_fields = ['name', 'sku_suffix', 'barcode']
    ordering_fields = ['name', 'sku_suffix', 'created_at']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'product'
        ).prefetch_related(
            Prefetch('prices', queryset=ProductPricing.objects.select_related('currency'))
        ).annotate(
            total_stock=Sum('inventory_items__quantity')
        )

    @action(detail=True, methods=['post'], serializer_class=EmptyDataSerializer)
    def toggle_active(self, request, uuid=None):
        instance = self.get_object()
        instance.is_active = not instance.is_active
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], serializer_class=ProductPriceUpdateListSerializer)
    def update_prices(self, request, uuid=None):
        instance = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        prices_data = serializer.validated_data.get('prices', [])
        employee = Employee.objects.get(
            user=request.user,
            company=request.company,
            is_active=True
        )
        
        for price_data in prices_data:
            price, created = ProductPricing.objects.update_or_create(
                company=request.company,
                product=instance,
                currency_id=price_data['currency'],
                defaults={
                    # Core prices
                    'base_selling_price': price_data['base_selling_price'],
                    'base_purchasing_price': price_data['base_purchasing_price'],
                    'actual_purchasing_price': price_data['actual_purchasing_price'],
                    'recommended_selling_price': price_data.get('recommended_selling_price'),
                    
                    # Cost and margin fields
                    'extra_cost_percentage': price_data.get('extra_cost_percentage', 0),
                    'extra_cost_fixed': price_data.get('extra_cost_fixed', 0),
                    'profit_margin_percentage': price_data.get('profit_margin_percentage', 0),
                    'contribution_margin_percentage': price_data.get('contribution_margin_percentage', 0),
                    
                    # Status and validity
                    'is_active': price_data.get('is_active', True),
                    'price_valid_from': price_data.get('price_valid_from'),
                    'price_valid_to': price_data.get('price_valid_to'),
                    
                    # Change tracking
                    'changed_by': employee,
                    'price_change_reason': price_data.get('price_change_reason'),
                }
            )
        
        instance.refresh_from_db()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def price_history(self, request, uuid=None):
        variant = self.get_object()
        prices = PriceHistoryEntry.objects.filter(
            company=request.company,
            variant=variant
        )
        paginator = CustomPageNumberPagination()
        paginated_prices = paginator.paginate_queryset(prices, request)
        serializer = PriceHistoryEntrySerializer(paginated_prices, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'])
    def prices(self, request, uuid=None):
        variant = self.get_object()
        prices = ProductPricing.objects.filter(
            company=request.company,
            variant=variant
        )
        paginator = CustomPageNumberPagination()
        paginated_prices = paginator.paginate_queryset(prices, request)
        serializer = ProductPricingSerializer(paginated_prices, many=True)
        return paginator.get_paginated_response(serializer.data)

class ProductBundleViewSet(BaseViewSet):
    swagger_tags = ['Products'] 
    queryset = ProductBundle.objects.all()
    serializer_class = ProductBundleSerializer
    filterset_class = ProductBundleFilter
    search_fields = ['name', 'number', 'sku', 'description']
    ordering_fields = ['name', 'sku', 'created_at']
    lookup_field = 'uuid'
    
    def get_queryset(self):
        return super().get_queryset().prefetch_related(
            'components__product',
            'components__variant',
            Prefetch('prices', queryset=BundlePricing.objects.select_related('currency'))
        ).annotate(
            total_components=Count('components', distinct=True)
        )
    
    @action(detail=True, methods=['post'], serializer_class=EmptyDataSerializer)
    def toggle_active(self, request, uuid=None):
        instance = self.get_object()
        instance.is_active = not instance.is_active
        instance.save()
        serializer = self.get_serializer(instance)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def add_component(self, request, uuid=None):
        bundle = self.get_object()
        serializer = BundleComponentSerializer(data={
            **request.data,
            'bundle': bundle.id  # Make sure bundle ID is included in the data
        }, context={'request': request})
        
        serializer.is_valid(raise_exception=True)
        serializer.save(company=request.company)
        
        bundle.refresh_from_db()
        bundle_serializer = self.get_serializer(bundle)
        return Response(bundle_serializer.data)
    
    @action(detail=True, methods=['post'], serializer_class=RemoveOrAddBundleComponentSerializer)
    def remove_component(self, request, uuid=None):
        bundle = self.get_object()
        component = request.data.get('component')
        try:
            component = bundle.components.get(id=component.id)
            component.delete()
            bundle.refresh_from_db()
            serializer = self.get_serializer(bundle)
            return Response(serializer.data)
        except BundleComponent.DoesNotExist:
            return Response(
                {'detail': 'Component not found'},
                status=status.HTTP_404_NOT_FOUND
            )
    
    @action(detail=True, methods=['post'], serializer_class=BundlePriceUpdateListSerializer)
    def update_prices(self, request, uuid=None):
        instance = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        prices_data = serializer.validated_data.get('prices', [])
        employee = Employee.objects.get(
            user=request.user,
            company=request.company,
            is_active=True
        )
        
        for price_data in prices_data:
            price, created = BundlePricing.objects.update_or_create(
                company=request.company,
                bundle=instance,
                currency=price_data['currency'],
                defaults={
                    'base_selling_price': price_data['base_selling_price'],
                    'cost_price': price_data.get('cost_price'),
                    'is_active': price_data.get('is_active', True),
                }
            )
        
        instance.refresh_from_db()
        return_serializer = ProductBundleSerializer(instance)
        return Response(return_serializer.data)

    @action(detail=True, methods=['post'], serializer_class=BundleComponentUpdateListSerializer)
    def update_components(self, request, uuid=None):
        """
        Update multiple bundle components in a single request.
        Can handle adding new components, updating existing ones.
        """
        bundle = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        components_data = serializer.validated_data.get('components', [])
        
        # Keep track of processed components
        processed_components = []
        
        for component_data in components_data:
            # Check if this is an update to an existing component
            component_id = component_data.get('id')
            
            if component_id:
                # Update existing component
                try:
                    component = bundle.components.get(id=component_id)
                    # Update fields
                    if 'product' in component_data and component_data['product']:
                        component.product = component_data['product']
                        component.variant = None
                    elif 'variant' in component_data and component_data['variant']:
                        component.variant = component_data['variant']
                        component.product = None
                    
                    component.quantity = component_data['quantity']
                    component.is_optional = component_data.get('is_optional', False)
                    component.save()
                    processed_components.append(component.id)
                except BundleComponent.DoesNotExist:
                    return Response(
                        {'detail': f'Component with id {component_id} not found'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                # Create new component
                defaults = {
                    'company': request.company,
                    'bundle': bundle,
                    'quantity': component_data['quantity'],
                    'is_optional': component_data.get('is_optional', False)
                }
                
                if 'product' in component_data and component_data['product']:
                    defaults['product'] = component_data['product']
                elif 'variant' in component_data and component_data['variant']:
                    defaults['variant'] = component_data['variant']
                
                component = BundleComponent.objects.create(**defaults)
                processed_components.append(component.id)
        
        # Optional: Remove components not in the list if you want to fully replace
        # bundle.components.exclude(id__in=processed_components).delete()
        
        bundle.refresh_from_db()
        return_serializer = ProductBundleSerializer(bundle)
        return Response(return_serializer.data)


# products/views.py

class PriceHistoryViewSet(BaseViewSet):
    swagger_tags = ['Products'] 
    serializer_class = PriceHistoryEntrySerializer
    filterset_fields = ['product', 'variant', 'currency', 'change_type']
    search_fields = ['change_reason']
    ordering_fields = ['changed_at', 'base_selling_price']
    ordering = ['-changed_at']
    lookup_field = 'uuid'

    def get_queryset(self):
        if hasattr(self.request, 'company'):
            return PriceHistoryEntry.objects.filter(
                company=self.request.company
            ).select_related(
                'product', 'variant', 'currency', 'changed_by'
            )
        return PriceHistoryEntry.objects.none()

    @action(detail=False)
    def product_history(self, request):
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        queryset = self.get_queryset().filter(product_id=product_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False)
    def variant_history(self, request):
        variant_id = request.query_params.get('variant_id')
        if not variant_id:
            return Response(
                {'error': 'variant_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
            
        queryset = self.get_queryset().filter(variant_id=variant_id)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    

class PriceViewSet(BaseViewSet):
    swagger_tags = ['Products'] 
    queryset = ProductPricing.objects.all()
    serializer_class = ProductPricingSerializer
    filterset_fields = ['product', 'variant', 'currency']
    search_fields = ['product__name', 'product__sku', 'variant__name', 'variant__sku', 'currency__currency']
    ordering_fields = ['base_selling_price']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'product', 'variant', 'currency', 'changed_by'
        )
    

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
