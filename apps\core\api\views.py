from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from utils.views import BaseViewSet
from utils.mixins import BulkOperationMixin
from django.db.models import Count, Q
from apps.core.models import (
    Company, User, Employee, Warehouse,
    CompanySetting, AuditLog, UnitOfMeasure,
    Language, Translation, NumberSequence, CustomField,
    Currency, PaymentTerm, DeliveryTerm, DeliveryMethod, ActiveCompanySession
)
from apps.authentication.api.serializers import PasswordChangeSerializer
from .serializers import (
    CompanySerializer, UserSerializer, EmployeeSerializer,
    WarehouseSerializer, CompanySettingSerializer,
    AuditLogSerializer, UnitOfMeasureSerializer, 
    LanguageSerializer, TranslationSerializer, NumberSequenceSerializer, 
    CustomFieldSerializer, CurrencySerializer, PaymentTermSerializer,
    DeliveryTermSerializer, LanguageSerializer, DeliveryMethodSerializer,
    UserMeSerializer
)
from .filters import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>, Employee<PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON>er, <PERSON>tLog<PERSON>ilter,
    CompanyFilter
)
from apps.core.services.audit import AuditLogService
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType  
from rest_framework import status, viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import ValidationError, PermissionDenied
from drf_yasg.utils import swagger_auto_schema
from rest_framework.views import APIView


class CompanyViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer
    filterset_class = CompanyFilter
    search_fields = ['name', 'registration_number', 'email']
    ordering_fields = ['name', 'created_at']
    http_method_names = ['get', 'put', 'patch', 'head', 'options']
    
    class Meta: 
        select_related_fields = ['company_settings']

    def get_queryset(self):
        user = self.request.user
        # For list view, show all companies the user has access to
        queryset = Company.objects.filter(
            employees__company_employee__user=user,
            employees__company_employee__is_active=True
        ).distinct()
        
        return queryset.annotate(
            employee_count=Count('employees', filter=Q(employees__is_active=True))
        )

    def get_object(self):
        obj = super().get_object()
        # Check if user has access to this company
        if not obj.employees.filter(
            company_employee__user=self.request.user,
            company_employee__is_active=True
        ).exists():
            raise PermissionDenied("You do not have access to this company")
        return obj

    @action(detail=True, methods=['get', 'put', 'patch'])
    def company_settings(self, request, pk=None):
        company = self.get_object()
        settings = company.company_settings
        if request.method == 'GET':
            serializer = CompanySettingSerializer(settings)
            return Response(serializer.data)
        
        serializer = CompanySettingSerializer(
            settings,
            data=request.data,
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_settings(self, request, pk=None):
        company = self.get_object()
        settings = company.company_settings
        serializer = CompanySettingSerializer(
            settings,
            data=request.data,
            partial=True
        )
        serializer.is_valid(raise_exception=True)
        serializer.save()
        return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        
        # Add company to context for nested serializers
        context = self.get_serializer_context()
        context['company'] = instance
        
        serializer = self.get_serializer(
            instance,
            data=request.data,
            partial=partial,
            context=context
        )
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)



class UserViewSet(BaseViewSet, BulkOperationMixin):
    queryset = User.objects.all()
    serializer_class = UserSerializer
    filterset_class = UserFilter
    search_fields = ['email', 'first_name', 'last_name']
    ordering_fields = ['email', 'created_at']

    class Meta: 
        select_related_fields = ['company']

    @action(detail=True, methods=['post'])
    def set_password(self, request, pk=None):
        user = self.get_object()
        serializer = PasswordChangeSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        user.set_password(serializer.validated_data['password'])
        user.is_verified = True
        user.save()
        return Response({'status': 'password set'})

class EmployeeViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Employee.objects.all()
    serializer_class = EmployeeSerializer
    filterset_class = EmployeeFilter
    search_fields = ['user__email', 'user__first_name']
    ordering_fields = ['joined_at', 'role']
    http_method_names = ['get', 'patch', 'put', 'delete', 'head', 'options']

    class Meta: 
        select_related_fields = ['company']

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        employee = self.get_object()
        employee.is_active = False
        employee.left_at = timezone.now()
        employee.save()
        return Response(self.get_serializer(employee).data)


class WarehouseViewSet(BaseViewSet, BulkOperationMixin):
    tags = ['Warehouses']
    queryset = Warehouse.objects.all()
    serializer_class = WarehouseSerializer
    filterset_class = WarehouseFilter
    search_fields = ['name', 'number', 'address']
    ordering_fields = ['name', 'number', 'created_at']

    class Meta: 
        select_related_fields = ['company']

    def get_queryset(self):
        return super().get_queryset().annotate(
            location_count=Count('locations', filter=Q(locations__is_active=True))
        )



class AuditLogViewSet(BaseViewSet):
    queryset = AuditLog.objects.all()
    serializer_class = AuditLogSerializer
    filterset_class = AuditLogFilter
    ordering_fields = ['created_at']
    http_method_names = ['get']

    

    class Meta:
        select_related_fields = ['user', 'content_type']


class UnitOfMeasureViewSet(BaseViewSet, BulkOperationMixin):
    queryset = UnitOfMeasure.objects.all()
    serializer_class = UnitOfMeasureSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'abbreviation': ['exact', 'icontains'],
        'is_active': ['exact']
    }
    search_fields = ['name', 'abbreviation']
    ordering_fields = ['name', 'abbreviation']

    class Meta: 
        select_related_fields = ['base_unit']

    @action(detail=True, methods=['get', 'post'])
    def translations(self, request, pk=None):
        unit_of_measure = self.get_object()
        content_type = ContentType.objects.get_for_model(UnitOfMeasure)

        if request.method == 'GET':
            translations = Translation.objects.filter(
                company=request.company,
                content_type=content_type,
                object_id=unit_of_measure.id
            ).select_related('language')
            
            serializer = TranslationSerializer(translations, many=True)
            return Response(serializer.data)

        elif request.method == 'POST':
            translations_data = request.data.get('translations', [])
            result = []

            for trans_data in translations_data:
                translation, created = Translation.objects.update_or_create(
                    company=request.company,
                    content_type=content_type,
                    object_id=unit_of_measure.id,
                    language_id=trans_data['language'],
                    defaults={
                        'name': trans_data['name'],
                        'description': trans_data.get('description', '')
                    }
                )
                result.append(translation)

            serializer = TranslationSerializer(result, many=True)
            return Response(serializer.data, status=status.HTTP_201_CREATED)


class CurrencyViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Currency.objects.all()
    serializer_class = CurrencySerializer
    filterset_fields = {
        'currency': ['exact', 'icontains'],
        'is_active': ['exact']
    }

class LanguageViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Language.objects.all()
    serializer_class = LanguageSerializer
    filterset_fields = {
        'language': ['exact', 'icontains'],
        'is_active': ['exact']
    }


class PaymentTermViewSet(BaseViewSet, BulkOperationMixin):
    queryset = PaymentTerm.objects.all()
    serializer_class = PaymentTermSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'type': ['exact'],
        'days': ['exact'],
        'is_active': ['exact']
    }
    @action(detail=True, methods=['get', 'post'])
    def translations(self, request, pk=None):
        payment_term = self.get_object()
        content_type = ContentType.objects.get_for_model(PaymentTerm)

        if request.method == 'GET':
            translations = Translation.objects.filter(
                company=request.company,
                content_type=content_type,
                object_id=payment_term.id
            ).select_related('language')

            serializer = TranslationSerializer(translations, many=True)
            return Response(serializer.data)
        
        elif request.method == 'POST':
            translations_data = request.data.get('translations', [])
            result = []

            for trans_data in translations_data:
                translation, created = Translation.objects.update_or_create(
                    company=request.company,
                    content_type=content_type,
                    object_id=payment_term.id,
                    language_id=trans_data['language'],
                    defaults={
                        'name': trans_data['name'],
                        'description': trans_data.get('description', '')
                    }
                )
                result.append(translation)  

            serializer = TranslationSerializer(result, many=True)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

class DeliveryTermViewSet(BaseViewSet, BulkOperationMixin):
    queryset = DeliveryTerm.objects.all()
    serializer_class = DeliveryTermSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'is_active': ['exact']
    }

    @action(detail=True, methods=['get', 'post'])
    def translations(self, request, pk=None):
        delivery_term = self.get_object()
        content_type = ContentType.objects.get_for_model(DeliveryTerm)

        if request.method == 'GET':
            translations = Translation.objects.filter(
                company=request.company,
                content_type=content_type,
                object_id=delivery_term.id
            ).select_related('language')

            serializer = TranslationSerializer(translations, many=True)
            return Response(serializer.data)
        
        elif request.method == 'POST':
            translations_data = request.data.get('translations', [])
            result = []

            for trans_data in translations_data:
                translation, created = Translation.objects.update_or_create(
                    company=request.company,
                    content_type=content_type,
                    object_id=delivery_term.id,
                    language_id=trans_data['language'],
                    defaults={
                        'name': trans_data['name'],
                        'description': trans_data.get('description', '')
                    }
                )
                result.append(translation)

            serializer = TranslationSerializer(result, many=True)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

class DeliveryMethodViewSet(BaseViewSet, BulkOperationMixin):
    queryset = DeliveryMethod.objects.all()
    serializer_class = DeliveryMethodSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'is_active': ['exact']
    }

    @action(detail=True, methods=['get', 'post'])
    def translations(self, request, pk=None):
        delivery_method = self.get_object()
        content_type = ContentType.objects.get_for_model(DeliveryMethod)

        if request.method == 'GET':
            translations = Translation.objects.filter(
                company=request.company,
                content_type=content_type,
                object_id=delivery_method.id
            ).select_related('language')

            serializer = TranslationSerializer(translations, many=True)
            return Response(serializer.data)
        
        elif request.method == 'POST':
            translations_data = request.data.get('translations', [])
            result = []

            for trans_data in translations_data:
                translation, created = Translation.objects.update_or_create(
                    company=request.company,
                    content_type=content_type,
                    object_id=delivery_method.id,
                    language_id=trans_data['language'],
                    defaults={
                        'name': trans_data['name'],
                        'description': trans_data.get('description', '')
                    }
                )
                result.append(translation)

            serializer = TranslationSerializer(result, many=True)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

class NumberSequenceViewSet(viewsets.ModelViewSet):
    serializer_class = NumberSequenceSerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        if hasattr(self.request, 'company'):
            return NumberSequence.objects.filter(
                company=self.request.company
            )
        return NumberSequence.objects.none()

    def perform_create(self, serializer):
        if hasattr(self.request, 'company'):
            serializer.save(company=self.request.company)
        else:
            raise ValidationError("Company is required")

class CustomFieldViewSet(BaseViewSet):
    serializer_class = CustomFieldSerializer
    filterset_fields = {
        'name': ['exact', 'icontains'],
        'field_type': ['exact'],
        'model_type': ['exact'],
        'is_required': ['exact'],
        'is_active': ['exact']
    }
    search_fields = ['name', 'description']
    ordering_fields = ['order', 'name', 'field_type', 'model_type']
    
    def get_queryset(self):
        return CustomField.objects.filter(
            company=self.request.company
        ).order_by('order', 'name')

    def perform_create(self, serializer):
        serializer.save(company=self.request.company)


class TranslationViewSet(BaseViewSet):
    swagger_tags = ['Core'] 
    queryset = Translation.objects.all()
    serializer_class = TranslationSerializer
    filterset_fields = ['language']
    search_fields = ['language__language']
    ordering_fields = ['language__language']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'language'
        )
    

    @swagger_auto_schema(auto_schema=None)
    def update(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    @swagger_auto_schema(auto_schema=None)
    def create(self, request, *args, **kwargs):
        return Response({"detail": "Method not allowed."}, status=status.HTTP_405_METHOD_NOT_ALLOWED)