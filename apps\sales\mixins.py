from django.db import transaction
from django.utils import timezone

class PriceHistoryMixin:
    def create_price_history(self, instance, price_data, history_model, **extra_fields):
        """
        Create price history entry when prices change
        """
        with transaction.atomic():
            # Deactivate previous active price history
            history_model.objects.filter(
                company=instance.company,
                is_active=True,
                **extra_fields
            ).update(
                is_active=False,
                price_valid_to=timezone.now().date()
            )
            
            # Create new price history entry
            history_model.objects.create(
                company=instance.company,
                unit_price=price_data['unit_price'],
                currency=price_data['currency'],
                price_valid_from=price_data.get('price_valid_from', timezone.now().date()),
                price_valid_to=price_data.get('price_valid_to'),
                changed_by=price_data['changed_by'],
                change_reason=price_data.get('change_reason', ''),
                is_active=True,
                **extra_fields
            ) 