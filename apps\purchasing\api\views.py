from rest_framework.decorators import action
from rest_framework.response import Response
from utils.views import BaseViewSet
from utils.mixins import BulkOperationMixin
from rest_framework import status
from django.db.models import Count
from django.utils import timezone
from apps.products.models import Product
from utils.pagination import CustomPageNumberPagination
from django.http import HttpResponse
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated

from utils.enums import InvoiceStatus

from utils.helpers import get_accurate_tax_rate
from .serializers import (
    SupplierSerializer, SupplierProductSerializer,
    PurchaseOrderSerializer, GoodsReceiptSerializer,
    SupplierContactSerializer, SupplierCategorySerializer,
    SupplierInvoiceSerializer, SupplierAddressSerializer,
    UpdateSupplierProductSerializer, SupplierInvoiceFromOrderSerializer,
    SupplierInvoiceMarkAsPaidSerializer
)
from apps.purchasing.services.supplier import SupplierService
from apps.purchasing.services.purchase_order import PurchaseOrderService
from apps.purchasing.services.goods_receipt import GoodsReceiptService
from .filters import SupplierFilter, PurchaseOrderFilter
from apps.purchasing.models import (
    PurchaseOrder, Supplier, GoodsReceipt, SupplierProduct, SupplierContact, 
    SupplierCategory, SupplierInvoice, SupplierAddress, PurchaseOrderLine
)
from apps.purchasing.services.invoice import SupplierInvoiceService
from apps.documents.services.pdf_generator import PDFGenerator


class SupplierViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Supplier.objects.all()
    serializer_class = SupplierSerializer
    filterset_class = SupplierFilter
    search_fields = ['name', 'number', 'email']
    ordering_fields = ['name', 'number', 'payment_terms']
    lookup_field = 'uuid'

    class Meta: 
        select_related_fields = ['company']

    def get_serializer_context(self):
        context = super().get_serializer_context()

        context['supplier_service'] = SupplierService(
            self.request.user,
            context.get('company', None)
        )
        
        return context

    @action(detail=True)
    def performance(self, request, uuid=None):
        supplier = self.get_object()
        service = SupplierService(request.user, request.company)
        performance = service.evaluate_supplier_performance(
            supplier.id,
            start_date=request.query_params.get('start_date'),
            end_date=request.query_params.get('end_date')
        )
        return Response(performance)

    @action(detail=True, methods=['post'], serializer_class=UpdateSupplierProductSerializer)
    def update_products(self, request, uuid=None):
        print(f"request.data: {request.data}")
        supplier = self.get_object()
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        products_data = serializer.validated_data.get('products', [])
        service = SupplierService(request.user, request.company)
        print(f"products_data: {products_data}")
        products = service.update_supplier_products(
            supplier.id,
            products_data
        )
        return Response(
            SupplierProductSerializer(
                products, 
                many=True,
                context=self.get_serializer_context()
            ).data
        )

    @action(detail=True, methods=['get'])
    def products(self, request, uuid=None):
        supplier = self.get_object()
        products = SupplierProduct.objects.filter(
            company=request.company,
            supplier=supplier
        ).select_related(
            'product',
            'variant',
            'supplier__currency'
        ).order_by('product__name')

        paginator = CustomPageNumberPagination()
        paginated_products = paginator.paginate_queryset(products, request)
        serializer = SupplierProductSerializer(paginated_products, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def transactions(self, request, uuid=None):
        supplier = self.get_object()
        orders = PurchaseOrder.objects.filter(supplier=supplier, company=request.company)
        invoices = SupplierInvoice.objects.filter(supplier=supplier, company=request.company)
        order_serializer = PurchaseOrderSerializer(orders, many=True)
        invoice_serializer = SupplierInvoiceSerializer(invoices, many=True)
        return Response({
            'orders': order_serializer.data,
            'invoices': invoice_serializer.data
        })

    @action(detail=True, methods=['get', 'post'])
    def contacts(self, request, uuid=None):
        supplier = self.get_object()
        
        if request.method == 'GET':
            contacts = SupplierContact.objects.filter(
                company=request.company,
                supplier=supplier
            ).order_by('name')
            paginator = CustomPageNumberPagination()
            paginated_contacts = paginator.paginate_queryset(contacts, request)

            serializer = SupplierContactSerializer(
                paginated_contacts, 
                many=True,
                context=self.get_serializer_context()
            )
            return paginator.get_paginated_response(serializer.data)
        
        elif request.method == 'POST':
            serializer = SupplierContactSerializer(
                data=request.data,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save(
                company=request.company,
                supplier=supplier
            )
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=True, methods=['put', 'delete'], url_path='contacts/(?P<contact_uuid>[^/.]+)')
    def manage_contact(self, request, uuid=None, contact_uuid=None):
        supplier = self.get_object()
        
        try:
            contact = SupplierContact.objects.get(
                company=request.company,
                supplier=supplier,
                uuid=contact_uuid
            )
        except SupplierContact.DoesNotExist:
            return Response(
                {'detail': 'Contact not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.method == 'PUT':
            serializer = SupplierContactSerializer(
                contact,
                data=request.data,
                partial=True,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        
        elif request.method == 'DELETE':
            contact.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=True, methods=['get', 'post'])
    def addresses(self, request, uuid=None):
        supplier = self.get_object()
        
        if request.method == 'GET':
            addresses = supplier.addresses.all()
            paginator = CustomPageNumberPagination()
            paginated_addresses = paginator.paginate_queryset(addresses, request)
            serializer = SupplierAddressSerializer(
                paginated_addresses, 
                many=True,
                context=self.get_serializer_context()
            )
            return paginator.get_paginated_response(serializer.data)
        
        elif request.method == 'POST':
            serializer = SupplierAddressSerializer(
                data=request.data,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save(
                company=request.company,
                supplier=supplier
            )
            return Response(
                serializer.data,
                status=status.HTTP_201_CREATED
            )

    @action(detail=True, methods=['put', 'delete'], url_path='addresses/(?P<address_uuid>[^/.]+)')
    def manage_address(self, request, uuid=None, address_uuid=None):
        supplier = self.get_object()
        
        try:
            address = supplier.addresses.get(uuid=address_uuid)
        except SupplierAddress.DoesNotExist:
            return Response(
                {'detail': 'Address not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        if request.method == 'PUT':
            serializer = SupplierAddressSerializer(
                address,
                data=request.data,
                partial=True,
                context=self.get_serializer_context()
            )
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)
        
        elif request.method == 'DELETE':
            address.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)


class PurchaseOrderViewSet(BaseViewSet):
    queryset = PurchaseOrder.objects.all()
    serializer_class = PurchaseOrderSerializer
    filterset_class = PurchaseOrderFilter
    search_fields = ['number', 'supplier__name', 'notes']
    ordering_fields = ['number', 'order_date', 'total_amount']
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = ['supplier', 'receiving_location']
        prefetch_related_fields = ['lines']

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['order_service'] = PurchaseOrderService(
            self.request.user,
            context.get('company', None)
        )
        return context

    @action(detail=True, methods=['put'])
    def send(self, request, uuid=None): # mark as sent
        """Send order to supplier (currently only sets it to 'sent' status)"""
        order = self.get_object()
        service = PurchaseOrderService(request.user, request.company)
        order = service.send_to_supplier(order)
        return Response(self.get_serializer(order).data)

    @action(detail=True, methods=['put'])
    def confirm(self, request, uuid=None): # mark as confirmed by supplier
        """Confirm order (supplier accepted)"""
        order = self.get_object()
        service = PurchaseOrderService(request.user, request.company)
        order = service.confirm_order(
            order=order,
            supplier_reference=request.data.get('supplier_reference')
        )
        return Response(self.get_serializer(order).data)

    @action(detail=True, methods=['put'])
    def ready_for_receipt(self, request, uuid=None):
        """Mark order as ready for receiving"""
        order = self.get_object()
        service = PurchaseOrderService(request.user, request.company)
        order = service.mark_ready_for_receipt(order)
        return Response(self.get_serializer(order).data)

    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        """Cancel the order"""
        order = self.get_object()
        if not request.data.get('reason'):
            return Response(
                {'error': 'Cancellation reason is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        service = PurchaseOrderService(request.user, request.company)
        order = service.cancel_order(
            order=order,
            reason=request.data['reason']
        )
        return Response(self.get_serializer(order).data)
    
class PurchaseOrderLineViewSet(BaseViewSet): 
    from utils.serializers import GetLineTaxRateSerializer
    queryset = PurchaseOrderLine.objects.all()
    serializer_class = GetLineTaxRateSerializer
    lookup_field = 'uuid'

    @action(detail=False, methods=['post'])
    def accurate_tax_rate(self, request):
        """Get the accurate tax rate for a purchase order line"""
        product_id = request.data['product']
        supplier_id = request.data['supplier']
        product = Product.objects.get(id=product_id, company=request.company)
        supplier = Supplier.objects.get(id=supplier_id, company=request.company)
        return Response({'tax_rate': get_accurate_tax_rate(product, supplier)}, status=status.HTTP_200_OK)

class GoodsReceiptViewSet(BaseViewSet):
    queryset = GoodsReceipt.objects.all()
    serializer_class = GoodsReceiptSerializer
    filterset_fields = {
        'reference_number': ['exact', 'icontains'],
        'purchase_order': ['exact'],
        'receipt_date': ['exact', 'gte', 'lte']
    }
    search_fields = ['reference_number', 'notes']
    ordering_fields = ['reference_number', 'receipt_date']

    class Meta:
        select_related_fields = ['purchase_order', 'receiver']
        prefetch_related_fields = ['lines']

    @action(detail=False, methods=['post'])
    def create_receipt(self, request):
        service = GoodsReceiptService(request.user, request.company)
        receipt = service.create_receipt(
            po_id=request.data['purchase_order_id'],
            lines=request.data['lines'],
            reference_number=request.data['reference_number'],
            notes=request.data.get('notes')
        )
        return Response(self.get_serializer(receipt).data)

    @action(detail=True, methods=['post'])
    def send_to_qc(self, request, uuid=None):
        """Send receipt for quality control"""
        service = GoodsReceiptService(request.user, request.company)
        receipt = service.send_to_quality_control(uuid)
        return Response(self.get_serializer(receipt).data)

    @action(detail=True, methods=['post'])
    def complete_qc(self, request, uuid=None):
        """Complete quality control check"""
        quality_status = request.data.get('quality_status')
        if not quality_status:
            return Response(
                {'error': 'quality_status is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        service = GoodsReceiptService(request.user, request.company)
        receipt = service.complete_quality_control(
            receipt_id=uuid,
            quality_status=quality_status,
            notes=request.data.get('notes')
        )
        return Response(self.get_serializer(receipt).data)

    @action(detail=True, methods=['post'])
    def complete(self, request, uuid=None):
        """Complete the goods receipt"""
        service = GoodsReceiptService(request.user, request.company)
        receipt = service.complete_receipt(uuid)
        return Response(self.get_serializer(receipt).data)

    @action(detail=True, methods=['post'])
    def cancel(self, request, uuid=None):
        """Cancel the goods receipt"""
        service = GoodsReceiptService(request.user, request.company)
        receipt = service.cancel_receipt(
            receipt_id=uuid,
            reason=request.data.get('reason', '')
        )
        return Response(self.get_serializer(receipt).data)

class SupplierCategoryViewSet(BaseViewSet, BulkOperationMixin):
    queryset = SupplierCategory.objects.all()
    serializer_class = SupplierCategorySerializer
    search_fields = ['name', 'number', 'description']
    ordering_fields = ['name', 'number']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().annotate(
            supplier_count=Count('suppliers')
        )
    
    @action(detail=False, methods=['get'])
    def root_categories(self, request):
        queryset = self.get_queryset().filter(parent__isnull=True)
        paginator = CustomPageNumberPagination()
        paginated_queryset = paginator.paginate_queryset(queryset, request)
        serializer = self.get_serializer(paginated_queryset, many=True)
        return paginator.get_paginated_response(serializer.data)

    @action(detail=True, methods=['get'])
    def tree(self, request, uuid=None):
        instance = self.get_object()
        paginator = CustomPageNumberPagination()
        descendants = instance.get_descendants(include_self=True)
        paginated_descendants = paginator.paginate_queryset(descendants, request)
        serializer = self.get_serializer(paginated_descendants, many=True)
        return paginator.get_paginated_response(serializer.data)
    
    @action(detail=True)
    def suppliers(self, request, uuid=None):
        """Get all suppliers in this category"""
        category = self.get_object()
        suppliers = category.suppliers.all()
        paginator = CustomPageNumberPagination()
        paginated_suppliers = paginator.paginate_queryset(suppliers, request)
        serializer = SupplierSerializer(
            paginated_suppliers, 
            many=True,
            context=self.get_serializer_context()
        )
        return paginator.get_paginated_response(serializer.data)

class SupplierInvoiceViewSet(BaseViewSet):
    queryset = SupplierInvoice.objects.all()
    serializer_class = SupplierInvoiceSerializer
    filterset_fields = {
        'number': ['exact', 'icontains'],
        'invoice_number': ['exact', 'icontains'],
        'supplier': ['exact'],
        'purchase_order': ['exact'],
        'invoice_date': ['exact', 'gte', 'lte'],
        'due_date': ['exact', 'gte', 'lte'],
        'status': ['exact']
    }
    search_fields = ['number', 'invoice_number', 'notes']
    ordering_fields = ['invoice_date', 'due_date', 'total_amount']
    lookup_field = 'uuid'

    @action(detail=False, methods=['post'], serializer_class=SupplierInvoiceFromOrderSerializer)
    def create_from_order(self, request):
        """Create invoice from purchase order"""
        service = SupplierInvoiceService(request.user, request.company)
        invoice = service.create_invoice_from_order(
            po_id=request.data['purchase_order_id'],
            invoice_number=request.data['invoice_number'],
            invoice_date=request.data['invoice_date'],
            use_received_quantities=request.data.get('use_received_quantities', True)
        )
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['put'])
    def send(self, request, uuid=None):
        """Mark invoice as sent"""
        invoice = self.get_object()
        service = SupplierInvoiceService(request.user, request.company)
        invoice = service.send_invoice(invoice)
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['put'], serializer_class=SupplierInvoiceMarkAsPaidSerializer)
    def mark_as_paid(self, request, uuid=None):
        """Mark invoice as paid"""
        invoice = self.get_object()
        service = SupplierInvoiceService(request.user, request.company)
        invoice = service.mark_as_paid(
            invoice=invoice,
            payment_date=request.data.get('payment_date', timezone.now().date()),
            payment_reference=request.data.get('payment_reference'),
            partial=request.data.get('partial', False)
        )
        response_serializer = SupplierInvoiceSerializer(invoice, context=self.get_serializer_context())
        return Response(response_serializer.data)

    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        """Cancel the invoice"""
        if not request.data.get('reason'):
            return Response(
                {'error': 'Cancellation reason is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        invoice = self.get_object()
        service = SupplierInvoiceService(request.user, request.company)
        invoice = service.cancel_invoice(
            invoice=invoice,
            reason=request.data['reason']
        )
        return Response(self.get_serializer(invoice).data)

    @action(detail=True, methods=['get'])
    def pdf(self, request, uuid=None):
        """
        Generate a PDF for the supplier invoice
        """
        try:
            invoice = self.get_object()
            
            # Generate the PDF
            pdf_content, filename = PDFGenerator.generate_supplier_invoice_pdf(
                invoice=invoice,
                company=request.company
            )
            
            # Create the HTTP response with PDF content
            response = HttpResponse(pdf_content, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            return response
            
        except Exception as e:
            return Response(
                {"error": str(e)}, 
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )