from django.contrib import admin
from django.db.models import Count, Sum, F, DecimalField
from django.db.models.functions import Coalesce
from django.utils.html import format_html
from .models import (
    ProductType,
    ProductCategory,
    Product,
    ProductVariant,
    ProductPricing,
    ProductBundle,
    BundleComponent,
    BundlePricing,
    PriceHistoryEntry
)
from django.db import models
from decimal import Decimal

@admin.register(PriceHistoryEntry)
class PriceHistoryEntryAdmin(admin.ModelAdmin):
    list_display = ['id', 'product', 'variant', 'currency', 'base_selling_price']
    list_filter = ['company', 'currency']
    search_fields = ['variant__name', 'variant__sku', 'currency__currency']

@admin.register(ProductPricing)
class ProductPricingAdmin(admin.ModelAdmin):
    list_display = ['id', 'uuid', 'product', 'currency', 'base_selling_price']
    list_filter = ['company', 'currency']
    search_fields = ['product__name', 'product__sku', 'currency__currency']

class BasePricingInline(admin.TabularInline):
    """Base class for pricing inlines with common configurations"""
    extra = 0
    fields = ['currency', 'base_selling_price', 'is_active']
    show_change_link = True

class ProductPricingInline(BasePricingInline):
    model = ProductPricing
    fields = ['currency', 'base_purchasing_price', 'actual_purchasing_price', 'base_selling_price', 'recommended_selling_price', 'is_active']

class BundlePricingInline(BasePricingInline):
    model = BundlePricing
    fields = ['currency', 'base_selling_price', 'cost_price', 'is_active']

@admin.register(ProductType)
class ProductTypeAdmin(admin.ModelAdmin):
    list_display = ['id', 'name', 'number', 'description', 'product_count']
    search_fields = ['name', 'number']
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            product_count=Count('products', distinct=True)
        )
    
    def product_count(self, obj):
        return obj.product_count
    product_count.admin_order_field = 'product_count'
    product_count.short_description = 'Products'

class CategoryInline(admin.TabularInline):
    model = ProductCategory
    extra = 0
    fields = ['name', 'number', 'can_be_sold']
    show_change_link = True
    verbose_name = "Sub-category"
    verbose_name_plural = "Sub-categories"

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = [
        'name', 
        'number', 
        'parent', 
        'type',
        'company', 
        'product_count', 
        'subcategory_count',
        'can_be_sold'
    ]
    list_filter = [
        'company', 
        'type',
        'can_be_sold',
        ('parent', admin.EmptyFieldListFilter)
    ]
    search_fields = ['name', 'number', 'description']
    inlines = [CategoryInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            product_count=Count('products', distinct=True),
            subcategory_count=Count('children', distinct=True)
        )
    
    def product_count(self, obj):
        url = f"/admin/products/product/?category__id__exact={obj.id}"
        return format_html('<a href="{}">{}</a>', url, obj.product_count)
    product_count.admin_order_field = 'product_count'
    
    def subcategory_count(self, obj):
        url = f"/admin/products/productcategory/?parent__id__exact={obj.id}"
        return format_html('<a href="{}">{}</a>', url, obj.subcategory_count)
    subcategory_count.admin_order_field = 'subcategory_count'

class ProductVariantInline(admin.TabularInline):
    model = ProductVariant
    extra = 0
    fields = ['name', 'sku_suffix', 'barcode', 'weight', 'weight_unit', 'is_active']
    show_change_link = True

@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'name', 
        'sku', 
        'type',
        'category', 
        'unit_of_measure',
        'stock_status',
        'pricing_summary',
        'variant_count',
        'is_active'
    ]
    list_filter = [
        'company',
        'type',
        'category',
        'unit_of_measure',
        'lot_tracking_required',
        'is_active'
    ]
    search_fields = [
        'name',
        'sku',
        'barcode',
        'description'
    ]
    inlines = [ProductVariantInline, ProductPricingInline]
    
    fieldsets = (
        (None, {
            'fields': (
                ('company', 'is_active'),
                ('name', 'sku', 'barcode'),
                'description',
                ('type', 'category'),
                'unit_of_measure',
            )
        }),
        ('Physical Properties', {
            'fields': (
                ('weight', 'weight_unit'),
                ('width', 'height', 'depth', 'size_unit'),
            ),
            'classes': ('collapse',)
        }),
        ('Inventory Control', {
            'fields': (
                ('min_stock_level', 'max_stock_level'),
                'lot_tracking_required',
            )
        }),
        ('Media', {
            'fields': ('image', 'pdf'),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            variant_count=Count('variants', distinct=True),
            total_stock=Coalesce(
                Sum('inventory_items__quantity', output_field=DecimalField()),
                0,
                output_field=DecimalField()
            )
        ).prefetch_related('prices')
    
    def variant_count(self, obj):
        url = f"/admin/products/productvariant/?product__id__exact={obj.id}"
        return format_html('<a href="{}">{}</a>', url, obj.variant_count)
    variant_count.short_description = "Variants"
    variant_count.admin_order_field = 'variant_count'
    
    def stock_status(self, obj):
        if not hasattr(obj, 'total_stock'):
            return "N/A"
            
        total_stock = obj.total_stock
        status_color = 'green'
        
        if total_stock <= obj.min_stock_level:
            status_color = 'red'
        elif obj.max_stock_level and total_stock >= obj.max_stock_level:
            status_color = 'orange'
            
        # Fix: Use {} placeholders instead of {:.2f}
        return format_html(
            '<span style="color: {};">{} {}</span>',
            status_color,
            str(round(float(total_stock), 2)),  # Format the decimal manually
            obj.unit_of_measure.abbreviation
        )
    stock_status.short_description = "Current Stock"

    def pricing_summary(self, obj):
        prices = list(obj.prices.filter(is_active=True, variant__isnull=True))
        if not prices:
            return "-"
            
        price_strings = []
        for price in prices[:2]:  # Show only first 2 currencies
            price_strings.append(
                f"{price.base_selling_price} {price.currency.currency}"
            )
            
        if len(prices) > 2:
            price_strings.append("...")
            
        return format_html("<br>".join(price_strings))
    pricing_summary.short_description = "Base Prices"

class BundleComponentInline(admin.TabularInline):
    model = BundleComponent
    extra = 1
    fields = ['product', 'variant', 'quantity', 'is_optional']
    raw_id_fields = ['product', 'variant']

@admin.register(ProductBundle)
class ProductBundleAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'sku',
        'component_count',
        'price_display',
        'is_active'
    ]
    list_filter = ['company', 'is_active', 'price_override']
    search_fields = ['name', 'sku', 'barcode', 'description']
    inlines = [BundleComponentInline, BundlePricingInline]
    
    fieldsets = (
        (None, {
            'fields': (
                ('company', 'is_active'),
                ('name', 'sku', 'barcode'),
                'description',
            )
        }),
        ('Pricing', {
            'fields': (
                'price_override',
                'discount_percentage',
            )
        }),
        ('Media', {
            'fields': ('image',),
            'classes': ('collapse',)
        })
    )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            component_count=Count('components', distinct=True)
        )

    def component_count(self, obj):
        return obj.component_count
    component_count.admin_order_field = 'component_count'
    
    def price_display(self, obj):
        prices = list(obj.prices.filter(is_active=True))
        if not prices:
            return "-"
            
        price_strings = []
        for price in prices[:2]:
            price_strings.append(
                f"{price.base_selling_price} {price.currency.currency}"
            )
            
        if len(prices) > 2:
            price_strings.append("...")
            
        return format_html("<br>".join(price_strings))
    price_display.short_description = "Prices"

@admin.register(ProductVariant)
class ProductVariantAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'product',
        'full_sku',
        'barcode',
        'stock_status',
        'pricing_display',
        'is_active'
    ]
    list_filter = ['company', 'product__category', 'is_active']
    search_fields = [
        'name',
        'sku_suffix',
        'barcode',
        'product__name',
        'product__sku'
    ]
    raw_id_fields = ['product']
    inlines = [ProductPricingInline]
    
    fieldsets = (
        (None, {
            'fields': (
                ('company', 'is_active'),
                'product',
                ('name', 'sku_suffix', 'barcode'),
            )
        }),
        ('Physical Properties', {
            'fields': (
                ('weight', 'weight_unit'),
                ('width', 'height', 'depth', 'size_unit'),
            ),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            total_stock=Coalesce(
                Sum('inventory_items__quantity'), 
                Decimal('0')
            )
        ).select_related('product')
    
    def full_sku(self, obj):
        return f"{obj.product.sku}-{obj.sku_suffix}"
    full_sku.short_description = "Full SKU"
    
    def stock_status(self, obj):
        if not hasattr(obj, 'total_stock'):
            return "N/A"
            
        return format_html(
            '{:.2f} {}',
            obj.total_stock,
            obj.product.unit_of_measure.abbreviation
        )
    stock_status.short_description = "Current Stock"
    
    def pricing_display(self, obj):
        prices = list(obj.prices.filter(is_active=True))
        if not prices:
            return "-"
            
        price_strings = []
        for price in prices[:2]:
            price_strings.append(
                f"{price.base_selling_price} {price.currency.currency}"
            )
            
        if len(prices) > 2:
            price_strings.append("...")
            
        return format_html("<br>".join(price_strings))
    pricing_display.short_description = "Prices"