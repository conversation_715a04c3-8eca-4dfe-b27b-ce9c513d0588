from django_filters import rest_framework as filters
from apps.inventory.models import Location, Lot, InventoryItem, InventoryMovement

class LocationFilter(filters.FilterSet):
    class Meta:
        model = Location
        fields = {
            'warehouse': ['exact'],
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
            'parent': ['exact', 'isnull'],
            'is_receiving': ['exact'],
            'is_shipping': ['exact'],
            'is_storage': ['exact'],
            'is_active': ['exact'],
            'is_quality_control': ['exact'],
            'is_returns': ['exact']
        }


class LotFilter(filters.FilterSet):
    expiry_before = filters.DateFilter(
        field_name='expiry_date',
        lookup_expr='lte'
    )
    expiry_after = filters.DateFilter(
        field_name='expiry_date',
        lookup_expr='gte'
    )

    class Meta:
        model = Lot
        fields = {
            'number': ['exact', 'icontains'],
            'product': ['exact'],
            'variant': ['exact'],
            'is_blocked': ['exact']
        }


class InventoryItemFilter(filters.FilterSet):
    min_quantity = filters.NumberFilter(
        field_name='quantity',
        lookup_expr='gte'
    )
    max_quantity = filters.NumberFilter(
        field_name='quantity',
        lookup_expr='lte'
    )

    class Meta:
        model = InventoryItem
        fields = {
            'product': ['exact'],
            'variant': ['exact'],
            'lot': ['exact'],
            'location': ['exact']
        }


class InventoryMovementFilter(filters.FilterSet):
    scheduled_date_before = filters.DateTimeFilter(
        field_name='scheduled_date',
        lookup_expr='lte'
    )
    scheduled_date_after = filters.DateTimeFilter(
        field_name='scheduled_date',
        lookup_expr='gte'
    )

    class Meta:
        model = InventoryMovement
        fields = {
            'reference_number': ['exact', 'icontains'],
            'movement_type': ['exact'],
            'from_location': ['exact'],
            'to_location': ['exact'],
            'status': ['exact']
        }