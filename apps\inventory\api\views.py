from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters import rest_framework as filters
from utils.views import BaseViewSet
from utils.mixins import BulkOperationMixin
from utils.enums import AdjustmentStatus,InventoryMovementStatus
from apps.inventory.api.filters import (
    LocationFilter, LotFilter, InventoryItemFilter, InventoryMovementFilter
)
from apps.inventory.api.serializers import (
    LocationSerializer, LotSerializer,
    InventoryItemSerializer, InventoryMovementSerializer,
    InventoryAdjustmentSerializer,
    InventoryAdjustmentCreateSerializer,
)
from apps.inventory.services.location import LocationService
from apps.inventory.models import Location, Lot, InventoryItem, InventoryMovement, InventoryAdjustment
from apps.operations.services.adjustment import AdjustmentService
from apps.operations.services.movement import MovementService
from django.core.exceptions import ValidationError
from rest_framework import status, viewsets
from apps.products.models import Product, ProductVariant
from utils.views import BaseServiceViewSet



class LocationViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Location.objects.all()
    serializer_class = LocationSerializer
    filterset_class = LocationFilter
    search_fields = ['name', 'number']
    ordering_fields = ['name', 'number']
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = ['warehouse', 'parent']

    @action(detail=False, methods=['post'])
    def setup_special_locations(self, request):
        service = LocationService(request.user, request.company)
        locations = service.setup_special_locations()
        
        return Response({
            'quality_control': LocationSerializer(
                locations['quality_control']
            ).data,
            'returns_stock': LocationSerializer(
                locations['returns_stock']
            ).data
        })



class LotViewSet(BaseViewSet, BulkOperationMixin):
    queryset = Lot.objects.all()
    serializer_class = LotSerializer
    filterset_class = LotFilter
    search_fields = ['number', 'supplier_lot_number']
    ordering_fields = ['number', 'manufactured_date', 'expiry_date']

    class Meta:
        select_related_fields = ['product', 'variant']

    @action(detail=True, methods=['post'])
    def block(self, request, pk=None):
        instance = self.get_object()
        instance.is_blocked = True
        instance.save()
        return Response(self.get_serializer(instance).data)

    @action(detail=True, methods=['post'])
    def unblock(self, request, pk=None):
        instance = self.get_object()
        instance.is_blocked = False
        instance.save()
        return Response(self.get_serializer(instance).data)

class InventoryItemViewSet(BaseViewSet, BulkOperationMixin):
    queryset = InventoryItem.objects.all()
    serializer_class = InventoryItemSerializer
    filterset_class = InventoryItemFilter
    search_fields = [
        'product__name',
        'product__sku',
        'variant__name',
        'lot__number'
    ]
    ordering_fields = ['quantity', 'reserved_quantity']
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = [
            'product', 'variant', 'lot', 'location'
        ]

    @action(detail=False, methods=['get'])
    def by_product_location(self, request):
        """
        Get inventory items for a specific product at a specific location.
        
        Query parameters:
        - product_uuid: UUID of the product
        - location_uuid: UUID of the location
        - variant_uuid: Optional UUID of the variant
        """
        product_uuid = request.query_params.get('product_uuid')
        location_uuid = request.query_params.get('location_uuid')
        variant_uuid = request.query_params.get('variant_uuid', None)

        print(request.query_params)
        
        if not product_uuid or not location_uuid:
            return Response(
                {'error': 'Both product_uuid and location_uuid are required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get the product and location by UUID
        try:
            product = Product.objects.get(uuid=product_uuid, company=request.company)
            location = Location.objects.get(uuid=location_uuid, company=request.company)
            variant = None
        except (Product.DoesNotExist, Location.DoesNotExist):
            return Response(
                {'error': 'Product or Location not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Build the query
        query = InventoryItem.objects.filter(
            company=request.company,
            product=product,
            location=location
        )
        print(query)
        
        # Add variant filter if provided
        if variant_uuid:
            try:
                variant = ProductVariant.objects.get(uuid=variant_uuid, company=request.company)
                query = query.filter(variant=variant)
            except ProductVariant.DoesNotExist:
                return Response(
                    {'error': 'Variant not found'},
                    status=status.HTTP_404_NOT_FOUND
                )
        
        # Get the results
        inventory_item = query.select_related(
            'product', 'variant', 'lot', 'location'
        ).first() # there should only be 1 inventory item per product at a location
        print(inventory_item)
        if not inventory_item:
            from apps.products.api.serializers import ProductSerializer, ProductVariantSerializer
            return Response(
                {
                    'product': ProductSerializer(product).data,
                    'variant': ProductVariantSerializer(variant).data if variant else None,
                    'location': LocationSerializer(location).data,
                    'quantity': 0,
                    'reserved_quantity': 0
                },
                status=status.HTTP_200_OK
            )
        
        serializer = self.get_serializer(inventory_item)
        return Response(serializer.data)



class InventoryMovementViewSet(BaseViewSet, BaseServiceViewSet):
    queryset = InventoryMovement.objects.all()
    serializer_class = InventoryMovementSerializer
    filterset_class = InventoryMovementFilter
    search_fields = ['reference_number', 'notes']
    ordering_fields = [
        'reference_number', 'scheduled_date',
        'completed_date'
    ]
    lookup_field = 'uuid'

    class Meta:
        select_related_fields = ['from_location', 'to_location']
        prefetch_related_fields = ['lines']

    def get_queryset(self):
        return super().get_queryset().select_related(
            'from_location',
            'to_location'
        ).prefetch_related(
            'lines__product',
            'lines__variant',
            'lines__lot'
        )
    
    def create(self, request):
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        service = MovementService(company=request.company, user=request.user)
        try:
            movement = service.create_internal_movement(**serializer.validated_data)
            return Response(self.get_serializer(movement).data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def update(self, request, uuid=None):
        movement = self.get_object()
        serializer = self.serializer_class(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        service = MovementService(company=request.company, user=request.user)
        try:
            movement = service.update_internal_movement(movement=movement, **serializer.validated_data)
            return Response(self.get_serializer(movement).data) 
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    @action(detail=True, methods=['put'])
    def await_processing(self, request, uuid=None):
        movement = self.get_object()
        if movement.status != InventoryMovementStatus.DRAFT.name:
            return Response(
                {'error': 'Movement is not in draft status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        service = MovementService(company=request.company, user=request.user)
        try:
            service.await_processing(movement)
            return Response(self.get_serializer(movement).data)
        except ValueError as e:
            return self.handle_service_error(e)

    @action(detail=True, methods=['put'])
    def validate(self, request, uuid=None):
        movement = self.get_object()
        if movement.status != InventoryMovementStatus.DRAFT.name:
            return Response(
                {'error': 'Movement is not in draft status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        service = MovementService(company=request.company, user=request.user)
        try:
            service.validate_movement(movement)
            return Response(self.get_serializer(movement).data)
        except ValueError as e:
            return self.handle_service_error(e)

    @action(detail=True, methods=['put'])
    def process(self, request, uuid=None):
        movement = self.get_object()
        if movement.status != InventoryMovementStatus.PENDING.name:
            return Response(
                {'error': 'Movement is not in pending status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        service = MovementService(company=request.company, user=request.user)
        try:
            service.process_movement(movement)
            return Response(self.get_serializer(movement).data)
        except ValueError as e:
            return self.handle_service_error(e)
        
    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        movement = self.get_object()
        if movement.status not in [InventoryMovementStatus.DRAFT.name, InventoryMovementStatus.PENDING.name]:
            return Response(
                {'error': 'Movement is not in draft or pending status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        service = MovementService(company=request.company, user=request.user)
        try:
            service.cancel_movement(movement)
            return Response(self.get_serializer(movement).data)
        except ValueError as e:
            return self.handle_service_error(e)

class InventoryAdjustmentViewSet(viewsets.ModelViewSet):
    queryset = InventoryAdjustment.objects.all()
    serializer_class = InventoryAdjustmentSerializer
    filterset_fields = ['status', 'adjustment_type', 'location']
    search_fields = ['reference_number', 'notes']
    ordering_fields = ['created_at', 'status']
    ordering = ['-created_at']
    lookup_field = 'uuid'

    def get_queryset(self):
        return super().get_queryset().select_related(
            'location',
            'approved_by'
        ).prefetch_related(
            'lines__product',
            'lines__variant',
            'lines__lot'
        )

    def create(self, request):
        serializer = InventoryAdjustmentCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        service = AdjustmentService(company=request.company, user=request.user)
        try:
            adjustment = service.create_adjustment(
                **serializer.validated_data
            )
            return Response(
                self.get_serializer(adjustment).data,
                status=status.HTTP_201_CREATED
            )
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        
    def update(self, request, uuid=None):
        adjustment = self.get_object()
        if adjustment.status != AdjustmentStatus.DRAFT.name:
            return Response(
                {'error': 'Adjustment is not in draft status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        serializer = InventoryAdjustmentCreateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        service = AdjustmentService(company=request.company, user=request.user)
        try:
            adjustment = service.update_adjustment(
                adjustment=adjustment,
                **serializer.validated_data
            )
            return Response(self.get_serializer(adjustment).data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
        

    @action(detail=True, methods=['put'])
    def await_approval(self, request, uuid=None):
        service = AdjustmentService(company=request.company, user=request.user)
        adjustment = self.get_object()
        try:
            adjustment = service.await_approval(adjustment)
            return Response(self.get_serializer(adjustment).data)
        except ValidationError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['put'])
    def approve(self, request, uuid=None):
        service = AdjustmentService(company=request.company, user=request.user)
        adjustment = self.get_object()
        if adjustment.status != AdjustmentStatus.PENDING.name:
            return Response(
                {'error': 'Adjustment is not in pending status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            adjustment = service.approve_adjustment(
                adjustment=adjustment,
                user=request.user
            )
            return Response(self.get_serializer(adjustment).data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['put'])
    def cancel(self, request, uuid=None):
        service = AdjustmentService(company=request.company, user=request.user)
        adjusment = self.get_object()
        if adjusment.status not in [AdjustmentStatus.PENDING.name, AdjustmentStatus.DRAFT.name]:
            return Response(
                {'error': 'Adjustment is not in pending or draft status'},
                status=status.HTTP_400_BAD_REQUEST
            )
        try:
            adjustment = service.cancel_adjustment(
                adjustment=adjusment
            )
            return Response(self.get_serializer(adjustment).data)
        except ValidationError as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )