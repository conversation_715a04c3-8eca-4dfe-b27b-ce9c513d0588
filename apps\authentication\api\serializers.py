# authentication/serializers.py
from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from apps.core.models import Company, Employee
from django.contrib.auth.password_validation import validate_password


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    def validate(self, attrs):
        data = super().validate(attrs)
        
        # Add user's companies to response
        companies = Company.objects.filter(
            company_employee__user=self.user,
            company_employee__is_active=True
        ).values('id', 'name')
        
        data['companies'] = companies
        # Add current active company if exists
        active_session = self.user.active_company_sessions.filter(is_active=True).first()
        if active_session:
            data['active_company'] = {
                'id': active_session.company.id,
                'uuid': active_session.company.uuid,
                'name': active_session.company.name
            }
        return data

class CompanySwitchSerializer(serializers.Serializer):
    company_id = serializers.UUIDField()

    def validate_company_id(self, value):
        if not Company.objects.filter(id=value, is_active=True).exists():
            raise serializers.ValidationError("Invalid company ID")
        return value
    

class PasswordChangeSerializer(serializers.Serializer):
    password = serializers.CharField(
        validators=[validate_password],
        write_only=True
    )
    confirm_password = serializers.CharField(write_only=True)

    def validate(self, attrs):
        if attrs['password'] != attrs['confirm_password']:
            raise serializers.ValidationError({
                'confirm_password': 'Passwords do not match'
            })
        return attrs