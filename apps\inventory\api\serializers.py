from rest_framework import serializers
from utils.serializers import BaseModelSerializer
from apps.inventory.models import (
    Location, Lot, InventoryItem,
    InventoryMovement, InventoryMovementLine,
    InventoryAdjustmentLine, InventoryAdjustment
)
from django.db import transaction
from utils.enums import AdjustmentType, AdjustmentStatus
from django.utils import timezone


class LotSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = Lot
        fields = [
            'id', 'number', 'product', 'product_name',
            'variant', 'variant_name', 'manufactured_date',
            'expiry_date', 'supplier_lot_number', 'notes',
            'is_blocked'
        ]
        read_only_fields = ['id']

    def validate(self, attrs):
        attrs = super().validate(attrs)
        # Validate product belongs to same company
        if attrs.get('product'):
            if attrs['product'].company != self.context['request'].company:
                raise serializers.ValidationError({
                    'product': 'Product must belong to the same company'
                })

        # Validate variant belongs to product
        if attrs.get('variant'):
            if attrs['variant'].product != attrs.get('product'):
                raise serializers.ValidationError({
                    'variant': 'Variant must belong to the specified product'
                })

        return attrs

class InventoryItemSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )
    lot_number = serializers.CharField(
        source='lot.number',
        read_only=True,
        allow_null=True
    )
    location_name = serializers.CharField(
        source='location.name',
        read_only=True
    )
    available_quantity = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        read_only=True,
        source='get_available_quantity'
    )

    representation_fields = {
        'product': 'apps.products.api.serializers.ProductSerializer',
        'variant': 'apps.products.api.serializers.ProductVariantSerializer'
    }

    class Meta:
        model = InventoryItem
        fields = [
            'id', 'uuid', 'product', 'product_name', 'variant',
            'variant_name', 'lot', 'lot_number', 'location',
            'location_name', 'quantity', 'reserved_quantity',
            'available_quantity', 'cost_price'
        ]
        read_only_fields = ['id', 'reserved_quantity']

    def validate(self, attrs):
        attrs = super().validate(attrs)
        # Validate all related objects belong to same company
        company = self.context['request'].company

        if attrs.get('product') and attrs['product'].company != company:
            raise serializers.ValidationError({
                'product': 'Product must belong to the same company'
            })

        if attrs.get('variant'):
            if attrs['variant'].company != company:
                raise serializers.ValidationError({
                    'variant': 'Variant must belong to the same company'
                })
            if attrs['variant'].product != attrs.get('product'):
                raise serializers.ValidationError({
                    'variant': 'Variant must belong to the specified product'
                })

        if attrs.get('lot') and attrs['lot'].company != company:
            raise serializers.ValidationError({
                'lot': 'Lot must belong to the same company'
            })

        if attrs.get('location') and attrs['location'].company != company:
            raise serializers.ValidationError({
                'location': 'Location must belong to the same company'
            })

        return attrs


class LocationSerializer(BaseModelSerializer):
    inventory_items = InventoryItemSerializer(many=True, read_only=True)
    warehouse_name = serializers.CharField(
        source='warehouse.name',
        read_only=True
    )
    parent_name = serializers.CharField(
        source='parent.name',
        read_only=True
    )

    representation_fields = {
        'inventory_items': 'apps.inventory.api.serializers.InventoryItemSerializer'
    }

    class Meta:
        model = Location
        fields = [
            'id', 'uuid', 'warehouse', 'warehouse_name', 'name', 'number',
            'parent', 'parent_name', 'is_receiving', 'is_shipping',
            'is_storage', 'is_active', 'is_quality_control',
            'is_returns','inventory_items'
        ]
        read_only_fields = ['id']

    def validate(self, attrs):
        attrs = super().validate(attrs)
        # Validate warehouse belongs to same company
        if attrs.get('warehouse'):
            if attrs['warehouse'].company != self.context['request'].company:
                raise serializers.ValidationError({
                    'warehouse': 'Warehouse must belong to the same company'
                })

        # Validate parent belongs to same company
        if attrs.get('parent'):
            if attrs['parent'].company != self.context['request'].company:
                raise serializers.ValidationError({
                    'parent': 'Parent location must belong to the same company'
                })
            
            # Validate parent is in same warehouse
            if attrs.get('warehouse') and attrs['parent'].warehouse != attrs['warehouse']:
                raise serializers.ValidationError({
                    'parent': 'Parent location must be in the same warehouse'
                })

        return attrs


class InventoryMovementLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )
    lot_number = serializers.CharField(
        source='lot.number',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = InventoryMovementLine
        fields = [
            'id', 'product', 'product_name', 'variant',
            'variant_name', 'lot', 'lot_number', 'quantity',
            'cost_price'
        ]
        read_only_fields = ['id']

class InventoryMovementSerializer(BaseModelSerializer):
    lines = InventoryMovementLineSerializer(many=True)
    from_location_name = serializers.CharField(
        source='from_location.name',
        read_only=True,
        allow_null=True
    )
    to_location_name = serializers.CharField(
        source='to_location.name',
        read_only=True,
        allow_null=True
    )
    reference_number = serializers.CharField(
        required=False,
        allow_null=True
    )

    representation_fields = {
        'from_location': 'apps.inventory.api.serializers.LocationSerializer',
        'to_location': 'apps.inventory.api.serializers.LocationSerializer'
    }

    class Meta:
        model = InventoryMovement
        fields = [
            'id', 'uuid', 'reference_number', 'movement_type',
            'from_location', 'from_location_name',
            'to_location', 'to_location_name', 'status',
            'notes', 'scheduled_date', 'completed_date',
            'lines'
        ]
        read_only_fields = ['id', 'completed_date', 'reference_number', 'status']

    def validate(self, attrs):
        attrs = super().validate(attrs)
        company = self.context['request'].company

        # Validate locations belong to same company
        if attrs.get('from_location') and attrs['from_location'].company != company:
            raise serializers.ValidationError({
                'from_location': 'From location must belong to the same company'
            })

        if attrs.get('to_location') and attrs['to_location'].company != company:
            raise serializers.ValidationError({
                'to_location': 'To location must belong to the same company'
            })
        
        if 'reference_number' not in attrs or not attrs['reference_number']:
            prefix = "MVM"
            date_str = timezone.now().strftime("%y%m%d")
            count = InventoryMovement.objects.filter(
                company=company,
                reference_number__startswith=f"{prefix}{date_str}"
            ).count()   
            attrs['reference_number'] = f"{prefix}{date_str}{str(count + 1).zfill(4)}"

        return attrs

    @transaction.atomic
    def create(self, validated_data):
        lines_data = validated_data.pop('lines', [])
        movement = super().create(validated_data)

        for line_data in lines_data:
            line_data['company'] = movement.company
            InventoryMovementLine.objects.create(
                movement=movement,
                **line_data
            )

        return movement
    
    @transaction.atomic
    def update(self, instance, validated_data):
        """
        Update an inventory movement and its related lines.
        """
        # Extract lines data from the validated data
        lines_data = validated_data.pop('lines', [])
        
        # Update the movement instance with the remaining data
        instance = super().update(instance, validated_data)
        
        # Delete all existing lines and create new ones
        instance.lines.all().delete()
        
        # Create new lines based on the provided data
        for line_data in lines_data:
            line_data['company'] = instance.company
            InventoryMovementLine.objects.create(
                movement=instance,
                **line_data
            )
            
        return instance


class InventoryAdjustmentLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)
    lot_number = serializers.CharField(source='lot.number', read_only=True)

    representation_fields = {
        'product': 'apps.products.api.serializers.ProductSerializer',
        'variant': 'apps.products.api.serializers.ProductVariantSerializer'
    }
    
    class Meta:
        model = InventoryAdjustmentLine
        fields = [
            'id', 'product', 'product_name', 'variant',
            'variant_name', 'lot', 'lot_number',
            'previous_quantity', 'new_quantity',
            'adjustment_quantity', 'cost_price',
            'adjustment_value'
        ]
        read_only_fields = [
            'id', 'product_name', 'variant_name',
            'lot_number', 'adjustment_quantity',
            'adjustment_value'
        ]

class InventoryAdjustmentSerializer(BaseModelSerializer):
    lines = InventoryAdjustmentLineSerializer(many=True, read_only=True)
    location_name = serializers.CharField(source='location.name', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)

    representation_fields = {
        'approved_by': 'apps.core.api.serializers.EmployeeSerializer',
        'location': 'apps.inventory.api.serializers.LocationSerializer'
    }
    
    class Meta:
        model = InventoryAdjustment
        fields = [
            'id', 'uuid', 'reference_number', 'location', 'location_name',
            'adjustment_type', 'status', 'notes', 'approved_by',
            'approved_by_name', 'approved_at', 'created_at',
            'updated_at', 'lines'
        ]
        read_only_fields = [
            'id', 'reference_number', 'location_name',
            'approved_by', 'approved_by_name', 'approved_at',
            'created_at', 'updated_at'
        ]


class InventoryAdjustmentLineCreateSerializer(BaseModelSerializer):
    class Meta:
        model = InventoryAdjustmentLine
        fields = ['product', 'variant', 'lot', 'new_quantity', 'inventory_item', 'previous_quantity', 'cost_price']
        read_only_fields = ['inventory_item', 'previous_quantity']

class InventoryAdjustmentCreateSerializer(BaseModelSerializer):
    lines = InventoryAdjustmentLineCreateSerializer(many=True)
    
    class Meta:
        model = InventoryAdjustment
        fields = ['location', 'adjustment_type', 'notes', 'lines']
        read_only_fields = ['id', 'reference_number', 'location_name', 'approved_by', 'approved_by_name', 'approved_at', 'created_at', 'updated_at']
