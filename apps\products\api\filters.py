# filters.py
from django_filters import rest_framework as filters
from apps.products.models import Product, ProductVariant, ProductCategory, ProductBundle, ProductType


class ProductTypeFilter(filters.FilterSet):
    name = filters.CharFilter(lookup_expr='icontains')
    number = filters.CharFilter(lookup_expr='icontains')
    description = filters.CharFilter(lookup_expr='icontains')
    

class ProductFilter(filters.FilterSet):
    min_price = filters.NumberFilter(
        field_name='prices__base_selling_price',
        lookup_expr='gte'
    )
    max_price = filters.NumberFilter(
        field_name='prices__base_selling_price',
        lookup_expr='lte'
    )
    currency = filters.CharFilter(
        field_name='prices__currency__code'
    )
    has_stock = filters.BooleanFilter(
        method='filter_has_stock'
    )
    min_stock = filters.NumberFilter(
        method='filter_min_stock'
    )
    category_tree = filters.NumberFilter(
        method='filter_category_tree'
    )
    warehouse = filters.NumberFilter(
        field_name='inventory_items__warehouse'
    )

    class Meta:
        model = Product
        fields = {
            'name': ['exact', 'icontains'],
            'sku': ['exact', 'icontains'],
            'barcode': ['exact'],
            'is_active': ['exact'],
            'category': ['exact', 'isnull'],
            'type': ['exact'],
            'unit_of_measure': ['exact'],
            'lot_tracking_required': ['exact']
        }

    def filter_has_stock(self, queryset, name, value):
        if value:
            return queryset.filter(inventory_items__quantity__gt=0).distinct()
        return queryset.exclude(inventory_items__quantity__gt=0).distinct()

    def filter_min_stock(self, queryset, name, value):
        return queryset.filter(inventory_items__quantity__gte=value).distinct()

    def filter_category_tree(self, queryset, name, value):
        try:
            category = ProductCategory.objects.get(pk=value)
            descendant_ids = category.get_descendants(include_self=True).values_list('id', flat=True)
            return queryset.filter(category__in=descendant_ids)
        except ProductCategory.DoesNotExist:
            return queryset.none()

class ProductVariantFilter(filters.FilterSet):
    min_stock = filters.NumberFilter(
        method='filter_min_stock'
    )
    warehouse = filters.NumberFilter(
        field_name='inventory_items__warehouse'
    )
    has_stock = filters.BooleanFilter(
        method='filter_has_stock'
    )

    class Meta:
        model = ProductVariant
        fields = {
            'name': ['exact', 'icontains'],
            'sku_suffix': ['exact', 'icontains'],
            'barcode': ['exact'],
            'is_active': ['exact'],
            'product': ['exact'],
            'product__category': ['exact']
        }

    def filter_has_stock(self, queryset, name, value):
        if value:
            return queryset.filter(inventory_items__quantity__gt=0).distinct()
        return queryset.exclude(inventory_items__quantity__gt=0).distinct()

    def filter_min_stock(self, queryset, name, value):
        return queryset.filter(inventory_items__quantity__gte=value).distinct()

class ProductCategoryFilter(filters.FilterSet):
    descendants = filters.BooleanFilter(method='filter_descendants')
    has_products = filters.BooleanFilter(method='filter_has_products')

    class Meta:
        model = ProductCategory
        fields = {
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
            'parent': ['exact', 'isnull'],
            'type': ['exact'],
            'can_be_sold': ['exact']
        }

    def filter_descendants(self, queryset, name, value):
        if not value or not self.request.query_params.get('parent'):
            return queryset
        try:
            parent = ProductCategory.objects.get(
                pk=self.request.query_params['parent']
            )
            return queryset.filter(
                pk__in=parent.get_descendants(include_self=True)
            )
        except ProductCategory.DoesNotExist:
            return queryset.none()

    def filter_has_products(self, queryset, name, value):
        if value:
            return queryset.filter(products__isnull=False).distinct()
        return queryset.filter(products__isnull=True)

class ProductBundleFilter(filters.FilterSet):
    min_price = filters.NumberFilter(
        field_name='prices__base_selling_price',
        lookup_expr='gte'
    )
    max_price = filters.NumberFilter(
        field_name='prices__base_selling_price',
        lookup_expr='lte'
    )
    currency = filters.CharFilter(
        field_name='prices__currency__code'
    )
    has_components = filters.BooleanFilter(method='filter_has_components')

    class Meta:
        model = ProductBundle
        fields = {
            'name': ['exact', 'icontains'],
            'sku': ['exact', 'icontains'],
            'barcode': ['exact'],
            'is_active': ['exact'],
            'price_override': ['exact']
        }

    def filter_has_components(self, queryset, name, value):
        if value:
            return queryset.filter(components__isnull=False).distinct()
        return queryset.filter(components__isnull=True)