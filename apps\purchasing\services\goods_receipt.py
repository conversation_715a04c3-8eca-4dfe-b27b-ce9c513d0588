# purchasing/services/goods_receipt.py
from typing import Dict, List
from django.db import transaction
from django.db.models import F
from ..models import (
    PurchaseOrder, GoodsReceipt, GoodsReceiptLine
)
from apps.core.models import Employee
from apps.inventory.models import InventoryItem, Lot
from apps.operations.services.movement import MovementService
from .base import BasePurchasingService
from django.db.models import Sum
from django.utils import timezone
from utils.enums import PurchaseOrderStatus, GoodsReceiptStatus
from django.core.exceptions import ValidationError
from decimal import Decimal

class GoodsReceiptService(BasePurchasingService):
    @transaction.atomic
    def create_receipt(
        self,
        po_id: int,
        lines: List[Dict],
        reference_number: str,
        notes: str = None
    ) -> GoodsReceipt:
        po = PurchaseOrder.objects.get(
            company=self.company,
            id=po_id
        )
        employee = Employee.objects.get(
            company=self.company,
            user=self.user
        )
        
        if po.status == PurchaseOrderStatus.CANCELLED.name:
            raise ValueError("Cannot receive cancelled orders")
            
        receipt = GoodsReceipt.objects.create(
            company=self.company,
            purchase_order=po,
            reference_number=reference_number,
            notes=notes,
            receipt_date=timezone.now().date(),
            receiver=employee,
            warehouse=po.receiving_location.warehouse,
        )
        
        movement_service = MovementService(self.user, self.company)
        
        # Process each receipt line
        for line in lines:
            po_line = po.lines.get(id=line['purchase_order_line_id'])
            
            # Convert quantity to Decimal
            quantity = Decimal(str(line['quantity']))
            
            # Validate quantity
            remaining_qty = po_line.quantity - po_line.received_quantity
            if quantity > remaining_qty:
                raise ValueError(
                    f"Received quantity exceeds remaining quantity for product {po_line.product.name}"
                )
            
            # Create receipt line
            receipt_line = GoodsReceiptLine.objects.create(
                company=self.company,
                receipt=receipt,
                purchase_order_line=po_line,
                quantity=quantity,
                lot_number=line.get('lot_number', ''),
                expiry_date=line.get('expiry_date')
            )
            
            # Create or update lot
            if line.get('lot_number'):
                lot = Lot.objects.create(
                    company=self.company,
                    number=line['lot_number'],
                    product=po_line.product,
                    variant=po_line.variant,
                    expiry_date=line.get('expiry_date'),
                    supplier_lot_number=line.get('supplier_lot_number')
                )
            else:
                lot = None
            
            # Create inventory movement
            movement_service.create_internal_movement(
                from_location=None,  # from_location is None for receipts
                to_location=po.receiving_location,
                lines=[{
                    'product': po_line.product,
                    'variant': po_line.variant,
                    'lot': lot,
                    'quantity': quantity,
                    'cost_price': po_line.unit_price
                }],
                reference_number=f"GR-{receipt.reference_number}"
            )
            
            # Update PO line received quantity
            po_line.received_quantity = F('received_quantity') + quantity
            po_line.save()
        
        # Update PO status
        self._update_po_status(po)
        
        return receipt

    def _update_po_status(self, po: PurchaseOrder) -> None:
        """Update PO status based on received quantities"""
        total_ordered = po.lines.aggregate(
            total=Sum('quantity')
        )['total']
        total_received = po.lines.aggregate(
            total=Sum('received_quantity')
        )['total']
        
        if total_received >= total_ordered:
            po.status = PurchaseOrderStatus.COMPLETED.name
        elif total_received > 0:
            po.status = PurchaseOrderStatus.PARTIAL.name
        po.save()

    @transaction.atomic
    def send_to_quality_control(self, receipt_id: int) -> GoodsReceipt:
        """Mark receipt as pending quality control"""
        receipt = GoodsReceipt.objects.get(
            company=self.company,
            id=receipt_id,
            status=GoodsReceiptStatus.STARTED.name
        )
        
        receipt.status = GoodsReceiptStatus.QC_PENDING.name
        receipt.save()
        return receipt

    @transaction.atomic
    def complete_quality_control(
        self,
        receipt_id: int,
        quality_status: str,
        notes: str = None
    ) -> GoodsReceipt:
        """Complete quality control check"""
        receipt = GoodsReceipt.objects.get(
            company=self.company,
            id=receipt_id,
            status=GoodsReceiptStatus.QC_PENDING.name
        )
        
        receipt.quality_status = quality_status
        receipt.quality_control_by = self.user.employee
        receipt.quality_control_date = timezone.now()
        if notes:
            receipt.notes = f"{receipt.notes}\nQC Notes: {notes}"
        receipt.status = GoodsReceiptStatus.COMPLETED.name
        receipt.save()
        return receipt

    @transaction.atomic
    def complete_receipt(self, receipt_id: int) -> GoodsReceipt:
        """Complete the goods receipt"""
        receipt = GoodsReceipt.objects.get(
            company=self.company,
            id=receipt_id
        )
        
        if receipt.quality_control_required and receipt.status != GoodsReceiptStatus.QC_PENDING.name:
            raise ValidationError("Quality control check required before completion")
        
        receipt.status = GoodsReceiptStatus.COMPLETED.name
        receipt.save()
        return receipt

    @transaction.atomic
    def cancel_receipt(
        self,
        receipt_id: int,
        reason: str = ""
    ) -> GoodsReceipt:
        """Cancel the goods receipt"""
        receipt = GoodsReceipt.objects.get(
            company=self.company,
            id=receipt_id
        )
        
        if receipt.status == GoodsReceiptStatus.COMPLETED.name:
            raise ValidationError("Cannot cancel completed receipts")
        
        receipt.status = GoodsReceiptStatus.CANCELLED.name
        if reason:
            receipt.notes = f"{receipt.notes}\nCancellation reason: {reason}"
        receipt.save()
        return receipt