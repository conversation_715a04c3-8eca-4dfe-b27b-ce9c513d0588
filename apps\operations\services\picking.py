from .base import BaseOperationService
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.db.models import F, Sum
from apps.base.models import CompanyFKRel
from apps.inventory.models import Location, InventoryItem
from apps.sales.models import SalesOrder, SalesOrderLine

from ..models import PickingTask, PickingTaskLine
from utils.enums import SalesOrderStatus, PickingTaskStatus
from django.utils import timezone



class PickingService(BaseOperationService):
    @transaction.atomic
    def create_picking_task(
        self,
        sales_order: SalesOrder,
        priority: int = 0
    ) -> PickingTask:
        """Creates picking task for a sales order with optimized picking locations"""
        
        # Create main picking task
        picking_task = PickingTask.objects.create(
            company=self.company,
            sales_order=sales_order,
            priority=priority,
            warehouse=sales_order.fulfilling_warehouse
        )

        # Create picking lines with optimized location selection
        for order_line in sales_order.lines.all():
            self._create_picking_lines(picking_task, order_line)

        return picking_task

    def _create_picking_lines(
        self,
        picking_task: PickingTask,
        order_line: SalesOrderLine
    ) -> None:
        """
        Creates picking lines with optimized location selection
        Uses FIFO and minimizes picking locations
        """
        remaining_quantity = order_line.quantity
        
        # Get available inventory items, ordered by FIFO
        inventory_items = InventoryItem.objects.filter(
            company=self.company,
            product=order_line.product,
            variant=order_line.variant,
            reserved_quantity__gt=0
        ).order_by('created_at')

        for item in inventory_items:
            if remaining_quantity <= 0:
                break

            pick_quantity = min(
                remaining_quantity,
                item.reserved_quantity
            )

            PickingTaskLine.objects.create(
                company=self.company,
                picking_task=picking_task,
                sales_order_line=order_line,
                location=item.location,
                quantity=pick_quantity,
                lot=item.lot
            )

            remaining_quantity -= pick_quantity

    @transaction.atomic
    def assign_picker(
        self,
        picking_task_id: int,
        employee_id: int
    ) -> PickingTask:
        """Assigns a picker to the picking task"""
        picking_task = PickingTask.objects.get(
            company=self.company,
            id=picking_task_id
        )
        
        if picking_task.status != PickingTaskStatus.PENDING.name:
            raise ValueError("Can only assign pending picking tasks")

        picking_task.assigned_to_id = employee_id
        picking_task.save()

        return picking_task

    @transaction.atomic
    def start_picking(self, picking_task_id: int) -> PickingTask:
        """Start the picking process"""
        picking_task = PickingTask.objects.get(
            company=self.company,
            id=picking_task_id
        )
        if picking_task.status != PickingTaskStatus.PENDING.name:
            raise ValueError("Can only start pending picking tasks")
            
        if not picking_task.assigned_to:
            raise ValueError("Picking task must be assigned before starting")

        picking_task.status = PickingTaskStatus.IN_PROGRESS.name
        picking_task.started_at = timezone.now()
        picking_task.save()

        return picking_task

    @transaction.atomic
    def record_pick(
        self,
        picking_line_id: int,
        picked_quantity: Decimal
    ) -> PickingTaskLine:
        """Record picked quantity for a picking line"""
        picking_line = PickingTaskLine.objects.get(
            company=self.company,
            id=picking_line_id
        )
        
        if picking_line.picking_task.status != PickingTaskStatus.IN_PROGRESS.name:
            raise ValueError("Can only pick from in-progress tasks")

        if picked_quantity > (picking_line.quantity - picking_line.picked_quantity):
            raise ValueError("Cannot pick more than remaining quantity")

        picking_line.picked_quantity = (picking_line.picked_quantity or 0) + picked_quantity
        picking_line.save()

        # Refresh to get the latest state
        picking_line.refresh_from_db()

        # Check if picking task is complete
        self._check_picking_completion(picking_line.picking_task)

        return picking_line

    def _check_picking_completion(self, picking_task: PickingTask) -> None:
        """Check if all lines are picked and update task status"""
        incomplete_lines = picking_task.lines.filter(
            picked_quantity__lt=F('quantity')
        ).exists()

        if not incomplete_lines:
            picking_task.status = PickingTaskStatus.COMPLETED.name
            picking_task.completed_at = timezone.now()
            picking_task.save()

            # Update sales order status
            picking_task.sales_order.status = SalesOrderStatus.PACKED.name
            picking_task.sales_order.save()

    def get_picking_route(self, picking_task_id: int) -> List[Dict]:
        """
        Generate optimized picking route based on warehouse layout
        Returns ordered list of locations to visit
        """
        picking_task = PickingTask.objects.get(
            company=self.company,
            id=picking_task_id
        )

        # Group picking lines by location
        picking_lines = picking_task.lines.filter(
            picked_quantity__lt=F('quantity')
        ).select_related('location')

        # TODO: Here you would implement your warehouse-specific routing algorithm
        # This is a simple example that orders by location number
        route = []
        for line in picking_lines.order_by('location__code'):
            route.append({
                'location': line.location,
                'picking_line_id': line.id,
                'product': line.sales_order_line.product,
                'quantity': line.quantity - line.picked_quantity
            })

        return route