# authentication/middleware.py
from django.http import HttpResponseForbidden
from apps.core.models import ActiveCompanySession, Employee
from rest_framework_simplejwt.authentication import JWTAuthentication
from django.core.exceptions import PermissionDenied

class ActiveCompanyMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response
        self.jwt_auth = JWTAuthentication()

    def __call__(self, request):
        if hasattr(request, '_is_swagger_request'):
            return self.get_response(request)

        # Try to authenticate with JWT if not already authenticated
        if not request.user.is_authenticated:
            try:
                auth_result = self.jwt_auth.authenticate(request)
                if auth_result:
                    request.user = auth_result[0]
            except:
                return self.get_response(request)

        if not request.user.is_authenticated:
            return self.get_response(request)

        try:
            active_session = ActiveCompanySession.objects.select_related('company').get(
                user=request.user,
                is_active=True
            )
            
            is_active_employee = Employee.objects.filter(
                user=request.user,
                company=active_session.company,
                is_active=True
            ).exists()
            
            if not is_active_employee:
                active_session.is_active = False
                active_session.save()
                raise PermissionDenied("No active company access")
                
            request.company = active_session.company
            
        except ActiveCompanySession.DoesNotExist:
            request.company = None
            
        response = self.get_response(request)
        return response