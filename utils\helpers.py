from decimal import Decimal


def get_accurate_tax_rate(product, counter_party):
    from utils.enums import CountryCodes, VATZone
    counter_party_vat_zone = counter_party.vat_zone
    # First try to get country-specific rate
    country_specific_rates = product.type.country_specific_rates
    if country_specific_rates and counter_party_vat_zone in country_specific_rates.keys():
        tax_rate = Decimal(str(country_specific_rates[counter_party_vat_zone]))
        if tax_rate is None:
            eu_country_codes = CountryCodes.eu_members()
            if counter_party_vat_zone in eu_country_codes:
                tax_rate = Decimal(str(product.type.europe_rate))
            else:
                tax_rate = Decimal(str(product.type.export_rate))
    
    # Fall back to regional rates
    elif counter_party_vat_zone == VATZone.DOMESTIC.name:
        tax_rate = Decimal(str(product.type.domestic_rate))
    elif counter_party_vat_zone == VATZone.DOMESTIC_EXCEPTION.name:
        tax_rate = Decimal(str(product.type.domestic_exception_rate))
    elif counter_party_vat_zone == VATZone.EUROPE.name:
        tax_rate = Decimal(str(product.type.europe_rate))
    elif counter_party_vat_zone == VATZone.EXPORT.name:
        tax_rate = Decimal(str(product.type.export_rate))
    else:
        raise ValueError(f"Unknown VAT zone: {counter_party_vat_zone}")
    
    return tax_rate