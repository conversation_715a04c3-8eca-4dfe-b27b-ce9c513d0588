# purchasing/serializers.py
from rest_framework import serializers
from utils.serializers import BaseModelSerializer
from apps.purchasing.models import (
    Supplier, SupplierProduct, PurchaseOrder,
    PurchaseOrderLine, GoodsReceipt, GoodsReceiptLine, SupplierContact, SupplierCategory,
    SupplierInvoice, SupplierInvoiceLine, SupplierAddress
)
from apps.products.models import Product, ProductVariant
from apps.core.api.serializers import ( 
    PaymentTermSerializer, DeliveryTermSerializer, CurrencySerializer,
    CustomFieldValueSerializer, DeliveryMethodSerializer
)
from django.db import transaction
from decimal import Decimal
from django.utils import timezone
from apps.purchasing.services.goods_receipt import GoodsReceiptService
from apps.core.models import CustomFieldValue

class SupplierAddressSerializer(BaseModelSerializer):
    """
    Serializer for supplier addresses with type (warehouse, office, etc)
    """
    class Meta:
        model = SupplierAddress
        fields = [
            'id', 'uuid', 'address_type', 'name', 'street_address1', 
            'street_address2', 'city', 'state', 'zip_code',
            'country', 'is_default', 'notes'
        ]
        read_only_fields = ['id']

class SupplierContactSerializer(BaseModelSerializer):
    """
    Serializer for supplier contacts with role and contact details
    """
    class Meta:
        model = SupplierContact
        fields = [
            'id', 'uuid', 'name', 'role', 'email', 'phone',
            'mobile', 'is_primary', 'notes'
        ]
        read_only_fields = ['id']


    def validate(self, data):
        """
        Check that at least one contact method is provided
        """
        if not any([data.get('email'), data.get('phone'), data.get('mobile')]):
            raise serializers.ValidationError(
                "At least one contact method (email, phone, or mobile) is required"
            )
        return data

class SupplierProductSerializer(BaseModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)  # For existing components
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), required=False)
    variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False, allow_null=True)
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )
    currency_code = serializers.CharField(
        source='supplier.currency.currency',
        read_only=True
    )
    product_sku = serializers.CharField(
        source='product.sku',
        read_only=True
    )

    representation_fields = {
        'product': 'apps.products.api.serializers.ProductSerializer',
        'variant': 'apps.products.api.serializers.ProductVariantSerializer',
    }

    class Meta:
        model = SupplierProduct
        fields = [
            'id', 'uuid', 'product', 'product_name', 'product_sku',
            'variant', 'variant_name', 'supplier_sku',
            'price', 'previous_price', 'min_order_quantity', 'lead_time_days',
            'is_preferred', 'currency_code'
        ]
        read_only_fields = ['id', 'currency_code']

class UpdateSupplierProductSerializer(serializers.Serializer):
    products = SupplierProductSerializer(many=True)
    

class SupplierCategorySerializer(BaseModelSerializer):
    supplier_count = serializers.SerializerMethodField()

    def get_supplier_count(self, obj):
        return obj.suppliers.count()

    class Meta:
        model = SupplierCategory
        fields = [
            'id', 'uuid', 'name', 'number', 'description', 'supplier_count'
        ]
        read_only_fields = ['id']

    def validate_number(self, value):
        if SupplierCategory.objects.filter(
            company=self.context['request'].company,
            number=value
        ).exists():
            raise serializers.ValidationError("Category number must be unique.")
        return value

class SupplierSerializer(BaseModelSerializer):
    products = SupplierProductSerializer(many=True, read_only=True)
    contacts = SupplierContactSerializer(many=True, read_only=True)
    addresses = SupplierAddressSerializer(many=True, read_only=True)
    performance_metrics = serializers.SerializerMethodField()
    payment_terms_name = serializers.CharField(
        source='payment_terms.name',
        read_only=True
    )
    delivery_terms_name = serializers.CharField(
        source='delivery_terms.name',
        read_only=True
    )
    currency_code = serializers.CharField(
        source='currency.currency',
        read_only=True
    )
    category_name = serializers.CharField(source='category.name', read_only=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )

    representation_fields = {
        'payment_terms': PaymentTermSerializer,
        'delivery_terms': DeliveryTermSerializer,
        'currency': CurrencySerializer,
        'category': SupplierCategorySerializer,
    }

    class Meta:
        model = Supplier
        fields = [
            'id', 'uuid', 'name', 'number', 'tax_number', 'payment_terms',
            'email', 'phone', 'street_address1', 'street_address2', 'city', 'state', 
            'zip_code', 'country', 'vat_zone', 'currency', 'notes', 'products', 'contacts',
            'performance_metrics', 'payment_terms_name', 'delivery_terms_name', 'currency_code',
            'category', 'category_name', 'addresses', 'custom_fields', 'delivery_terms'
        ]
        read_only_fields = ['id']

    def get_performance_metrics(self, obj):
        service = self.context.get('supplier_service')
        if service:
            performance = service.evaluate_supplier_performance(obj.id)
            return performance.get('metrics')
        return None

    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        supplier = super().create(validated_data)
        
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=supplier.company,
                content_object=supplier,
                **field_data
            )
        
        return supplier

    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        supplier = super().update(instance, validated_data)
        
        if custom_fields_data:
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=supplier.company,
                    content_object=supplier,
                    **field_data
                )
        
        return supplier

class PurchaseOrderLineSerializer(BaseModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )
    total_amount = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        read_only=True,
        source='get_total_amount'
    )
    quantity = serializers.DecimalField(max_digits=15, decimal_places=2)
    unit_price = serializers.DecimalField(max_digits=15, decimal_places=2)
    tax_rate = serializers.DecimalField(max_digits=5, decimal_places=2, required=False, default=0)
    discount = serializers.DecimalField(max_digits=5, decimal_places=2, required=False, default=0)

    class Meta:
        model = PurchaseOrderLine
        fields = [
            'id', 'uuid', 'product', 'product_name', 'variant', 'discount',
            'variant_name', 'quantity', 'received_quantity',
            'unit_price', 'tax_rate', 'expected_delivery_date', 'total_amount'
        ]
        read_only_fields = ['id', 'received_quantity']

    def validate_quantity(self, value):
        """Validate quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        return value

    def validate_price(self, value):
        """Validate price is not negative"""
        if value < 0:
            raise serializers.ValidationError("Price cannot be negative")
        return value
    
    def validate_discount(self, value):
        """Validate discount is not negative"""
        if value < 0:
            raise serializers.ValidationError("Discount cannot be negative")
        return value

class PurchaseOrderSerializer(BaseModelSerializer):
    supplier_name = serializers.CharField(
        source='supplier.name',
        read_only=True
    )
    receiving_location_name = serializers.CharField(
        source='receiving_location.name',
        read_only=True
    )
    lines = PurchaseOrderLineSerializer(many=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )

    representation_fields = {
        'currency': CurrencySerializer,
        'payment_term': PaymentTermSerializer,
        'delivery_term': DeliveryTermSerializer,
        'delivery_method': DeliveryMethodSerializer
    }

    class Meta:
        model = PurchaseOrder
        fields = [
            'id', 'uuid', 'number', 'supplier', 'supplier_name',
            'order_date', 'expected_delivery_date', 'status',
            'receiving_location', 'receiving_location_name',
            'notes', 'total_amount', 'lines', 'our_reference',
            'your_reference', 'currency', 'payment_term', 'delivery_term', 'currency_rate',
            'delivery_method', 'custom_fields', 'prices_include_tax'
        ]
        read_only_fields = ['id', 'number', 'total_amount']
    
    def validate_receiving_location(self, value):
        """
        Validate that the receiving location is marked as a receiving location
        """
        if not value.is_receiving:
            raise serializers.ValidationError("Location must be a receiving location.")
        return value

    @transaction.atomic
    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        lines_data = validated_data.pop('lines', [])
        service = self.context['order_service']
        
        order = service.create_purchase_order(
            supplier_id=validated_data['supplier'].id,
            receiving_location_id=validated_data['receiving_location'].id,
            lines=[{
                'product_id': line['product'].id,
                'variant_id': line.get('variant', {}).get('id'),
                'quantity': line['quantity'],
                'unit_price': line['unit_price'],
                'tax_rate': line.get('tax_rate', 0),
                'expected_delivery_date': line.get('expected_delivery_date'),
            } for line in lines_data],
            notes=validated_data.get('notes'),
            expected_delivery_date=validated_data.get('expected_delivery_date'),
            currency=validated_data.get('currency'),
            payment_term=validated_data.get('payment_term'),
            delivery_term=validated_data.get('delivery_term'),
            currency_rate=validated_data.get('currency_rate'),
            our_reference=validated_data.get('our_reference'),
            your_reference=validated_data.get('your_reference'),
            delivery_method=validated_data.get('delivery_method'),
        )
        
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=order.company,
                content_object=order,
                **field_data
            )
        
        return order

    @transaction.atomic
    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        lines_data = validated_data.pop('lines', [])
        print(lines_data)
        
        # Update the purchase order instance with header data
        order = super().update(instance, validated_data)
        
        # Handle custom fields
        if custom_fields_data:
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=order.company,
                    content_object=order,
                    **field_data
                )
        
        # Handle lines if provided
        if lines_data:
            # Create new lines
            line_ids = [line.get('id') for line in lines_data]
            for line_data in lines_data:
                line_id = line_data.pop('id', None)
                if line_id:
                    line = PurchaseOrderLine.objects.get(id=line_id)
                    for key, value in line_data.items():
                        setattr(line, key, value)
                    line.save()
                else:
                    PurchaseOrderLine.objects.create(
                        company=order.company,
                        purchase_order=order,
                        **line_data
                    )
            # Delete lines that are not in the request
            PurchaseOrderLine.objects.filter(
                company=order.company,
                purchase_order=order,
            ).exclude(id__in=line_ids).delete()
            
            order.save()
        
        return order

class GoodsReceiptLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='purchase_order_line.product.name',
        read_only=True
    )
    ordered_quantity = serializers.DecimalField(
        source='purchase_order_line.quantity',
        max_digits=15,
        decimal_places=2,
        read_only=True
    )
    quantity = serializers.DecimalField(max_digits=15, decimal_places=2)

    class Meta:
        model = GoodsReceiptLine
        fields = [
            'id', 'uuid', 'purchase_order_line', 'product_name',
            'ordered_quantity', 'quantity', 'lot_number',
            'expiry_date'
        ]
        read_only_fields = ['id']

    def validate_quantity(self, value):
        """Validate quantity is positive"""
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        return value

class GoodsReceiptSerializer(BaseModelSerializer):
    lines = GoodsReceiptLineSerializer(many=True)
    receiver_name = serializers.CharField(
        source='receiver.get_full_name',
        read_only=True
    )
    supplier_name = serializers.CharField(
        source='purchase_order.supplier.name',
        read_only=True
    )
    quality_control_by_name = serializers.CharField(
        source='quality_control_by.full_name',
        read_only=True,
        allow_null=True
    )
    reference_number = serializers.CharField(
        required=False,
        allow_null=True
    )

    class Meta:
        model = GoodsReceipt
        fields = [
            'id', 'uuid', 'purchase_order', 'receipt_date',
            'reference_number', 'notes', 'receiver',
            'receiver_name', 'supplier_name', 'lines',
            'status', 'quality_control_required',
            'quality_control_by', 'quality_control_by_name',
            'quality_control_date', 'quality_status'
        ]
        read_only_fields = [
            'id', 'uuid', 'receiver', 'quality_control_by',
            'quality_control_date'
        ]

    def validate(self, attrs):
        attrs = super().validate(attrs)
        company = self.context['request'].company
        if 'reference_number' not in attrs or not attrs['reference_number']:
            prefix = "MVM"
            date_str = timezone.now().strftime("%y%m%d")
            count = GoodsReceipt.objects.filter(
                company=company,
                reference_number__startswith=f"{prefix}{date_str}"
            ).count()   
            attrs['reference_number'] = f"{prefix}{date_str}{str(count + 1).zfill(4)}"
        return attrs

    @transaction.atomic
    def create(self, validated_data):
        lines_data = validated_data.pop('lines', [])
        service = GoodsReceiptService(
            self.context['request'].user,
            self.context['request'].company
        )
        
        receipt = service.create_receipt(
            po_id=validated_data['purchase_order'].id,
            lines=[{
                'purchase_order_line_id': line['purchase_order_line'].id,
                'quantity': line['quantity'],
                'lot_number': line.get('lot_number', ''),
                'expiry_date': line.get('expiry_date')
            } for line in lines_data],
            reference_number=validated_data.get('reference_number'),
            notes=validated_data.get('notes')
        )
        return receipt

class SupplierInvoiceLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)

    class Meta:
        model = SupplierInvoiceLine
        fields = [
            'id', 'uuid', 'product', 'product_name', 'variant', 'variant_name',
            'description', 'quantity', 'unit_price', 'tax_rate',
            'net_amount', 'tax_amount', 'total_amount'
        ]
        read_only_fields = ['id', 'net_amount', 'tax_amount', 'total_amount']

class SupplierInvoiceFromOrderSerializer(serializers.Serializer):
    purchase_order_id = serializers.IntegerField()
    invoice_number = serializers.CharField()
    invoice_date = serializers.DateField()
    use_received_quantities = serializers.BooleanField(default=True)

class SupplierInvoiceMarkAsPaidSerializer(serializers.Serializer):
    payment_date = serializers.DateField()
    payment_reference = serializers.CharField()
    partial = serializers.BooleanField(default=False)

class SupplierInvoiceSerializer(BaseModelSerializer):
    lines = SupplierInvoiceLineSerializer(many=True, read_only=True)
    supplier_name = serializers.CharField(source='supplier.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )

    class Meta:
        model = SupplierInvoice
        fields = [
            'id', 'uuid', 'number', 'invoice_number', 'supplier', 'supplier_name',
            'purchase_order', 'invoice_date', 'due_date', 'currency',
            'currency_code', 'currency_rate', 'total_net_amount',
            'total_tax_amount', 'total_amount', 'status', 'final_paid_date',
            'payment_reference', 'notes', 'lines', 'custom_fields',
            'our_reference', 'your_reference', 'delivery_term', 'delivery_method',
            'payment_term', 
        ]
        read_only_fields = [
            'id', 'number', 'total_net_amount', 'total_tax_amount',
            'total_amount'
        ]

    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        invoice = super().create(validated_data)
        
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=invoice.company,
                content_object=invoice,
                **field_data
            )
        
        return invoice

    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        invoice = super().update(instance, validated_data)
        
        if custom_fields_data:
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=invoice.company,
                    content_object=invoice,
                    **field_data
                )
        
        return invoice