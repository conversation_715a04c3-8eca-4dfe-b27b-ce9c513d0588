from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(
    r'locations',
    views.LocationViewSet,
    basename='location'
)
router.register(
    r'lots',
    views.LotViewSet,
    basename='lot'
)
router.register(
    r'items',
    views.InventoryItemViewSet,
    basename='inventory-item'
)
router.register(
    r'movements',
    views.InventoryMovementViewSet,
    basename='inventory-movement'
)
router.register(
    r'adjustments',
    views.InventoryAdjustmentViewSet,
    basename='inventory-adjustment'
)

urlpatterns = [
    path('inventory/', include(router.urls)),
]