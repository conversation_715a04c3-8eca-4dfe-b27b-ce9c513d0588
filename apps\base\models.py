from django.db import models
from uuid import uuid4
from utils.enums import InvoiceStatus, VATZone, AddressType, CountryCodes
from decimal import Decimal
from utils.helpers import get_accurate_tax_rate
from apps.base.mixins import NumberSequenceMixin

class BaseModel(models.Model):
    id = models.BigAutoField(primary_key=True, editable=False)
    uuid = models.UUIDField(default=uuid4, editable=False, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.__class__.__name__} {self.id}"
    
    class Meta:
        abstract = True



class CompanyFKRel(BaseModel):
    company = models.ForeignKey(
        'core.Company', on_delete=models.CASCADE,
        related_name="company_%(class)s",
        editable=True
    )

    class Meta:
        abstract = True

    def __str__(self):
        return f"{self.__class__.__name__} - {self.company.name} - {self.id}"



class BaseTransaction(BaseModel, NumberSequenceMixin):
    """
    Abstract base model for all transaction types (orders, invoices, etc.).
    Handles common fields and calculation of totals from transaction lines.
    """
    number = models.CharField(max_length=50)
    order_date = models.DateField()
    expected_delivery_date = models.DateField(null=True, blank=True)
    our_reference = models.CharField(max_length=50, null=True, blank=True)
    your_reference = models.CharField(max_length=50, null=True, blank=True)
    notes = models.TextField(blank=True, null=True)
    currency = models.ForeignKey('core.Currency', on_delete=models.PROTECT)
    currency_rate = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    payment_term = models.ForeignKey('core.PaymentTerm', on_delete=models.PROTECT)
    delivery_term = models.ForeignKey('core.DeliveryTerm', on_delete=models.PROTECT)
    delivery_method = models.ForeignKey('core.DeliveryMethod', on_delete=models.PROTECT)
    total_net_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_discount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    prices_include_tax = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        """
        Custom save method to calculate transaction totals from its lines.
        Only calculates totals if the transaction already exists (has pk)
        to ensure all lines are saved first.
        
        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        if not self.number:
            self.number = self.get_next_number()
        
        # First save for new transactions to ensure they exist in DB
        if not self.pk:
            super().save(*args, **kwargs)
        
        # Calculate totals only for existing transactions
        if self.pk:
            lines = self.lines.all()
            self.total_net_amount = sum(line.net_amount for line in lines)
            self.total_tax_amount = sum(line.tax_amount for line in lines)
            self.total_amount = sum(line.total_amount for line in lines)
            self.total_discount = sum(line.discount for line in lines)
            super().save(*args, **kwargs)

    class Meta: 
        abstract = True

class BaseTransactionLine(BaseModel):
    """
    Abstract base model for all transaction line items.
    Handles tax rate determination and amount calculations based on pricing rules.
    """
    product = models.ForeignKey('products.Product', on_delete=models.PROTECT)
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.PROTECT
    )
    discount = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0
    )
    description = models.CharField(max_length=255)
    quantity = models.DecimalField(max_digits=15, decimal_places=2)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2)
    net_amount = models.DecimalField(max_digits=15, decimal_places=2)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2)

    def save(self, *args, **kwargs):
        """
        Custom save method that:
        1. Determines the appropriate tax rate based on:
           - Country-specific rates (if available)
           - Regional rates (domestic, EU, export)
           - Counter party's VAT zone
        2. Calculates amounts based on whether prices include tax
        3. Updates the parent transaction
        
        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        if self.tax_rate is None:
            self.tax_rate = self.get_accurate_tax_rate(self.product, self._get_counter_party())
        else:
            # Convert existing tax_rate to Decimal if it isn't already
            self.tax_rate = Decimal(str(self.tax_rate))

        # Ensure quantity, unit_price, and discount are Decimal objects
        quantity = Decimal(str(self.quantity))
        unit_price = Decimal(str(self.unit_price))
        discount_pct = Decimal(str(self.discount))
        discount_factor = Decimal('1') - (discount_pct / Decimal('100'))
        
        prices_include_tax = self._get_includes_tax()
        # Calculate total amounts based on unit price, quantity, and discount
        if prices_include_tax:
            # Calculate the base amount with tax included
            gross_amount = quantity * unit_price
            # Apply discount to the gross amount
            discounted_gross = gross_amount * discount_factor
            # Extract the net amount and tax from the discounted gross amount
            self.net_amount = discounted_gross / (Decimal('1') + self.tax_rate / Decimal('100'))
            self.tax_amount = discounted_gross - self.net_amount
        else:
            # Calculate the base net amount without tax
            base_net = quantity * unit_price
            # Apply discount to the net amount
            self.net_amount = base_net * discount_factor
            # Calculate tax on the discounted net amount
            self.tax_amount = self.net_amount * (self.tax_rate / Decimal('100'))
        
        self.total_amount = self.net_amount + self.tax_amount
        super().save(*args, **kwargs)
        self._save_parent(*args, **kwargs)

    def _save_parent(self, *args, **kwargs):
        """
        Triggers a save on the parent transaction to recalculate totals.
        """
        parent = self._get_parent()
        parent.save()

    def _get_includes_tax(self):
        """
        Determines if prices include tax based on the parent transaction's settings.
        
        Returns:
            bool: True if prices include tax, False otherwise
        """
        parent = self._get_parent()
        return parent.prices_include_tax

    def _get_counter_party(self):
        """
        Gets the counter party (customer or supplier) from the parent transaction.
        Used for determining appropriate tax rates.
        
        Returns:
            Customer or Supplier instance
            
        Raises:
            ValueError: If parent transaction has no customer or supplier
        """
        parent = self._get_parent()
        if hasattr(parent, 'customer'):
            return parent.customer
        elif hasattr(parent, 'supplier'):
            return parent.supplier
        else:
            raise ValueError("Parent does not have a customer or supplier")
    
    def _get_parent(self):
        """
        Gets the parent transaction based on the line type.
        
        Returns:
            Transaction instance (SalesOrder, PurchaseOrder, Invoice, etc.)
        """
        from apps.sales.models import SalesInvoiceLine, SalesOrderLine
        from apps.purchasing.models import PurchaseOrderLine, SupplierInvoiceLine
        if isinstance(self, SalesInvoiceLine):
            return self.invoice
        elif isinstance(self, SupplierInvoiceLine):
            return self.invoice
        elif isinstance(self, SalesOrderLine):
            return self.sales_order
        elif isinstance(self, PurchaseOrderLine):
            return self.purchase_order

    def get_accurate_tax_rate(self, product=None):
        """
        Get the tax rate for the line item.
        """
        tax_rate = get_accurate_tax_rate(self.product)

        return Decimal(str(tax_rate))
        

    class Meta: 
        abstract = True

class BaseInvoice(BaseTransaction):
    """Abstract base model for both supplier and customer invoices"""
    invoice_date = models.DateField()
    due_date = models.DateField()
    final_paid_date = models.DateField(null=True, blank=True)
    payment_reference = models.CharField(max_length=100, blank=True, null=True)
    status = models.CharField(max_length=100, default=InvoiceStatus.DRAFT.name, choices=InvoiceStatus.choices())

    class Meta:
        abstract = True



class RelationBase(BaseModel): 
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=50)
    tax_number = models.CharField(max_length=50, blank=True)
    payment_terms = models.ForeignKey(
        'core.PaymentTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)ss'
    )
    delivery_terms = models.ForeignKey(
        'core.DeliveryTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)ss'
    )
    language = models.ForeignKey(
        'core.Language',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)ss'
    )
    vat_zone = models.CharField(max_length=255, blank=True, choices=VATZone.choices())
    currency = models.ForeignKey(
        'core.Currency',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='%(class)ss'
    )
    email = models.EmailField()
    phone = models.CharField(max_length=50)
    street_address1 = models.TextField()
    street_address2 = models.TextField(blank=True)
    city = models.CharField(max_length=255)
    state = models.CharField(max_length=255)
    zip_code = models.CharField(max_length=50)
    country = models.CharField(max_length=255)
    is_active = models.BooleanField(default=True)
    notes = models.TextField(blank=True)

    class Meta:
        abstract = True


class ContactBase(BaseModel):
    name = models.CharField(max_length=255)
    email = models.EmailField()
    phone = models.CharField(max_length=50)
    mobile = models.CharField(max_length=50, blank=True)
    role = models.CharField(max_length=50)
    notes = models.TextField(blank=True, null=True)
    is_primary = models.BooleanField(default=False)

    class Meta:
        abstract = True



class AddressBase(BaseModel):
    name = models.CharField(max_length=255)
    address_type = models.CharField(max_length=50, choices=AddressType.choices())
    street_address1 = models.TextField()
    street_address2 = models.TextField(blank=True)
    city = models.CharField(max_length=255)
    state = models.CharField(max_length=255)
    zip_code = models.CharField(max_length=50)
    country = models.CharField(max_length=255)
    notes = models.TextField(blank=True, null=True)
    is_default = models.BooleanField(default=False)

    class Meta:
        abstract = True

class BasePriceHistory(BaseModel):
    """
    Abstract base class for tracking price history across the system
    """
    price_valid_from = models.DateField()
    price_valid_to = models.DateField(null=True, blank=True)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    currency = models.ForeignKey(
        'core.Currency',
        on_delete=models.PROTECT
    )
    changed_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.PROTECT,
        related_name='%(class)s_changes',
        blank=True,
        null=True
    )
    change_reason = models.CharField(max_length=255, blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
        ordering = ['-price_valid_from', '-id']

class CategoryBase(CompanyFKRel):
    """
    Abstract base class for hierarchical category structures across the system.
    Provides common functionality for category trees with parent-child relationships.
    """
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=50)
    description = models.TextField(blank=True)
    parent = models.ForeignKey(
        'self',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='children'
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        abstract = True
    
    def get_ancestors(self, include_self=False):
        """
        Get all ancestors of this category, ordered from root to direct parent.
        """
        if self.parent is None:
            if include_self:
                return type(self).objects.filter(pk=self.pk)
            else:
                return type(self).objects.none()
        
        result = self.parent.get_ancestors(include_self=True)
        if include_self:
            result = list(result) + [self]
            return result
        return result
    
    def get_descendants(self, include_self=False):
        """
        Get all descendants of this category in tree order.
        """
        model = type(self)
        descendants = model.objects.none()
        
        if include_self:
            descendants = model.objects.filter(pk=self.pk)
        
        # Get all direct children
        children = model.objects.filter(parent=self)
        
        # For each child, add their descendants recursively
        for child in children:
            descendants = descendants | model.objects.filter(pk=child.pk) | child.get_descendants()
        
        return descendants
    
    def get_children(self):
        """
        Get all direct children of this category.
        """
        return type(self).objects.filter(parent=self)
    
    def __str__(self):
        return f"{self.name}"

