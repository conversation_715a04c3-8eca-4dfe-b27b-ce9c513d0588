from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from . import views

router = DefaultRouter()
router.register(
    r'product-categories',
    views.ProductCategoryViewSet,
    basename='product-category'
)
router.register(
    r'products',
    views.ProductViewSet,
    basename='product'
)
router.register(
    r'variants',
    views.ProductVariantViewSet,
    basename='product-variant'
)
router.register(
    r'bundles',
    views.ProductBundleViewSet,
    basename='product-bundle'
)
router.register(
    r'types',
    views.ProductTypeViewSet,
    basename='product-type'
)


router.register(
    r'price-history',
    views.PriceHistoryViewSet,
    basename='price-history'
)
router.register(
    r'prices',
    views.PriceViewSet,
    basename='price'
)


urlpatterns = [
    path('products/', include(router.urls)),
]