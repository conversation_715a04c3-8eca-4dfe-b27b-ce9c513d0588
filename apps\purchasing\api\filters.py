from apps.purchasing.models import (
    Supplier, SupplierProduct, PurchaseOrder, GoodsReceipt, SupplierCategory
)
from django_filters import rest_framework as filters

class SupplierFilter(filters.FilterSet):
    min_payment_terms = filters.NumberFilter(
        field_name='payment_terms',
        lookup_expr='gte'
    )
    max_payment_terms = filters.NumberFilter(
        field_name='payment_terms',
        lookup_expr='lte'
    )

    class Meta:
        model = Supplier
        fields = {
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
            'currency': ['exact'],
            'is_active': ['exact'],
            'category': ['exact'],
        }


class PurchaseOrderFilter(filters.FilterSet):
    min_total = filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='gte'
    )
    max_total = filters.NumberFilter(
        field_name='total_amount',
        lookup_expr='lte'
    )
    order_date_from = filters.DateFilter(
        field_name='order_date',
        lookup_expr='gte'
    )
    order_date_to = filters.DateFilter(
        field_name='order_date',
        lookup_expr='lte'
    )

    class Meta:
        model = PurchaseOrder
        fields = {
            'number': ['exact', 'icontains'],
            'supplier': ['exact'],
            'status': ['exact'],
            'expected_delivery_date': ['exact', 'gte', 'lte']
        }


class SupplierCategoryFilter(filters.FilterSet):
    class Meta:
        model = SupplierCategory
        fields = {
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
        }