class NumberSequenceMixin:
    def get_next_number(self):
        from apps.core.models import NumberSequence  # Import here to avoid circular imports
        
        sequence_type = self._meta.model_name
        try:
            sequence = NumberSequence.objects.get(
                company=self.company,
                sequence_type=sequence_type
            )
            next_number = sequence.next_number
            
            # Try to extract the numeric part from the end of the string
            numeric_part = ''.join(filter(str.isdigit, next_number))
            if numeric_part:
                # Get the prefix (everything before the numeric part)
                prefix = next_number[:-len(numeric_part)]
                # Increment the number
                new_number = str(int(numeric_part) + 1)
                # Maintain the same width with leading zeros if present
                if len(numeric_part) > len(new_number):
                    new_number = new_number.zfill(len(numeric_part))
                next_number = f"{prefix}{new_number}"
            else:
                # If no numeric part found, just increment the whole thing
                next_number = str(int(next_number) + 1)
            
            sequence.next_number = next_number
            sequence.save()
            return next_number
            
        except NumberSequence.DoesNotExist:
            # If no sequence exists, create one starting from 1001
            next_num = '1001'
            
            NumberSequence.objects.create(
                company=self.company,
                sequence_type=sequence_type,
                next_number=str(int(next_num) + 1)
            )
            return next_num 