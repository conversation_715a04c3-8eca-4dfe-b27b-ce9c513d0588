from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(
    r'suppliers',
    views.SupplierViewSet,
    basename='supplier'
)
router.register(
    r'purchase-orders',
    views.PurchaseOrderViewSet,
    basename='purchase-order'
)
router.register(
    r'goods-receipts',
    views.GoodsReceiptViewSet,
    basename='goods-receipt'
)
router.register(
    r'supplier-categories',
    views.SupplierCategoryViewSet,
    basename='supplier-category'
)
router.register(
    r'supplier-invoices',
    views.SupplierInvoiceViewSet,
    basename='supplier-invoice'
)
router.register(
    r'purchase-order-lines',
    views.PurchaseOrderLineViewSet,
    basename='purchase-order-line'
)

urlpatterns = [
    path('purchasing/', include(router.urls)),
    # Removing the standalone PDF endpoint as it's now part of the SupplierInvoiceViewSet as an action
    # path('invoices/<int:invoice_id>/pdf/', views.SupplierInvoicePDFView.as_view(), name='supplier-invoice-pdf'),
]