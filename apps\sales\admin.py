from django.contrib import admin
from django.db.models import Count, Sum, F, Q
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import (
    Customer, CreditLimitChange, CustomerPriceList, PriceListItem,
    DiscountRule, SalesOrder, SalesOrderLine, Shipment, ShipmentLine,
    ShipmentTracking, ReturnOrder, ReturnLine, ReturnInspection,
    ReturnCreditNote, CustomerAddress, CustomerContact, CustomerCategory
)

class CustomerAddressInline(admin.TabularInline):
    model = CustomerAddress
    extra = 0
    fields = ['name', 'address_type', 'street_address1', 'street_address2', 'city', 'state', 'zip_code', 'country', 'notes', 'is_default']

class CustomerContactInline(admin.TabularInline):
    model = CustomerContact
    extra = 0
    fields = ['name', 'email', 'phone', 'mobile', 'role', 'notes', 'is_primary']



class CreditLimitChangeInline(admin.TabularInline):
    model = CreditLimitChange
    extra = 0
    readonly_fields = ['changed_by', 'old_limit', 'new_limit', 'reason']
    can_delete = False
    max_num = 0
    ordering = ['-created_at']


@admin.register(CustomerCategory)   
class CustomerCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'number', 'parent']
    search_fields = ['name', 'number']
    list_filter = ['company']

@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = [
        'name', 
        'number', 
        'email', 
        'payment_terms',
        'credit_limit',
        'credit_usage',
        'active_orders_count',
        'is_active'
    ]
    list_filter = ['company', 'payment_terms', 'category', 'is_active']
    search_fields = ['name', 'number', 'email', 'phone', 'tax_number']
    inlines = [CreditLimitChangeInline, CustomerAddressInline, CustomerContactInline]
    
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'number', 'is_active')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone')
        }),
        ('Financial Details', {
            'fields': ('tax_number', 'payment_terms', 'credit_limit')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        })
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            active_orders=Count(
                'sales_orders',
                filter=~Q(sales_orders__status__in=['COMPLETED', 'CANCELLED'])
            ),
            total_unpaid=Sum(
                'sales_orders__total_amount',
                filter=Q(sales_orders__status='CONFIRMED')
            )
        )
    
    def active_orders_count(self, obj):
        return obj.active_orders
    active_orders_count.admin_order_field = 'active_orders'
    
    def credit_usage(self, obj):
        if not obj.credit_limit:
            return 'No limit set'
        unpaid = obj.total_unpaid or 0
        percentage = (unpaid / obj.credit_limit * 100) if obj.credit_limit else 0
        color = 'green' if percentage < 70 else 'orange' if percentage < 90 else 'red'
        return format_html(
            '<span style="color: {}">${:.2f} ({:.1f}%)</span>',
            color, unpaid, percentage
        )
    credit_usage.short_description = 'Credit Usage'

class PriceListItemInline(admin.TabularInline):
    model = PriceListItem
    extra = 0
    raw_id_fields = ['product', 'variant']

@admin.register(CustomerPriceList)
class CustomerPriceListAdmin(admin.ModelAdmin):
    list_display = ['name', 'is_active', 'price_valid_from', 'price_valid_to', 'customer_count', 'item_count']
    list_filter = ['company', 'is_active']
    search_fields = ['name']
    filter_horizontal = ['customers']
    inlines = [PriceListItemInline]
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            customer_count=Count('customers'),
            item_count=Count('items')
        )
    
    def customer_count(self, obj):
        return obj.customer_count
    customer_count.admin_order_field = 'customer_count'
    
    def item_count(self, obj):
        return obj.item_count
    item_count.admin_order_field = 'item_count'

@admin.register(DiscountRule)
class DiscountRuleAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'discount_type',
        'value',
        'valid_from',
        'valid_to',
        'min_order_value',
        'product_count',
        'customer_count'
    ]
    list_filter = ['company', 'discount_type']
    search_fields = ['name']
    filter_horizontal = ['products', 'customers']
    
    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            product_count=Count('products'),
            customer_count=Count('customers')
        )
    
    def product_count(self, obj):
        return obj.product_count
    product_count.admin_order_field = 'product_count'
    
    def customer_count(self, obj):
        return obj.customer_count
    customer_count.admin_order_field = 'customer_count'

class SalesOrderLineInline(admin.TabularInline):
    model = SalesOrderLine
    extra = 0
    raw_id_fields = ['product', 'variant']
    fields = ['product', 'variant', 'quantity', 'shipped_quantity', 'unit_price', 'discount', 'tax_rate', 'line_total']
    readonly_fields = ['shipped_quantity', 'line_total']

    def line_total(self, obj):
        if not obj.quantity or not obj.unit_price:
            return '-'
        total = obj.quantity * obj.unit_price * (1 + obj.tax_rate/100) - obj.discount
        return format_html('${:.2f}', total)
    line_total.short_description = 'Total (inc. tax)'

class ShipmentInline(admin.TabularInline):
    model = Shipment
    extra = 0
    fields = ['number', 'status', 'shipping_method', 'carrier', 'tracking_number', 'shipped_at']
    show_change_link = True

@admin.register(SalesOrder)
class SalesOrderAdmin(admin.ModelAdmin):
    list_display = [
        'number',
        'customer',
        'order_date',
        'requested_delivery_date',
        'status',
        'shipping_status',
        'total_amount',
        'total_discount'
    ]
    list_filter = ['company', 'status', 'order_date']
    search_fields = ['number', 'customer__name', 'notes']
    raw_id_fields = ['customer', 'price_list']
    inlines = [SalesOrderLineInline, ShipmentInline]
    readonly_fields = ['total_amount', 'total_discount', 'uuid']
    date_hierarchy = 'order_date'

    def shipping_status(self, obj):
        total_lines = obj.lines.count()
        if not total_lines:
            return 'No lines'
            
        fully_shipped = obj.lines.filter(
            quantity=F('shipped_quantity')
        ).count()
        
        if fully_shipped == 0:
            return format_html(
                '<span style="color: red;">Not shipped</span>'
            )
        elif fully_shipped == total_lines:
            return format_html(
                '<span style="color: green;">Fully shipped</span>'
            )
        else:
            return format_html(
                '<span style="color: orange;">Partially shipped ({}/{})</span>',
                fully_shipped,
                total_lines
            )

class ShipmentLineInline(admin.TabularInline):
    model = ShipmentLine
    extra = 0
    raw_id_fields = ['sales_order_line', 'picked_from']

class ShipmentTrackingInline(admin.TabularInline):
    model = ShipmentTracking
    extra = 0
    fields = ['event_date', 'event_type', 'location', 'description']
    ordering = ['-event_date']

@admin.register(Shipment)
class ShipmentAdmin(admin.ModelAdmin):
    list_display = [
        'number',
        'sales_order',
        'status',
        'carrier',
        'tracking_number',
        'shipped_at',
        'shipping_cost'
    ]
    list_filter = ['company', 'status', 'carrier', 'shipped_at']
    search_fields = [
        'number',
        'sales_order__number',
        'tracking_number'
    ]
    raw_id_fields = ['sales_order', 'shipped_by']
    inlines = [ShipmentLineInline, ShipmentTrackingInline]
    readonly_fields = ['shipped_at']

class ReturnLineInline(admin.TabularInline):
    model = ReturnLine
    extra = 0
    raw_id_fields = ['sales_order_line']
    readonly_fields = ['received_quantity', 'inspection_result']

class ReturnCreditNoteInline(admin.TabularInline):
    model = ReturnCreditNote
    extra = 0
    readonly_fields = ['number', 'amount', 'status', 'issued_date']
    can_delete = False
    max_num = 0

@admin.register(ReturnOrder)
class ReturnOrderAdmin(admin.ModelAdmin):
    list_display = [
        'number',
        'rma_number',
        'sales_order',
        'status',
        'created_by',
        'created_at',
        'received_date',
        'return_value'
    ]
    list_filter = ['company', 'status', 'created_at', 'received_date']
    search_fields = [
        'number',
        'rma_number',
        'sales_order__number',
        'notes'
    ]
    raw_id_fields = ['sales_order', 'created_by']
    inlines = [ReturnLineInline, ReturnCreditNoteInline]
    readonly_fields = ['created_at', 'completed_date']
    date_hierarchy = 'created_at'
    
    def return_value(self, obj):
        total = sum(line.refund_amount or 0 for line in obj.lines.all())
        return format_html('${:.2f}', total)

class ReturnInspectionInline(admin.TabularInline):
    model = ReturnInspection
    extra = 0
    readonly_fields = ['inspector', 'inspection_date']
    can_delete = False
    max_num = 0
    ordering = ['-inspection_date']

@admin.register(ReturnLine)
class ReturnLineAdmin(admin.ModelAdmin):
    list_display = [
        'return_order',
        'sales_order_line',
        'quantity',
        'received_quantity',
        'status',
        'inspection_result',
        'refund_amount'
    ]
    list_filter = ['status', 'inspection_result']
    search_fields = [
        'return_order__number',
        'return_order__rma_number',
        'sales_order_line__product__name'
    ]
    raw_id_fields = ['return_order', 'sales_order_line']
    inlines = [ReturnInspectionInline]
    readonly_fields = ['received_quantity', 'inspection_result']

@admin.register(ReturnCreditNote)
class ReturnCreditNoteAdmin(admin.ModelAdmin):
    list_display = [
        'number',
        'return_order',
        'amount',
        'status',
        'issued_date'
    ]
    list_filter = ['company', 'status', 'issued_date']
    search_fields = [
        'number',
        'return_order__number',
        'return_order__rma_number'
    ]
    raw_id_fields = ['return_order']
    readonly_fields = ['issued_date']
    date_hierarchy = 'issued_date'