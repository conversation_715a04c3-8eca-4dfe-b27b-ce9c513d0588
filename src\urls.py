from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from utils.swagger import CustomSchemaGenerator
# Schema configuration
schema_view = get_schema_view(
    openapi.Info(
        title="Warehouse Management System API",
        default_version='v1',
        description="""
        A comprehensive API for managing warehouse operations including:
        * Inventory Management
        * Product Catalog
        * Sales Orders
        * Purchase Orders
        * Warehouse Operations
        
        ## Authentication
        This API uses JWT authentication. Include the JWT token in the Authorization header:
        `Authorization: Bearer <token>`
        
        ## Multi-tenancy
        All operations are scoped to the user's active company. Use the company switching endpoint
        to change the active company context.
        """,
        terms_of_service="https://www.yourcompany.com/terms/",
        contact=openapi.Contact(email="<EMAIL>"),
        license=openapi.License(name="Your License"),
    ),
    public=True,
    permission_classes=[permissions.AllowAny],
    generator_class=CustomSchemaGenerator,
)

urlpatterns = [
    # Admin and API URLs
    path('admin/', admin.site.urls),
    path('api/v1/', include('apps.authentication.api.urls')),
    path('api/v1/', include('apps.core.api.urls')),
    path('api/v1/', include('apps.inventory.api.urls')),
    path('api/v1/', include('apps.operations.api.urls')),
    path('api/v1/', include('apps.products.api.urls')),
    path('api/v1/', include('apps.purchasing.api.urls')),
    path('api/v1/', include('apps.sales.api.urls')),

    # Documentation URLs
    path('api/docs/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('api/redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
]