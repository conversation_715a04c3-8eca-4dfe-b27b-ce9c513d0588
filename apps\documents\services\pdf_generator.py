import os
import tempfile
from pathlib import Path
from datetime import datetime

from django.template.loader import render_to_string
from django.template.defaultfilters import slugify
from django.conf import settings
from weasyprint import HTML, CSS

from apps.core.models import Company


class PDFGenerator:
    """
    Service class for generating PDF documents from HTML templates.
    """
    
    @staticmethod
    def generate_pdf_from_html(html_content, css_string=None):
        """
        Generate a PDF from HTML content.
        
        Args:
            html_content (str): The HTML content to convert to PDF
            css_string (str, optional): Additional CSS to apply to the PDF
            
        Returns:
            bytes: The PDF content as bytes
        """
        # Create a temporary file to store the PDF
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
            temp_path = temp_file.name
        
        # Set up CSS if provided
        stylesheets = []
        if css_string:
            stylesheets.append(CSS(string=css_string))
        
        # Generate the PDF from HTML
        HTML(string=html_content).write_pdf(temp_path, stylesheets=stylesheets)
        
        # Read the PDF file
        with open(temp_path, 'rb') as pdf_file:
            pdf_content = pdf_file.read()
        
        # Clean up the temporary file
        os.unlink(temp_path)
        
        return pdf_content

    @classmethod
    def generate_sales_invoice_pdf(cls, invoice, company=None):
        """
        Generate a PDF for a sales invoice.
        
        Args:
            invoice: The SalesInvoice instance
            company (Company, optional): The company instance. If not provided,
                                         it will be fetched from the invoice.
                                         
        Returns:
            bytes: The PDF content
            str: The suggested filename for the PDF
        """
        if company is None:
            company = invoice.company
        
        # Prepare context for template rendering
        context = {
            'invoice': invoice,
            'company': company,
            'lines': invoice.lines.all(),
            'show_discount': invoice.total_discount > 0,
            'show_po_number': invoice.sales_order is not None,
            'notes': invoice.notes,
        }
        
        # Get company logo if available
        if hasattr(company, 'logo') and company.logo:
            context['company_logo_url'] = company.logo.url
        
        # Render the HTML template
        html_content = render_to_string('invoices/sales_invoice.html', context)
        
        # Generate PDF
        pdf_content = cls.generate_pdf_from_html(html_content)
        
        # Create a filename
        date_str = datetime.now().strftime('%Y%m%d')
        filename = f"sales_invoice_{slugify(company.name)}_{invoice.number}_{date_str}.pdf"
        
        return pdf_content, filename
    
    @classmethod
    def generate_supplier_invoice_pdf(cls, invoice, company=None):
        """
        Generate a PDF for a supplier invoice.
        
        Args:
            invoice: The SupplierInvoice instance
            company (Company, optional): The company instance. If not provided,
                                         it will be fetched from the invoice.
                                         
        Returns:
            bytes: The PDF content
            str: The suggested filename for the PDF
        """
        if company is None:
            company = invoice.company
        
        # Prepare context for template rendering
        context = {
            'invoice': invoice,
            'company': company,
            'lines': invoice.lines.all(),
            'show_discount': invoice.total_discount > 0,
            'show_po_number': invoice.purchase_order is not None,
            'notes': invoice.notes,
        }
        
        # Get company logo if available
        if hasattr(company, 'logo') and company.logo:
            context['company_logo_url'] = company.logo.url
        
        # Render the HTML template
        html_content = render_to_string('invoices/supplier_invoice.html', context)
        
        # Generate PDF
        pdf_content = cls.generate_pdf_from_html(html_content)
        
        # Create a filename
        date_str = datetime.now().strftime('%Y%m%d')
        supplier_name = slugify(invoice.supplier.name)
        filename = f"supplier_invoice_{supplier_name}_{invoice.invoice_number}_{date_str}.pdf"
        
        return pdf_content, filename 