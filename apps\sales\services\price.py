# sales/services/price.py
from decimal import Decimal
from typing import Dict, Optional, List, Tuple
from django.utils import timezone
from django.db.models import Q
from ..models import (
    Customer, CustomerPriceList, DiscountRule, DiscountUsage
)
from apps.products.models import Product, ProductVariant, ProductPricing
from .base import BaseSalesService
from utils.enums import DiscountType

class PricingService(BaseSalesService):
    def get_customer_price(
        self,
        customer: Customer,
        product: Product,
        variant: Optional[ProductVariant] = None,
        quantity: Decimal = Decimal('1'),
        order_value: Optional[Decimal] = None,
        currency = None
    ) -> Dict:
        """Get price and applicable discounts for a product"""
        base_price, price_source, price_list_name = self._get_base_price_with_source(
            customer, product, variant, quantity, currency
        )
        
        # Calculate applicable discounts
        discounts = self._get_applicable_discounts(
            customer=customer,
            product=product,
            order_value=order_value or (base_price * quantity),
            base_price=base_price
        )
        
        final_price = self._apply_discounts(base_price, discounts)
        
        return {
            'base_price': base_price,
            'price_source': price_source,
            'price_list_name': price_list_name,
            'discounts': discounts,
            'final_price': final_price,
            'total_discount_amount': base_price - final_price,
            'currency': currency or customer.currency
        }

    def _get_applicable_discounts(
        self,
        customer: Customer,
        product: Product,
        order_value: Decimal,
        base_price: Decimal
    ) -> List[Dict]:
        """Get all applicable discounts for a product"""
        today = timezone.now().date()
        
        applicable_rules = DiscountRule.objects.filter(
            Q(customers=customer) |
            Q(customer_categories=customer.category) |
            Q(products=product) |
            Q(product_categories=product.category) |
            Q(customers__isnull=True, products__isnull=True),
            company=self.company,
            is_active=True,
            valid_from__lte=today
        ).exclude(
            valid_to__lt=today
        )
        
        discounts = []
        for rule in applicable_rules:
            if rule.is_valid_for_order(order_value):
                if rule.discount_type == DiscountType.PERCENTAGE:
                    amount = base_price * (rule.value / 100)
                elif rule.discount_type == DiscountType.FIXED_AMOUNT:
                    amount = rule.value
                else:  # FIXED_PRICE
                    amount = base_price - rule.value
                    
                discounts.append({
                    'rule_id': rule.id,
                    'name': rule.name,
                    'type': rule.discount_type,
                    'value': rule.value,
                    'amount': amount,
                    'description': f"{rule.value}% off" if rule.discount_type == DiscountType.PERCENTAGE 
                                 else f"${rule.value} off" if rule.discount_type == DiscountType.FIXED_AMOUNT
                                 else f"Fixed price ${rule.value}"
                })
        
        return discounts

    def _calculate_discount_amount(
        self,
        rule: DiscountRule,
        base_price: Decimal
    ) -> Decimal:
        """Calculate discount amount based on rule type"""
        if rule.discount_type == DiscountType.PERCENTAGE.name:
            return base_price * (rule.value / 100)
        elif rule.discount_type == DiscountType.FIXED_AMOUNT.name:
            return min(rule.value, base_price)  # Don't discount more than base price
        elif rule.discount_type == DiscountType.FIXED_PRICE.name:
            return max(base_price - rule.value, Decimal('0'))
        return Decimal('0')

    def _apply_discounts(
        self,
        base_price: Decimal,
        discounts: List[Dict]
    ) -> Decimal:
        """Apply all discounts to get final price"""
        final_price = base_price
        
        for discount in discounts:
            if discount['type'] == DiscountType.FIXED_PRICE.name:
                # Fixed price overrides previous discounts
                final_price = base_price - discount['amount']
                break
            else:
                final_price -= discount['amount']
                
        return max(final_price, Decimal('0'))

    def record_discount_usage(
        self,
        discount_rule: DiscountRule,
        customer: Customer,
        sales_order,
        amount: Decimal
    ) -> None:
        """Record the usage of a discount"""
        DiscountUsage.objects.create(
            company=self.company,
            discount_rule=discount_rule,
            customer=customer,
            sales_order=sales_order,
            amount=amount
        )

    def _get_base_price(
        self,
        customer: Customer,
        product: Product,
        variant: Optional[ProductVariant],
        quantity: Decimal,
        currency = None
    ) -> Decimal:
        """Get base price considering all pricing rules"""
        today = timezone.now().date()
        
        # First try customer-specific price list
        customer_price_list = CustomerPriceList.objects.filter(
            customers=customer,
            company=self.company,
            is_active=True,
            price_valid_from__lte=today
        ).exclude(
            price_valid_to__lt=today
        ).first()
        
        if customer_price_list:
            price_item = customer_price_list.items.filter(
                product=product,
                variant=variant,
                min_quantity__lte=quantity
            ).order_by('-min_quantity').first()
            
            if price_item:
                return self._convert_currency(
                    price_item.unit_price,
                    customer_price_list.currency,
                    currency
                )
        
        # If no customer-specific price, try category price list
        if customer.category:
            category_price_list = CustomerPriceList.objects.filter(
                customer_categories=customer.category,
                company=self.company,
                is_active=True,
                price_valid_from__lte=today
            ).exclude(
                price_valid_to__lt=today
            ).first()
            
            if category_price_list:
                price_item = category_price_list.items.filter(
                    product=product,
                    variant=variant,
                    min_quantity__lte=quantity
                ).order_by('-min_quantity').first()
                
                if price_item:
                    return self._convert_currency(
                        price_item.unit_price,
                        category_price_list.currency,
                        currency
                    )
        
        # Try variant price if variant specified
        if variant:
            variant_price = variant.prices.filter(
                is_active=True,
                price_valid_from__lte=today
            ).exclude(
                price_valid_to__lt=today
            ).order_by('-price_valid_from').first()
            
            if variant_price:
                return self._convert_currency(
                    variant_price.price,
                    variant_price.currency,
                    currency
                )
        
        # Get current product price
        product_price = product.prices.filter(
            is_active=True,
            price_valid_from__lte=today
        ).exclude(
            price_valid_to__lt=today
        ).order_by('-price_valid_from').first()
        
        if product_price:
            return self._convert_currency(
                product_price.price,
                product_price.currency,
                currency
            )
        
        # Fallback to base selling price
        return 0

    def _convert_currency(
        self,
        amount: Decimal,
        from_currency,
        to_currency
    ) -> Decimal:
        """Convert amount between currencies"""
        if not to_currency or from_currency == to_currency:
            return amount
            
        # Get conversion rate
        rate = self._get_currency_rate(from_currency, to_currency)
        return amount * rate

    def _get_currency_rate(self, from_currency, to_currency) -> Decimal:
        """Get currency conversion rate"""
        # Implementation depends on how you handle currency rates
        # Could be from a service, database, etc.
        return Decimal('1.0')  # Placeholder

    def _get_base_price_with_source(
        self,
        customer: Customer,
        product: Product,
        variant: Optional[ProductVariant],
        quantity: Decimal,
        currency = None
    ) -> Tuple[Decimal, str, Optional[str]]:
        """Get base price and price source for a product"""
        today = timezone.now().date()
        
        # First try customer-specific price list
        customer_price_list = CustomerPriceList.objects.filter(
            customers=customer,
            company=self.company,
            is_active=True,
            price_valid_from__lte=today
        ).exclude(
            price_valid_to__lt=today
        ).first()
        
        if customer_price_list:
            price_item = customer_price_list.items.filter(
                product=product,
                variant=variant,
                min_quantity__lte=quantity
            ).order_by('-min_quantity').first()
            
            if price_item:
                return (
                    self._convert_currency(
                        price_item.unit_price,
                        customer_price_list.currency,
                        currency
                    ),
                    'customer_price_list',
                    customer_price_list.name
                )
        
        # If no customer-specific price, try category price list
        if customer.category:
            category_price_list = CustomerPriceList.objects.filter(
                customer_categories=customer.category,
                company=self.company,
                is_active=True,
                price_valid_from__lte=today
            ).exclude(
                price_valid_to__lt=today
            ).first()
            
            if category_price_list:
                price_item = category_price_list.items.filter(
                    product=product,
                    variant=variant,
                    min_quantity__lte=quantity
                ).order_by('-min_quantity').first()
                
                if price_item:
                    return (
                        self._convert_currency(
                            price_item.unit_price,
                            category_price_list.currency,
                            currency
                        ),
                        'category_price_list',
                        category_price_list.name
                    )
        
        # Try variant price if variant specified
        if variant:
            variant_price = variant.prices.filter(
                is_active=True,
                price_valid_from__lte=today
            ).exclude(
                price_valid_to__lt=today
            ).order_by('-price_valid_from').first()
            
            if variant_price:
                return (
                    self._convert_currency(
                        variant_price.price,
                        variant_price.currency,
                        currency
                    ),
                    'variant_price',
                    None
                )
        
        # Get current product price
        product_price = product.prices.filter(
            is_active=True,
            price_valid_from__lte=today
        ).exclude(
            price_valid_to__lt=today
        ).order_by('-price_valid_from').first()
        
        if product_price:
            return (
                self._convert_currency(
                    product_price.price,
                    product_price.currency,
                    currency
                ),
                'product_price',
                None
            )
        
        # Fallback to base selling price
        return (0, 'base_selling_price', None)