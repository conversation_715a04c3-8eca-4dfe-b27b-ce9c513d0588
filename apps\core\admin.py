from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.html import format_html
from django.db.models import Count, Q
from django.urls import reverse
from .models import (
    User, Company, Employee, Warehouse, 
    CompanySetting, ActiveCompanySession, AuditLog,
    UnitOfMeasure, Currency, Language, Translation,
    PaymentTerm, DeliveryTerm, DeliveryMethod
)


@admin.register(UnitOfMeasure)
class UnitOfMeasureAdmin(admin.ModelAdmin):
    list_display = ['name', 'abbreviation', 'company', 'is_active', 'product_count']
    list_filter = ['company', 'is_active']
    search_fields = ['name', 'abbreviation']
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.annotate(
            products_count=Count('product', distinct=True)
        )
        return queryset
    
    def product_count(self, obj):
        return obj.products_count
    product_count.admin_order_field = 'products_count'

class EmployeeInline(admin.TabularInline):
    model = Employee
    extra = 1
    fields = ['company', 'role', 'is_active', 'left_at']
    readonly_fields = ['joined_at']
    raw_id_fields = ['company']
    show_change_link = True

class ActiveCompanySessionInline(admin.TabularInline):
    model = ActiveCompanySession
    extra = 0
    readonly_fields = ['company', 'created_at']
    can_delete = False
    max_num = 1

@admin.register(User)
class CustomUserAdmin(UserAdmin):
    list_display = [
        'email',
        'full_name_display',
        'active_companies_count',
        'is_employee',
        'is_staff',
        'is_active',
        'is_verified',
        'last_login'
    ]
    search_fields = ['email', 'first_name', 'last_name', 'username']
    list_filter = [
        'is_staff',
        'is_employee',
        'is_active',
        'is_verified',
        'company_employee__role'
    ]
    readonly_fields = ['uuid', 'updated_at', 'created_at', 'last_login']
    inlines = [EmployeeInline, ActiveCompanySessionInline]

    fieldsets = (
        (None, {
            'fields': (
                'email', 'password', 'username'
            )
        }),
        ('Personal Info', {
            'fields': (
                'first_name', 'last_name', 'phone'
            )
        }),
        ('Permissions', {
            'fields': (
                'is_active', 'is_staff', 'is_superuser',
                'is_employee', 'is_verified', 'groups', 
                'user_permissions'
            )
        }),
        ('System Info', {
            'fields': ('uuid', 'created_at', 'updated_at', 'last_login'),
            'classes': ('collapse',)
        }),
    )

    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': (
                'email', 'username', 'password1', 'password2',
                'is_employee', 'is_staff', 'is_superuser'
            )
        }),
    )

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            active_companies=Count(
                'company_employee',
                filter=Q(company_employee__is_active=True)
            )
        )

    def full_name_display(self, obj):
        if obj.first_name or obj.last_name:
            return f"{obj.first_name} {obj.last_name}".strip()
        return "-"
    full_name_display.short_description = "Full Name"

    def active_companies_count(self, obj):
        url = reverse('admin:core_employee_changelist')
        url += f'?user__id__exact={obj.id}'
        return format_html('<a href="{}">{} companies</a>', url, obj.active_companies)
    active_companies_count.short_description = "Active Companies"
    active_companies_count.admin_order_field = 'active_companies'

class EmployeeInlineForCompany(admin.TabularInline):
    model = Employee
    extra = 1
    fields = ['user', 'role', 'is_active', 'left_at']
    readonly_fields = ['joined_at']
    raw_id_fields = ['user']

class WarehouseInline(admin.TabularInline):
    model = Warehouse
    extra = 1
    fields = ['name', 'number', 'is_active']
    show_change_link = True

@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'registration_number',
        'email',
        'active_employees_count',
        'active_warehouses_count',
        'is_active'
    ]
    search_fields = ['name', 'registration_number', 'email']
    list_filter = ['is_active', 'created_at']
    inlines = [EmployeeInlineForCompany, WarehouseInline]
    
    fieldsets = (
        (None, {
            'fields': ('name', 'registration_number', 'is_active')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'website', 'address')
        }),
        ('Tax Information', {
            'fields': ('vat_number',)
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            active_employees=Count(
                'employees',
                filter=Q(employees__company_employee__is_active=True)
            ),
            active_warehouses=Count(
                'company_warehouse',
                filter=Q(company_warehouse__is_active=True)
            )
        )

    def active_employees_count(self, obj):
        url = reverse('admin:core_employee_changelist')
        url += f'?company__id__exact={obj.id}'
        return format_html('<a href="{}">{} employees</a>', url, obj.active_employees)
    active_employees_count.admin_order_field = 'active_employees'

    def active_warehouses_count(self, obj):
        url = reverse('admin:core_warehouse_changelist')
        url += f'?company__id__exact={obj.id}'
        return format_html('<a href="{}">{} warehouses</a>', url, obj.active_warehouses)
    active_warehouses_count.admin_order_field = 'active_warehouses'

@admin.register(Employee)
class EmployeeAdmin(admin.ModelAdmin):
    list_display = [
        'user_email',
        'user_full_name',
        'company',
        'role',
        'is_active',
        'joined_at',
        'left_at'
    ]
    list_filter = ['role', 'is_active', 'company']
    search_fields = [
        'user__email',
        'user__first_name',
        'user__last_name',
        'company__name'
    ]
    raw_id_fields = ['user', 'company']
    date_hierarchy = 'joined_at'

    def user_email(self, obj):
        return obj.user.email
    user_email.admin_order_field = 'user__email'

    def user_full_name(self, obj):
        if obj.user.first_name or obj.user.last_name:
            return f"{obj.user.first_name} {obj.user.last_name}".strip()
        return "-"
    user_full_name.admin_order_field = 'user__first_name'

@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = [
        'name',
        'number',
        'company',
        'location_count',
        'email',
        'is_active'
    ]
    list_filter = ['company', 'is_active']
    search_fields = ['name', 'number', 'email', 'company__name']
    
    fieldsets = (
        (None, {
            'fields': ('company', 'name', 'number', 'is_active')
        }),
        ('Contact Information', {
            'fields': ('email', 'phone', 'address')
        }),
        ('System Information', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    readonly_fields = ['created_at', 'updated_at']

    def get_queryset(self, request):
        return super().get_queryset(request).annotate(
            loc_count=Count('locations')
        )

    def location_count(self, obj):
        return obj.loc_count
    location_count.admin_order_field = 'loc_count'

@admin.register(CompanySetting)
class CompanySettingAdmin(admin.ModelAdmin):
    list_display = [
        'company',
        'default_currency',
        'inventory_valuation_method',
        'timezone'
    ]
    list_filter = [
        'inventory_valuation_method',
        'default_currency',
        'timezone'
    ]
    search_fields = ['company__name']
    raw_id_fields = ['company']

@admin.register(AuditLog)
class AuditLogAdmin(admin.ModelAdmin):
    list_display = [
        'created_at',
        'company',
        'user',
        'content_type',
        'action',
        'object_id'
    ]
    list_filter = [
        'company',
        'action',
        'content_type',
        'created_at'
    ]
    search_fields = [
        'user__email',
        'company__name',
        'action'
    ]
    readonly_fields = [
        'created_at',
        'company',
        'user',
        'content_type',
        'object_id',
        'action',
        'data'
    ]
    date_hierarchy = 'created_at'

    def has_add_permission(self, request):
        return False

    def has_change_permission(self, request, obj=None):
        return False

    def has_delete_permission(self, request, obj=None):
        return False
    

@admin.register(Currency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['currency', 'is_active']
    list_filter = ['is_active']
    search_fields = ['currency']


@admin.register(Language)
class LanguageAdmin(admin.ModelAdmin):
    list_display = ['language', 'is_active']
    list_filter = ['is_active']
    search_fields = ['language']


@admin.register(Translation)
class TranslationAdmin(admin.ModelAdmin):
    list_display = ['language', 'name', 'description']
    list_filter = ['language']
    search_fields = ['name', 'description']
    raw_id_fields = ['language']

@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name', 'description']


@admin.register(DeliveryTerm)
class DeliveryTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name', 'description']

@admin.register(DeliveryMethod)
class DeliveryMethodAdmin(admin.ModelAdmin):
    list_display = ['name', 'description']
    search_fields = ['name', 'description']
