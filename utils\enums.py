from enum import Enum


class EmployeeRole(Enum):
    ADMIN = 'Administrator'
    MANAGER = 'Manager'
    WAREHOUSE_WORKER = 'Warehouse Worker'
    SALES_REP = 'Sales Representative'
    PURCHASER = 'Purchaser'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class InvoiceStatus(Enum):
    DRAFT = 'Draft'
    SENT = 'Sent'
    PAID = 'Paid'
    CANCELLED = 'Cancelled'
    PARTIAL_PAID = 'Partial Paid'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class InventoryValuation(Enum): 
    FIFO = 'First In First Out'
    AVERAGE = 'Average Cost'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class InventoryMovementType(Enum):
    RECEIPT = 'Receipt'
    SHIPMENT = 'Shipment'
    TRANSFER = 'Transfer'
    ADJUSTMENT = 'Adjustment'
    RETURN = 'Return'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class InventoryMovementStatus(Enum):
    DRAFT = 'Draft'
    PENDING = 'Pending'
    IN_PROGRESS = 'In Progress'
    COMPLETED = 'Completed'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]



"""The status flow typically goes:
DRAFT -> When order is created
SENT -> When order is sent to supplier
CONFIRMED -> When supplier confirms
READY -> When goods are expected/ready for receipt
PARTIAL -> When some items are received
COMPLETED -> When all items are received
Or it can be CANCELLED at any point before receiving goods.
The PARTIAL and COMPLETED statuses are handled automatically by the GoodsReceiptService when goods are received.
"""
class PurchaseOrderStatus(str, Enum):
    DRAFT = 'DRAFT'          # Utkast - Initial state when PO is created
    SENT = 'SENT'           # Utskickat - PO has been sent to supplier
    CONFIRMED = 'CONFIRMED'  # Bekräftat - Supplier has confirmed the order
    READY = 'READY'         # Redo för inleverans - Order is ready for receipt
    PARTIAL = 'PARTIAL'      # Delvis inlevererat - Some items received
    COMPLETED = 'COMPLETED'  # Avslutat - All items received
    CANCELLED = 'CANCELLED'  # Makulerat - Order has been cancelled

    @classmethod
    def choices(cls):
        return [
            (key.name, key.value) for key in cls
        ]


class DiscountType(Enum):
    PERCENTAGE = "PERCENTAGE"  # e.g., 10% off
    FIXED_AMOUNT = "FIXED_AMOUNT"  # e.g., $50 off
    FIXED_PRICE = "FIXED_PRICE"  # e.g., fixed price of $99

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]



class SalesOrderStatus(Enum):
    DRAFT = 'Draft'
    CONFIRMED = 'Confirmed'
    PICKING = 'Picking in Progress'
    PACKED = 'Packed'
    SHIPPING = 'Shipping'
    SHIPPED = 'Shipped'
    PARTIAL_SHIPPED = 'Partially Shipped'
    DELIVERED = 'Delivered'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class PickingTaskStatus(Enum):
    PENDING = 'Pending'
    IN_PROGRESS = 'In Progress'
    COMPLETED = 'Completed'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class CustomerCreditStatus(Enum): 
    ON_HOLD = 'On Hold'
    NO_LIMIT = 'No Limit'
    LIMIT_REACHED = 'Limit Reached'
    NEAR_LIMIT = 'Near Limit'
    GOOD = 'Good'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class ShipmentStatus(Enum):
    PENDING = 'Pending'
    IN_TRANSIT = 'In Transit'
    DELIVERED = 'Delivered'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class ShipmentStatus(Enum):
    PENDING = 'Pending'
    READY = 'Ready'
    PICKED_UP = 'Picked Up'
    IN_TRANSIT = 'In Transit'
    OUT_FOR_DELIVERY = 'Out for Delivery'
    DELIVERED = 'Delivered'
    FAILED = 'Failed'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class ReturnStatus(Enum):
    PENDING = 'Pending'
    APPROVED = 'Approved'
    RECEIVED = 'Received'
    INSPECTED = 'Inspected'
    COMPLETED = 'Completed'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class ReturnLineStatus(Enum):
    PENDING = 'Pending'
    RECEIVED = 'Received'
    INSPECTED = 'Inspected'
    REFUNDED = 'Refunded'
    REJECTED = 'Rejected'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class InspectionResult(Enum):
    RETURN_TO_STOCK = 'Return to Stock'
    DAMAGED_REFUND = 'Damaged - Refund'
    DAMAGED_NO_REFUND = 'Damaged - No Refund'
    REPAIR = 'Needs Repair'
    DISPOSE = 'Dispose'
    RETURN_TO_VENDOR = 'Return to Vendor'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class ReturnCreditNoteStatus(Enum):
    DRAFT = 'Draft'
    ISSUED = 'Issued'
    CREDITED = 'Credited'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class AdjustmentType(Enum):
    """Types of inventory adjustments"""
    DAMAGE = 'Damaged Goods'
    SHRINKAGE = 'Inventory Shrinkage'
    FOUND = 'Found Items'
    COUNT = 'Cycle Count Adjustment'
    QUALITY = 'Quality Control Adjustment'
    EXPIRY = 'Expired Items'
    OTHER = 'Other Adjustment'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class AdjustmentStatus(Enum):
    """Status of inventory adjustments"""
    DRAFT = 'Draft'
    PENDING = 'Pending Approval'
    APPROVED = 'Approved'
    COMPLETED = 'Completed'
    CANCELLED = 'Cancelled'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class QualityStatus(Enum):
    GOOD = 'Good'
    QC_HOLD = 'Quality Control Hold'
    DAMAGED = 'Damaged'
    EXPIRED = 'Expired'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]



class AddressType(Enum):
    BILLING = 'Billing'
    SHIPPING = 'Shipping'
    BOTH = 'Both'
    OTHER = 'Other'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class Languages(Enum):
    ENGLISH = 'English'
    GERMAN = 'German'
    FRENCH = 'French'
    SPANISH = 'Spanish'
    ITALIAN = 'Italian'
    SWEDISH = 'Swedish'
    NORWEGIAN = 'Norwegian'
    DANISH = 'Danish'
    FINNISH = 'Finnish'
    DUTCH = 'Dutch'
    POLISH = 'Polish'
    PORTUGUESE = 'Portuguese'
    RUSSIAN = 'Russian'
    CHINESE = 'Chinese'
    JAPANESE = 'Japanese'
    KOREAN = 'Korean'
    ARABIC = 'Arabic'
    HINDI = 'Hindi'
    TURKISH = 'Turkish'
    INDONESIAN = 'Indonesian'
    THAI = 'Thai'
    VIETNAMESE = 'Vietnamese'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class TermType(Enum): 
    NET = 'Net'
    CASH = 'Cash'
    CREDIT_CARD = 'Credit Card'
    DUE_DATE = 'Due Date'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class NumberSequenceType(Enum):
    PURCHASE_ORDER = 'Purchase Order'
    SUPPLIER_INVOICE = 'Supplier Invoice'
    SALES_ORDER = 'Sales Order'
    SALES_INVOICE = 'Sales Invoice'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class Currencies(Enum):
    EUR = 'Euro'
    USD = 'US Dollar'
    GBP = 'British Pound'
    SEK = 'Swedish Krona'
    NOK = 'Norwegian Krone'
    DKK = 'Danish Krone'
    CHF = 'Swiss Franc'
    CAD = 'Canadian Dollar'
    AUD = 'Australian Dollar'
    NZD = 'New Zealand Dollar'
    JPY = 'Japanese Yen'
    CNY = 'Chinese Yuan'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class SizeUnits(Enum):
    CM = 'Centimeter'
    MM = 'Millimeter'
    M = 'Meter'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class WeightUnits(Enum):
    KG = 'Kilogram'
    G = 'Gram'
    T = 'Ton'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]


class PriceChangeType(Enum):
    MANUAL = 'Manual Update'
    SYSTEM = 'System Update'
    IMPORT = 'Data Import'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class GoodsReceiptStatus(str, Enum):
    STARTED = 'STARTED'           # Påbörjad
    QC_PENDING = 'QC_PENDING'     # Kvalitetskontroll
    COMPLETED = 'COMPLETED'       # Klar
    CANCELLED = 'CANCELLED'       # Makulerad

    @classmethod
    def choices(cls):
        return [
            (key.name, key.value) for key in cls
        ]

class CustomFieldTypes(Enum):
    TEXT = 'Text'
    NUMBER = 'Number'
    DATE = 'Date'
    BOOLEAN = 'Boolean'
    SELECT = 'Select'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
class CustomFieldModelTypes(Enum):
    PRODUCT = 'Product'
    CUSTOMER = 'Customer'
    SUPPLIER = 'Supplier'
    PURCHASE = 'Purchase Documents'
    SALES = 'Sales Documents'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class VATZone(Enum):
    DOMESTIC = 'Domestic'
    DOMESTIC_EXCEPTION = 'Domestic Exception'
    EUROPE = 'Europe, default'
    EXPORT = 'Export, default'
    
    # EU Member States
    AT = 'Austria'
    BE = 'Belgium'
    BG = 'Bulgaria'
    HR = 'Croatia'
    CY = 'Cyprus'
    CZ = 'Czech Republic'
    DK = 'Denmark'
    EE = 'Estonia'
    FI = 'Finland'
    FR = 'France'
    DE = 'Germany'
    GR = 'Greece'
    HU = 'Hungary'
    IE = 'Ireland'
    IT = 'Italy'
    LV = 'Latvia'
    LT = 'Lithuania'
    LU = 'Luxembourg'
    MT = 'Malta'
    NL = 'Netherlands'
    PL = 'Poland'
    PT = 'Portugal'
    RO = 'Romania'
    SK = 'Slovakia'
    SI = 'Slovenia'
    ES = 'Spain'
    SE = 'Sweden'

    # Major Non-EU Countries
    AU = 'Australia'
    BR = 'Brazil'
    CA = 'Canada'
    CN = 'China'
    IN = 'India'
    IL = 'Israel'
    JP = 'Japan'
    KR = 'South Korea'
    MX = 'Mexico'
    NZ = 'New Zealand'
    NO = 'Norway'
    RU = 'Russia'
    SA = 'Saudi Arabia'
    SG = 'Singapore'
    CH = 'Switzerland'
    TR = 'Türkiye'
    AE = 'United Arab Emirates'
    GB = 'United Kingdom'
    US = 'United States'
    ZA = 'South Africa'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]

class CountryCodes(Enum):
    # EU Member States
    AT = 'Austria'
    BE = 'Belgium'
    BG = 'Bulgaria'
    HR = 'Croatia'
    CY = 'Cyprus'
    CZ = 'Czech Republic'
    DK = 'Denmark'
    EE = 'Estonia'
    FI = 'Finland'
    FR = 'France'
    DE = 'Germany'
    GR = 'Greece'
    HU = 'Hungary'
    IE = 'Ireland'
    IT = 'Italy'
    LV = 'Latvia'
    LT = 'Lithuania'
    LU = 'Luxembourg'
    MT = 'Malta'
    NL = 'Netherlands'
    PL = 'Poland'
    PT = 'Portugal'
    RO = 'Romania'
    SK = 'Slovakia'
    SI = 'Slovenia'
    ES = 'Spain'
    SE = 'Sweden'

    # Major Non-EU Countries
    AU = 'Australia'
    BR = 'Brazil'
    CA = 'Canada'
    CN = 'China'
    IN = 'India'
    IL = 'Israel'
    JP = 'Japan'
    KR = 'South Korea'
    MX = 'Mexico'
    NZ = 'New Zealand'
    NO = 'Norway'
    RU = 'Russia'
    SA = 'Saudi Arabia'
    SG = 'Singapore'
    CH = 'Switzerland'
    TR = 'Türkiye'
    AE = 'United Arab Emirates'
    GB = 'United Kingdom'
    US = 'United States'
    ZA = 'South Africa'

    @classmethod
    def choices(cls):
        return [(choice.name, choice.value) for choice in cls]
    
    @classmethod
    def eu_members(cls):
        """Returns a list of tuples containing only EU member states"""
        eu_codes = {'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 
                   'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 
                   'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'}
        return [(choice.name, choice.value) for choice in cls if choice.name in eu_codes]
    
    @classmethod
    def non_eu_members(cls):
        """Returns a list of tuples containing only non-EU countries"""
        eu_codes = {'AT', 'BE', 'BG', 'HR', 'CY', 'CZ', 'DK', 'EE', 'FI', 'FR', 
                   'DE', 'GR', 'HU', 'IE', 'IT', 'LV', 'LT', 'LU', 'MT', 'NL', 
                   'PL', 'PT', 'RO', 'SK', 'SI', 'ES', 'SE'}
        return [(choice.name, choice.value) for choice in cls if choice.name not in eu_codes]