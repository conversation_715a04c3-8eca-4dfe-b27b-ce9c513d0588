# core/models.py

from django.contrib.auth.models import AbstractBaseUser, PermissionsMixin
from django.core.validators import validate_email
from django.db import models
from apps.base.models import CompanyFKRel
from apps.base.models import BaseModel
from .managers import CustomUserManager
from utils.enums import (
    EmployeeRole, InventoryValuation, Languages, Currencies, TermType, 
    NumberSequenceType, VATZone, CustomFieldTypes, CustomFieldModelTypes
)
from django.contrib.contenttypes.models import ContentType
from decimal import Decimal
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from django.db.models import Q
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation

optional = {'null': True, 'blank': True}


class Company(BaseModel):
    name = models.CharField(max_length=255)
    registration_number = models.CharField(max_length=50, unique=True)
    vat_number = models.CharField(max_length=50, blank=True)
    address = models.TextField()
    phone = models.CharField(max_length=50)
    email = models.EmailField()
    website = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)

    def save(self, *args, **kwargs):
        creating = not self.pk  # Check if this is a new company
        super().save(*args, **kwargs)
        
        if creating:
            # Create default company settings
            from apps.core.models import Currency  # Import here to avoid circular imports
            default_currency = Currency.objects.filter(company=self).first()
            CompanySetting.objects.create(
                company=self,
                default_currency=default_currency,
                timezone='UTC'
            )

    class Meta:
        verbose_name_plural = "companies"

    def __str__(self):
        return self.name


    
class User(AbstractBaseUser, PermissionsMixin, BaseModel):
    username = models.CharField(max_length=255, default='-')
    email = models.EmailField(unique=True, validators=[validate_email])
    first_name = models.CharField(max_length=255, **optional)
    last_name = models.CharField(max_length=255, **optional)
    phone = models.CharField(max_length=255, **optional)
    is_employee = models.BooleanField(default=False)
    companies = models.ManyToManyField(
        Company,
        through='Employee',
        related_name='employees'
    )
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False, help_text="Will be True after the user has set their password through the welcome email")

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []
    
    objects = CustomUserManager()

    @property
    def full_name(self):
        return f"{self.first_name or ''} {self.last_name or ''}"
    
    def save(self, *args, **kwargs):
        if not self.username or self.username == '-':
            self.username = self.email.split('@')[0]
        super().save(*args, **kwargs)
    
    class Meta: 
        ordering = ['-id']
    
    def __str__(self) -> str:
        if self.email and self.first_name:
            return f"{self.id}, {self.email} - {self.first_name}"
        elif self.email:
            return self.email
        elif self.first_name: 
            return self.first_name
        else: 
            return str(self.uuid)
        
    

class ActiveCompanySession(CompanyFKRel):
    user = models.ForeignKey(
        'User',
        on_delete=models.CASCADE,
        related_name='active_company_sessions'
    )
    is_active = models.BooleanField(default=True)

    class Meta:
        constraints = [
            models.UniqueConstraint(
                fields=['user'],
                condition=models.Q(is_active=True),
                name='unique_active_company_per_user'
            )
        ]


class Employee(CompanyFKRel):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='company_employee')
    role = models.CharField(
        max_length=40,
        choices=EmployeeRole.choices(),
        default=EmployeeRole.WAREHOUSE_WORKER.name
    )
    primary_warehouse = models.ForeignKey(
        'Warehouse',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='primary_warehouse'
    )
    is_active = models.BooleanField(default=True)
    joined_at = models.DateTimeField(auto_now_add=True)
    left_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        unique_together = ['user', 'company']

    def __str__(self):
        return f"{self.user.email} - {self.company.name} ({self.role})"


class Warehouse(CompanyFKRel):
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=50)
    barcode = models.CharField(max_length=50, blank=True)
    address = models.TextField()
    phone = models.CharField(max_length=50, blank=True)
    email = models.EmailField(blank=True)
    is_active = models.BooleanField(default=True)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='children')

    class Meta:
        unique_together = ['company', 'number']

    def __str__(self):
        return f"{self.name} ({self.company.name})"


class CompanySetting(BaseModel):
    company = models.OneToOneField(
        Company,
        on_delete=models.CASCADE,
        related_name='company_settings'
    )
    default_currency = models.ForeignKey(
        'Currency',
        on_delete=models.PROTECT,
        related_name='companies_default',
        **optional
    )
    default_warehouse = models.ForeignKey(
        'Warehouse',
        on_delete=models.PROTECT,
        related_name='companies_default',
        **optional
    )
    inventory_valuation_method = models.CharField(
        max_length=50,
        choices=InventoryValuation.choices(),
        default=InventoryValuation.FIFO.name
    )
    timezone = models.CharField(max_length=50, default='UTC')

    # New default fields for Products
    default_product_type = models.ForeignKey(
        'products.ProductType',
        on_delete=models.SET_NULL,
        related_name='companies_default',
        **optional
    )
    default_product_supplier = models.ForeignKey(
        'purchasing.Supplier',
        on_delete=models.SET_NULL,
        related_name='companies_default_as_supplier',
        **optional
    )
    default_product_category = models.ForeignKey(
        'products.ProductCategory',
        on_delete=models.SET_NULL,
        related_name='companies_default',
        **optional
    )
    default_unit_of_measure = models.ForeignKey(
        'UnitOfMeasure',
        on_delete=models.SET_NULL,
        related_name='companies_default',
        **optional
    )

    # New default fields for Customers
    default_customer_payment_term = models.ForeignKey(
        'PaymentTerm',
        on_delete=models.SET_NULL,
        related_name='companies_default_customer_payment_terms',
        **optional
    )
    default_customer_delivery_term = models.ForeignKey(
        'DeliveryTerm',
        on_delete=models.SET_NULL,
        related_name='companies_default_customer_delivery_terms',
        **optional
    )
    default_customer_language = models.ForeignKey(
        'Language',
        on_delete=models.SET_NULL,
        related_name='companies_default_customer',
        **optional
    )
    default_customer_vat_zone = models.CharField(
        max_length=50,
        choices=VATZone.choices(),
        default=VATZone.DOMESTIC.name
    )
    default_customer_category = models.ForeignKey(
        'sales.CustomerCategory',
        on_delete=models.SET_NULL,
        related_name='companies_default',
        **optional
    )

    # New default fields for Suppliers
    default_supplier_payment_term = models.ForeignKey(
        'PaymentTerm',
        on_delete=models.SET_NULL,
        related_name='companies_default_supplier_payment_terms',
        **optional
    )
    default_supplier_delivery_term = models.ForeignKey(
        'DeliveryTerm',
        on_delete=models.SET_NULL,
        related_name='companies_default_supplier_delivery_terms',
        **optional
    )
    default_supplier_language = models.ForeignKey(
        'Language',
        on_delete=models.SET_NULL,
        related_name='companies_default_supplier',
        **optional
    )
    default_supplier_vat_zone = models.CharField(
        max_length=50,
        choices=VATZone.choices(),
        default=VATZone.DOMESTIC.name
    )
    default_supplier_category = models.ForeignKey(
        'purchasing.SupplierCategory',
        on_delete=models.SET_NULL,
        related_name='companies_default',
        **optional
    )

    def __str__(self):
        return f"Settings for {self.company.name}"
    

class WarehouseSetting(CompanyFKRel):
    warehouse = models.OneToOneField(Warehouse, on_delete=models.CASCADE, related_name='warehouse_settings')
    default_receiving_location = models.ForeignKey('inventory.Location', on_delete=models.PROTECT, related_name='default_receiving_location')
    default_shipping_location = models.ForeignKey('inventory.Location', on_delete=models.PROTECT, related_name='default_shipping_location')
    requires_quality_check = models.BooleanField(default=False)
    auto_assign_tasks = models.BooleanField(default=True)

    def __str__(self):
        return f"Settings for {self.warehouse.name}"

class AuditLog(CompanyFKRel):
    """
    Model to store audit logs for all operations
    """
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True
    )
    content_type = models.ForeignKey(
        ContentType,
        on_delete=models.CASCADE
    )
    object_id = models.UUIDField()
    action = models.CharField(max_length=50)
    data = models.JSONField(null=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['company', 'content_type', 'object_id']),
            models.Index(fields=['created_at']),
        ]



class Language(CompanyFKRel):
    language = models.CharField(
        max_length=50,
        choices=Languages.choices()
    )
    is_active = models.BooleanField(default=True)



class Currency(CompanyFKRel):
    currency = models.CharField(
        max_length=3,
        choices=Currencies.choices()
    )
    is_active = models.BooleanField(default=True)


class Translation(CompanyFKRel):
    language = models.ForeignKey(Language, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    description = models.TextField()
    abbreviation = models.CharField(max_length=10, blank=True)

    class Meta:
        unique_together = ['language', 'object_id', 'content_type']

    def __str__(self):
        return f"{self.language.language} - {self.name}"

class VATRates(CompanyFKRel):
    """Separate VAT rates into their own model to avoid duplication"""
    domestic_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))])
    domestic_exception_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))])
    europe_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))])
    export_rate = models.DecimalField(max_digits=5, decimal_places=2, validators=[MinValueValidator(Decimal('0')), MaxValueValidator(Decimal('100'))])
    country_specific_rates = models.JSONField(default=dict)

    class Meta:
        abstract = True


class UnitOfMeasure(CompanyFKRel):
    name = models.CharField(max_length=50)
    abbreviation = models.CharField(max_length=10)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['company', 'abbreviation']
        indexes = [
            models.Index(fields=['company', 'abbreviation']),
        ]

class PaymentTerm(CompanyFKRel):
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=TermType.choices())
    days = models.IntegerField(null=True, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['company', 'name']
        indexes = [
            models.Index(fields=['company', 'name']),
            models.Index(fields=['company', 'type']),
            models.Index(fields=['company', 'days']),
        ]
        ordering = ['-id']

class DeliveryTerm(CompanyFKRel):
    name = models.CharField(max_length=255)
    type = models.CharField(max_length=255, choices=TermType.choices())
    days = models.IntegerField(null=True, blank=True)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['company', 'name']
        indexes = [
            models.Index(fields=['company', 'name']),
            models.Index(fields=['company', 'type']),
            models.Index(fields=['company', 'days']),
        ]


class DeliveryMethod(CompanyFKRel):
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['company', 'name']
        indexes = [
            models.Index(fields=['company', 'name']),
        ]

class NumberSequence(CompanyFKRel):
    sequence_type = models.CharField(max_length=50, choices=NumberSequenceType.choices())
    next_number = models.CharField(max_length=50)
    company = models.ForeignKey(
        'Company',
        on_delete=models.CASCADE,
        related_name='number_sequences'
    )
    
    class Meta:
        unique_together = ['company', 'sequence_type']

    def clean(self):
        # Check if the next number would conflict with existing records
        if not self.pk:  # Only check on creation
            return

        existing_number = None
        if self.sequence_type == NumberSequenceType.PURCHASE_ORDER.name:
            from apps.purchasing.models import PurchaseOrder
            existing_number = PurchaseOrder.objects.filter(
                company=self.company,
                number__gte=self.next_number
            ).order_by('-number').first()
        elif self.sequence_type == NumberSequenceType.SUPPLIER_INVOICE.name:
            from apps.purchasing.models import SupplierInvoice
            existing_number = SupplierInvoice.objects.filter(
                company=self.company,
                number__gte=self.next_number
            ).order_by('-number').first()
        elif self.sequence_type == NumberSequenceType.SALES_ORDER.name:
            from apps.sales.models import SalesOrder
            existing_number = SalesOrder.objects.filter(
                company=self.company,
                number__gte=self.next_number
            ).order_by('-number').first()
        elif self.sequence_type == NumberSequenceType.SALES_INVOICE.name:
            from apps.sales.models import SalesInvoice
            existing_number = SalesInvoice.objects.filter(
                company=self.company,
                number__gte=self.next_number
            ).order_by('-number').first()

        if existing_number:
            raise ValidationError(
                _('Next number cannot be lower than existing numbers. '
                  f'Found existing record with number: {existing_number.number}')
            )

    def __str__(self):
        return f"{self.get_sequence_type_display()} - Next: {self.next_number}"

class CustomField(CompanyFKRel):

    name = models.CharField(max_length=100)
    field_type = models.CharField(max_length=20, choices=CustomFieldTypes.choices())
    model_type = models.CharField(max_length=20, choices=CustomFieldModelTypes.choices())
    description = models.TextField(blank=True)
    is_required = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    select_options = models.JSONField(
        null=True, 
        blank=True,
        help_text="Options for select field type, stored as JSON array"
    )
    default_value = models.JSONField(
        null=True,
        blank=True,
        help_text="Default value for the field"
    )
    validation_regex = models.CharField(
        max_length=255,
        blank=True,
        help_text="Regular expression for validation"
    )
    order = models.IntegerField(default=0)

    class Meta:
        unique_together = ['company', 'name', 'model_type']
        ordering = ['order', 'name']

    def __str__(self):
        return f"{self.model_type} - {self.name}"


class CustomFieldValue(CompanyFKRel):
    custom_field = models.ForeignKey(
        CustomField,
        on_delete=models.CASCADE,
        related_name='values'
    )
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.UUIDField()
    content_object = GenericForeignKey('content_type', 'object_id')
    value = models.JSONField()

    class Meta:
        unique_together = ['company', 'custom_field', 'content_type', 'object_id']
        indexes = [
            models.Index(fields=['content_type', 'object_id']),
        ]

    def __str__(self):
        return f"{self.custom_field.name}: {self.value}"
