{% extends "invoices/base.html" %}

{% block title %}Supplier Invoice #{{ invoice.number }}{% endblock %}

{% block invoice_type %}Supplier Invoice{% endblock %}

{% block invoice_subtitle %}
{% if invoice.is_credit_note %}Credit Note{% endif %}
{% endblock %}

{% block company_logo %}{{ company_logo_url|default:"" }}{% endblock %}

{% block from_title %}Supplier{% endblock %}

{% block from_details %}
<div>
  <p><strong>{{ invoice.supplier.name }}</strong></p>
  <p>{{ invoice.supplier.address|linebreaksbr }}</p>
  {% if invoice.supplier.tax_number %}<p>Tax ID: {{ invoice.supplier.tax_number }}</p>{% endif %}
  {% if invoice.supplier.vat_zone %}<p>VAT Zone: {{ invoice.supplier.vat_zone }}</p>{% endif %}

  {% if invoice.supplier.contact_person %}
  <p>Contact: {{ invoice.supplier.contact_person.name }}{% if invoice.supplier.contact_person.role %}, {{
    invoice.supplier.contact_person.role }}{% endif %}</p>
  <p>{{ invoice.supplier.contact_person.email }}{% if invoice.supplier.contact_person.phone %} | {{
    invoice.supplier.contact_person.phone }}{% endif %}</p>
  {% endif %}
</div>
{% endblock %}

{% block to_title %}Billed To{% endblock %}

{% block to_details %}
<div>
  <p><strong>{{ company.name }}</strong></p>
  <p>{{ company.address|linebreaksbr }}</p>
  {% if company.registration_number %}<p>Reg. No: {{ company.registration_number }}</p>{% endif %}
  {% if company.vat_number %}<p>VAT ID: {{ company.vat_number }}</p>{% endif %}
</div>
{% endblock %}

{% block invoice_number_label %}Supplier Invoice Number{% endblock %}
{% block invoice_number %}{{ invoice.invoice_number }}{% endblock %}

{% block invoice_date_label %}Invoice Date{% endblock %}
{% block invoice_date %}{{ invoice.invoice_date|date:"F j, Y" }}{% endblock %}

{% block due_date %}{{ invoice.due_date|date:"F j, Y" }}{% endblock %}

{% block status %}
{% if invoice.status == 'draft' %}
<span class="badge badge-info">Draft</span>
{% elif invoice.status == 'received' %}
<span class="badge badge-info">Received</span>
{% elif invoice.status == 'approved' %}
<span class="badge badge-warning">Approved</span>
{% elif invoice.status == 'paid' %}
<span class="badge badge-success">Paid</span>
{% elif invoice.status == 'disputed' %}
<span class="badge badge-danger">Disputed</span>
{% elif invoice.status == 'cancelled' %}
<span class="badge badge-danger">Cancelled</span>
{% else %}
<span class="badge">{{ invoice.status|title }}</span>
{% endif %}
{% endblock %}

{% block payment_terms %}
{{ invoice.payment_term.name|default:"" }}
{% endblock %}

{% block currency %}{{ invoice.currency.currency }}{% endblock %}

{% block extra_metadata %}
{% if invoice.purchase_order %}
<tr>
  <th>PO Reference:</th>
  <td>{{ invoice.purchase_order.number }}</td>
</tr>
{% endif %}
{% if invoice.internal_reference %}
<tr>
  <th>Internal Reference:</th>
  <td>{{ invoice.internal_reference }}</td>
</tr>
{% endif %}
{% endblock %}

{% block invoice_items %}
{% for line in invoice.lines.all %}
<tr>
  <td>{{ line.product.name|default:"-" }}</td>
  <td>{{ line.description }}</td>
  <td>{{ line.quantity }} {{ line.product.unit|default:"" }}</td>
  <td>{{ line.unit_price|floatformat:2 }}</td>
  <td>{{ line.tax_rate|floatformat:2 }}%</td>
  <td class="text-right">{{ line.total_amount|floatformat:2 }}</td>
</tr>
{% endfor %}
{% endblock %}

{% block subtotal %}{{ invoice.total_net_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block tax_amount %}{{ invoice.total_tax_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block discount %}{{ invoice.total_discount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block total %}{{ invoice.total_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}

{% block payment_info %}
<div>
  <p><strong>Supplier Payment Details:</strong></p>
  <p>Account Name: {{ invoice.supplier.bank_account_name|default:"" }}</p>
  <p>Account Number: {{ invoice.supplier.bank_account_number|default:"" }}</p>
  <p>Bank: {{ invoice.supplier.bank_name|default:"" }}</p>
  <p>IBAN: {{ invoice.supplier.iban|default:"" }}</p>
  <p>SWIFT/BIC: {{ invoice.supplier.swift|default:"" }}</p>

  {% if invoice.payment_reference %}
  <p><strong>Payment Reference:</strong> {{ invoice.payment_reference }}</p>
  {% endif %}
</div>
{% endblock %}