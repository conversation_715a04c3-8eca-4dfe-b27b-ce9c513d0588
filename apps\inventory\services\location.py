from django.db import transaction
from apps.inventory.models import Location
from apps.inventory.services.base import BaseLocationService
from typing import Dict


class LocationService(BaseLocationService):
    @transaction.atomic
    def setup_special_locations(self) -> Dict[str, Location]:
        """
        Sets up special locations required for company operations
        Returns dict with created/existing special locations
        """
        special_locations = {}
        
        # Setup Quality Control location if not exists
        qc_location = Location.objects.filter(
            company=self.company,
            is_quality_control=True
        ).first()
        
        if not qc_location:
            qc_location = Location.objects.create(
                company=self.company,
                name="Quality Control",
                number="QC",
                is_quality_control=True,
                is_active=True
            )
        special_locations['quality_control'] = qc_location

        # Setup Returns Stock location if not exists
        returns_location = Location.objects.filter(
            company=self.company,
            is_returns=True
        ).first()
        
        if not returns_location:
            returns_location = Location.objects.create(
                company=self.company,
                name="Returns Stock",
                number="RET",
                is_returns=True,
                is_active=True
            )
        special_locations['returns_stock'] = returns_location

        return special_locations