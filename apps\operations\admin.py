from django.contrib import admin
from django.db.models import Sum, F
from django.utils.html import format_html
from .models import PickingTask, PickingTaskLine

class PickingTaskLineInline(admin.TabularInline):
    model = PickingTaskLine
    extra = 0
    raw_id_fields = ['sales_order_line', 'location', 'lot']
    fields = ['sales_order_line', 'location', 'lot', 'quantity', 'picked_quantity']
    #readonly_fields = ['picked_quantity']
    show_change_link = True

    def get_queryset(self, request):
        # Optimize queryset by selecting related fields
        return super().get_queryset(request).select_related(
            'sales_order_line__product',
            'location',
            'lot'
        )

@admin.register(PickingTask)
class PickingTaskAdmin(admin.ModelAdmin):
    list_display = [
        'number', 'sales_order', 'status', 'assigned_to',
        'started_at', 'completed_at', 'picking_progress'
    ]
    list_filter = ['status', 'assigned_to']
    search_fields = ['number', 'sales_order__number']
    raw_id_fields = ['sales_order', 'assigned_to']
    inlines = [PickingTaskLineInline]
    readonly_fields = ['started_at', 'completed_at']
    date_hierarchy = 'created_at'

    fieldsets = (
        (None, {
            'fields': ('company', 'sales_order', 'assigned_to', 'status', 'priority')
        }),
        ('Timing Information', {
            'fields': ('started_at', 'completed_at')
        }),
        ('Additional Information', {
            'fields': ('notes',),
            'classes': ('collapse',)
        }),
    )

    def picking_progress(self, obj):
        total_lines = obj.lines.count()
        if not total_lines:
            return "No lines"
            
        picked_quantity = sum(
            line.picked_quantity or 0 
            for line in obj.lines.all()
        )
        total_quantity = sum(
            line.quantity or 0 
            for line in obj.lines.all()
        )
        
        if total_quantity == 0:
            percentage = 0
        else:
            percentage = (picked_quantity / total_quantity) * 100
            
        # Split into two format_html calls to avoid the formatting issue
        progress_bar = format_html(
            '<div style="width:100px; border:1px solid #ccc; padding:2px">'
            '<div style="width:{}%; background-color:#00a65a; height:20px"></div>'
            '</div>',
            int(percentage)  # Convert to int for the width
        )
        
        return format_html(
            '{} {}%',
            progress_bar,
            int(percentage)  # Convert to int for display
        )
    picking_progress.short_description = 'Progress'

    def get_queryset(self, request):
        # Optimize queryset by selecting related fields
        return super().get_queryset(request).select_related(
            'company',
            'sales_order',
            'assigned_to'
        )

@admin.register(PickingTaskLine)
class PickingTaskLineAdmin(admin.ModelAdmin):
    list_display = [
        'picking_task', 'sales_order_line', 'location',
        'quantity', 'picked_quantity'
    ]
    list_filter = ['picking_task__status', 'location']
    search_fields = [
        'picking_task__number',
        'sales_order_line__sales_order__number'
    ]
    raw_id_fields = ['picking_task', 'sales_order_line', 'location']
    readonly_fields = ['picked_quantity']

    def get_queryset(self, request):
        # Optimize queryset by selecting related fields
        return super().get_queryset(request).select_related(
            'picking_task__sales_order',
            'sales_order_line__product',
            'location'
        )