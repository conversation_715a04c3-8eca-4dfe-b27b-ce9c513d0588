from typing import Dict, List
from datetime import datetime, timedelta
from django.db.models import Count, Sum, F, Q
from django.utils import timezone
from apps.inventory.models import Location, InventoryItem, InventoryMovement
from apps.operations.models import PickingTask, PickingTaskLine
from .base import BaseOperationService
from typing import Optional

class WarehouseOptimizationService(BaseOperationService):
    def suggest_product_placement(self) -> List[Dict]:
        """
        Suggest optimal product placement based on:
        - Picking frequency
        - Product velocity
        - Product relationships
        - Storage requirements
        """
        # Get picking frequency per product
        thirty_days_ago = timezone.now() - timedelta(days=30)
        picking_frequency = (
            PickingTaskLine.objects.filter(
                company=self.company,
                picking_task__created_at__gte=thirty_days_ago
            )
            .values('sales_order_line__product')
            .annotate(
                pick_count=Count('id'),
                total_quantity=Sum('quantity')
            )
        )

        # Get current storage locations
        current_locations = InventoryItem.objects.filter(
            company=self.company,
            quantity__gt=0
        ).select_related('product', 'location')

        suggestions = []
        for freq in picking_frequency:
            product_id = freq['sales_order_line__product']
            current_locs = [
                item for item in current_locations 
                if item.product_id == product_id
            ]

            # Calculate optimal zone based on frequency
            if freq['pick_count'] > 100:
                suggested_zone = 'HIGH_VELOCITY'
            elif freq['pick_count'] > 50:
                suggested_zone = 'MEDIUM_VELOCITY'
            else:
                suggested_zone = 'LOW_VELOCITY'

            suggestions.append({
                'product_id': product_id,
                'current_locations': current_locs,
                'pick_frequency': freq['pick_count'],
                'suggested_zone': suggested_zone,
                'reason': f"Product picked {freq['pick_count']} times in last 30 days"
            })

        return suggestions

    def optimize_picking_routes(
        self,
        picking_task_id: int,
        algorithm: str = 'TSP'
    ) -> List[Dict]:
        """
        Generate optimized picking route for a picking task
        Supports different routing algorithms
        """
        picking_task = PickingTask.objects.get(
            company=self.company,
            id=picking_task_id
        )

        # Get picking lines with locations
        picking_lines = (
            picking_task.lines
            .filter(picked_quantity__lt=F('quantity'))
            .select_related('location', 'sales_order_line__product')
            .order_by('location__code')  # Basic ordering
        )

        if algorithm == 'TSP':
            # Implement Traveling Salesman Problem algorithm
            # This is a simplified version - you'd want a proper TSP implementation
            route = self._apply_tsp_algorithm(picking_lines)
        elif algorithm == 'SNAKE':
            # Snake or S-shape routing
            route = self._apply_snake_routing(picking_lines)
        else:
            raise ValueError(f"Unsupported routing algorithm: {algorithm}")

        return route

    def _apply_tsp_algorithm(self, picking_lines) -> List[Dict]:
        """
        Apply TSP algorithm to optimize picking route
        This is a simplified version - you'd want a proper TSP implementation
        """
        # Implement a simple nearest neighbor algorithm
        route = []
        remaining_lines = list(picking_lines)
        
        if not remaining_lines:
            return route

        # Start from the first location (ideally would be warehouse entrance)
        current_location = remaining_lines[0].location
        
        while remaining_lines:
            # Find nearest location
            nearest_idx = 0
            nearest_distance = float('inf')
            
            for i, line in enumerate(remaining_lines):
                # In a real implementation, you would calculate actual distance
                # This is a simplified version using location codes
                distance = abs(
                    int(line.location.number.replace('LOC-', '')) - 
                    int(current_location.number.replace('LOC-', ''))
                )
                
                if distance < nearest_distance:
                    nearest_distance = distance
                    nearest_idx = i

            # Add to route
            next_line = remaining_lines.pop(nearest_idx)
            route.append({
                'sequence': len(route) + 1,
                'location': next_line.location,
                'picking_line_id': next_line.id,
                'product': next_line.sales_order_line.product,
                'quantity': next_line.quantity - next_line.picked_quantity
            })
            current_location = next_line.location

        return route

    def _apply_snake_routing(self, picking_lines) -> List[Dict]:
        """
        Apply snake routing (S-shape) algorithm
        Assumes warehouse has aisles and orders locations by aisle
        """
        # Group by aisle
        aisle_groups = {}
        for line in picking_lines:
            # Assume location number format: AISLE-LEVEL-POSITION
            aisle = line.location.number.split('-')[0]
            if aisle not in aisle_groups:
                aisle_groups[aisle] = []
            aisle_groups[aisle].append(line)

        route = []
        # Process each aisle in alternating directions
        aisles = sorted(aisle_groups.keys())
        for i, aisle in enumerate(aisles):
            aisle_lines = aisle_groups[aisle]
            if i % 2 == 0:  # Forward direction
                sorted_lines = sorted(
                    aisle_lines,
                    key=lambda x: x.location.number
                )
            else:  # Reverse direction
                sorted_lines = sorted(
                    aisle_lines,
                    key=lambda x: x.location.number,
                    reverse=True
                )

            for line in sorted_lines:
                route.append({
                    'sequence': len(route) + 1,
                    'location': line.location,
                    'picking_line_id': line.id,
                    'product': line.sales_order_line.product,
                    'quantity': line.quantity - line.picked_quantity
                })

        return route

    def calculate_warehouse_utilization(self) -> Dict:
        """
        Calculate warehouse storage utilization metrics
        """
        locations = Location.objects.filter(
            company=self.company,
            is_storage=True
        ).annotate(
            items_count=Count('inventory_items'),
            total_quantity=Sum('inventory_items__quantity')
        )

        total_locations = locations.count()
        used_locations = locations.filter(items_count__gt=0).count()
        
        return {
            'total_locations': total_locations,
            'used_locations': used_locations,
            'utilization_rate': (used_locations / total_locations * 100) if total_locations else 0,
            'location_details': [
                {
                    'location': loc,
                    'items_count': loc.items_count,
                    'total_quantity': loc.total_quantity or 0,
                }
                for loc in locations
            ]
        }

    def suggest_restock_locations(self) -> List[Dict]:
        """
        Suggest locations for restocking based on:
        - Current utilization
        - Product velocity
        - Storage requirements
        """
        # Get empty or low-utilized locations
        available_locations = Location.objects.filter(
            company=self.company,
            is_storage=True
        ).annotate(
            items_count=Count('inventory_items')
        ).filter(
            items_count__lt=5  # Arbitrary threshold
        )

        # Get products needing restock
        low_stock_locations = Location.objects.filter(
            company=self.company,
            inventory_items__quantity__lt=F('inventory_items__product__min_stock_level')
        ).distinct()

        suggestions = []
        for loc in low_stock_locations:
            items_to_restock = loc.inventory_items.filter(
                quantity__lt=F('product__min_stock_level')
            )
            
            for item in items_to_restock:
                suggested_location = self._find_optimal_restock_location(
                    item.product,
                    available_locations
                )
                
                if suggested_location:
                    suggestions.append({
                        'product': item.product,
                        'current_location': loc,
                        'suggested_location': suggested_location,
                        'quantity_needed': item.product.min_stock_level - item.quantity,
                        'reason': 'Below minimum stock level'
                    })

        return suggestions

    def _find_optimal_restock_location(
        self,
        product,
        available_locations
    ) -> Optional[Location]:
        """Find the best location for restocking a product"""
        # In a real implementation, consider:
        # - Product dimensions
        # - Location dimensions
        # - Product velocity
        # - Proximity to picking areas
        # For now, return the first available location
        return available_locations.first()

    def analyze_warehouse_performance(
        self,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> Dict:
        """
        Analyze warehouse performance metrics
        """
        if not start_date:
            start_date = timezone.now() - timedelta(days=30)
        if not end_date:
            end_date = timezone.now()

        picking_tasks = PickingTask.objects.filter(
            company=self.company,
            created_at__range=(start_date, end_date)
        )

        completed_tasks = picking_tasks.filter(
            status='COMPLETED'
        )

        avg_completion_time = None
        if completed_tasks:
            total_time = sum(
                (task.completed_at - task.started_at).total_seconds()
                for task in completed_tasks
                if task.completed_at and task.started_at
            )
            avg_completion_time = total_time / completed_tasks.count() / 3600  # hours

        return {
            'total_tasks': picking_tasks.count(),
            'completed_tasks': completed_tasks.count(),
            'completion_rate': (
                completed_tasks.count() / picking_tasks.count() * 100
                if picking_tasks.count() > 0 else 0
            ),
            'avg_completion_time': avg_completion_time,
            'utilization': self.calculate_warehouse_utilization(),
            'movement_efficiency': self._calculate_movement_efficiency(
                start_date, end_date
            )
        }

    def _calculate_movement_efficiency(
        self,
        start_date: datetime,
        end_date: datetime
    ) -> Dict:
        """Calculate efficiency metrics for warehouse movements"""
        movements = InventoryMovement.objects.filter(
            company=self.company,
            created_at__range=(start_date, end_date)
        )

        successful_movements = movements.filter(status='COMPLETED')
        
        return {
            'total_movements': movements.count(),
            'successful_movements': successful_movements.count(),
            'success_rate': (
                successful_movements.count() / movements.count() * 100
                if movements.count() > 0 else 0
            ),
            'movement_types_breakdown': self._get_movement_types_breakdown(movements)
        }

    def _get_movement_types_breakdown(self, movements) -> Dict:
        """Get breakdown of movement types"""
        return movements.values('movement_type').annotate(
            count=Count('id'),
            total_quantity=Sum('lines__quantity')
        )