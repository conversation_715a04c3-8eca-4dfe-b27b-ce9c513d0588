# purchasing/services/supplier.py
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.db.models import Avg, Sum, F, Q
from datetime import timedelta, date
from django.utils import timezone
from ..models import Supplier, SupplierProduct, PurchaseOrder, GoodsReceipt
from utils.enums import QualityStatus
from .base import BasePurchasingService
from django.db.models import Case, When, Value, DecimalField

class SupplierService(BasePurchasingService):

    
    def evaluate_supplier_performance(
        self,
        supplier_id: int,
        start_date: date = None,
        end_date: date = None
    ) -> Dict:
        """
        Evaluates supplier performance based on:
        - On-time delivery
        - Order fulfillment accuracy
        - Price consistency
        - Quality (based on returns/rejections)
        """
        supplier = Supplier.objects.get(
            company=self.company,
            id=supplier_id
        )
        
        orders = PurchaseOrder.objects.filter(
            company=self.company,
            supplier=supplier,
            status__in=['COMPLETED', 'PARTIAL']
        )
        
        if start_date:
            orders = orders.filter(order_date__gte=start_date)
        if end_date:
            orders = orders.filter(order_date__lte=end_date)

        # Calculate metrics
        total_orders = orders.count()
        if total_orders == 0:
            return {
                'supplier': supplier,
                'metrics': None,
                'message': 'No orders in selected period'
            }

        on_time_deliveries = orders.filter(
            receipts__receipt_date__lte=F('expected_delivery_date')
        ).distinct().count()

        quality_issues = GoodsReceipt.objects.filter(
            company=self.company,
            purchase_order__supplier=supplier,
            quality_status__in=[QualityStatus.DAMAGED.name, QualityStatus.EXPIRED.name]
        ).count()

        return {
            'supplier': supplier,
            'metrics': {
                'total_orders': total_orders,
                'on_time_delivery_rate': (on_time_deliveries / total_orders) * 100,
                'quality_issue_rate': (quality_issues / total_orders) * 100,
                'average_lead_time': self._calculate_average_lead_time(orders),
                'price_trend': self._analyze_price_trend(supplier)
            }
        }

    def _calculate_average_lead_time(self, orders) -> float:
        lead_times = []
        for order in orders:
            if order.receipts.exists():
                first_receipt = order.receipts.earliest('receipt_date')
                lead_time = (first_receipt.receipt_date - order.order_date).days
                lead_times.append(lead_time)
        
        return sum(lead_times) / len(lead_times) if lead_times else 0

    def _analyze_price_trend(self, supplier: Supplier) -> Dict:
        """Analyze price trends for supplier products"""
        six_months_ago = timezone.now().date() - timedelta(days=180)
        
        return SupplierProduct.objects.filter(
            company=self.company,
            supplier=supplier
        ).annotate(
            price_change=F('price') - F('previous_price'),
            price_change_percentage=Case(
                When(previous_price=0, then=Value(0)),
                default=(F('price') - F('previous_price')) / F('previous_price') * 100,
                output_field=DecimalField()
            )
        ).aggregate(
            avg_price_change=Avg('price_change_percentage')
        )

    @transaction.atomic
    def update_supplier_products(
        self,
        supplier_id: int,
        products: List[Dict]
    ) -> List[SupplierProduct]:
        """Update or create supplier products with new prices"""
        supplier = Supplier.objects.get(
            company=self.company,
            id=supplier_id
        )

        product_ids = [product.get('product').id for product in products if product.get('product')]
        variant_ids = [product.get('variant').id for product in products if product.get('variant')]
        try:
            updated_products = []
            for product_data in products:
                product = product_data.pop('product')
                variant = product_data.pop('variant')
                # Try to get existing product
                supplier_product = SupplierProduct.objects.filter(
                    company=self.company,
                    supplier=supplier,
                    product=product,
                    variant=variant
                ).first()
                
                if supplier_product:
                    # Update existing product
                    for key, value in product_data.items():
                        if hasattr(supplier_product, key):
                            setattr(supplier_product, key, value)
                    supplier_product.save()
                else:
                    # Create new product
                    supplier_product = SupplierProduct.objects.create(
                        company=self.company,
                        supplier=supplier,
                        product=product,
                        variant=variant,
                        **product_data
                    )
                
                updated_products.append(supplier_product)
            
            SupplierProduct.objects.filter(
                company=self.company,
                supplier=supplier
            ).exclude(
                product__id__in=product_ids,
                variant__id__in=variant_ids
            ).delete()
            return updated_products
        except Exception as e:
            from traceback_with_variables import format_exc
            print(f"Error: {format_exc(e)}")
            raise e

