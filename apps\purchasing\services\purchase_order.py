# purchasing/services/purchase_order.py
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.db.models import F, Sum
from ..models import PurchaseOrder, PurchaseOrderLine, Supplier
from apps.inventory.models import Location
from .base import BasePurchasingService
from datetime import date
from django.utils import timezone
from utils.enums import PurchaseOrderStatus
from django.core.exceptions import ValidationError

class PurchaseOrderService(BasePurchasingService):
    @transaction.atomic
    def create_purchase_order(
        self,
        supplier_id: int,
        receiving_location_id: int,
        lines: List[Dict],
        notes: str = None,
        expected_delivery_date: date = None,
        currency: str = None,
        payment_term: str = None,
        delivery_term: str = None,
        currency_rate: Decimal = None,
        delivery_method: str = None,
        our_reference: str = None,
        your_reference: str = None
    ) -> PurchaseOrder:
        # Validate supplier
        supplier = Supplier.objects.get(
            company=self.company,
            id=supplier_id,
            is_active=True
        )
        
        # Validate receiving location
        try:
            location = Location.objects.get(
                company=self.company,
                id=receiving_location_id,
                is_receiving=True
            )
        except Location.DoesNotExist:
            raise ValidationError("Invalid receiving location or location is not marked for receiving.")
        
        # Create PO with next number in sequence
        po = PurchaseOrder.objects.create(
            company=self.company,
            number=self._generate_po_number(),
            supplier=supplier,
            receiving_location=location,
            order_date=timezone.now().date(),
            expected_delivery_date=expected_delivery_date,
            notes=notes,
            currency=currency,
            payment_term=payment_term,
            delivery_term=delivery_term,
            currency_rate=currency_rate,
            our_reference=our_reference,
            your_reference=your_reference,
            delivery_method=delivery_method
        )
        
        # Create PO lines
        total_amount = Decimal('0')
        for line in lines:
            po_line = PurchaseOrderLine.objects.create(
                company=self.company,
                purchase_order=po,
                product_id=line['product_id'],
                variant_id=line.get('variant_id'),
                quantity=Decimal(str(line['quantity'])),
                unit_price=Decimal(str(line['unit_price'])),
                tax_rate=Decimal(str(line.get('tax_rate', '0'))),
                expected_delivery_date=line.get('expected_delivery_date')
            )

            total_amount += po_line.quantity * po_line.unit_price
        
        po.total_amount = total_amount
        po.save()
        
        return po

    def confirm_purchase_order(self, po_id: int) -> PurchaseOrder:
        """Mark PO as confirmed after supplier acknowledgment"""
        po = self._get_purchase_order(po_id)
        if po.status != PurchaseOrderStatus.DRAFT.name:
            raise ValueError("Only draft orders can be confirmed")
        
        po.status = PurchaseOrderStatus.CONFIRMED.name
        po.save()
        return po

    def cancel_purchase_order(self, po_id: int, reason: str) -> PurchaseOrder:
        """Cancel PO if not yet received"""
        po = self._get_purchase_order(po_id)
        if po.status in [PurchaseOrderStatus.PARTIAL.name, PurchaseOrderStatus.COMPLETED.name]:
            raise ValueError("Cannot cancel orders with receipts")
        
        po.status = PurchaseOrder.Status.CANCELLED
        po.notes = f"{po.notes}\nCancellation reason: {reason}"
        po.save()
        return po

    def _get_purchase_order(self, po_id: int) -> PurchaseOrder:
        return PurchaseOrder.objects.get(
            company=self.company,
            id=po_id
        )

    def _generate_po_number(self) -> str:
        """
        Generates PO number in format: PO-YYYY-XXXXX
        where XXXXX is a sequential number padded with zeros
        """
        current_year = timezone.now().year
        
        # Get last PO number for this year and company
        last_po = PurchaseOrder.objects.filter(
            company=self.company,
            number__startswith=f'PO-{current_year}'
        ).order_by('-number').first()
        
        if last_po:
            # Extract sequence number and increment
            last_sequence = int(last_po.number.split('-')[-1])
            new_sequence = last_sequence + 1
        else:
            new_sequence = 1
        
        return f'PO-{current_year}-{str(new_sequence).zfill(5)}'

    @transaction.atomic
    def send_to_supplier(self, order: PurchaseOrder) -> PurchaseOrder:
        """Mark PO as sent to supplier"""
        if order.status != PurchaseOrderStatus.DRAFT.name: 
            raise ValidationError("Only draft orders can be sent")
        order.status = PurchaseOrderStatus.SENT.name
        order.save()
        return order

    @transaction.atomic
    def confirm_order(self, order: PurchaseOrder, supplier_reference: str = None) -> PurchaseOrder:
        """Mark order as confirmed by supplier"""
        if order.status != PurchaseOrderStatus.SENT.name:
            raise ValidationError("Only sent orders can be confirmed")
        
        order.status = PurchaseOrderStatus.CONFIRMED.name
        if supplier_reference:
            order.your_reference = supplier_reference
        order.save()
        return order

    @transaction.atomic
    def mark_ready_for_receipt(self, order: PurchaseOrder) -> PurchaseOrder:
        """Mark order as ready for receiving"""
        if order.status != PurchaseOrderStatus.CONFIRMED.name:
            raise ValidationError("Only confirmed orders can be marked as ready")
        
        order.status = PurchaseOrderStatus.READY.name
        order.save()
        return order

    @transaction.atomic
    def cancel_order(self, order: PurchaseOrder, reason: str) -> PurchaseOrder:
        """Cancel the purchase order"""
        if order.status in [PurchaseOrderStatus.PARTIAL.name, PurchaseOrderStatus.COMPLETED.name]:
            raise ValidationError("Cannot cancel orders that have been received")
        
        order.status = PurchaseOrderStatus.CANCELLED.name
        order.notes = f"{order.notes}\nCancellation reason: {reason}" if order.notes else f"Cancellation reason: {reason}"
        order.save()
        return order
    
