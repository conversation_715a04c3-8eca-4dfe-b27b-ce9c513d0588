# API

## GENERAL

- Caching

## PRODUCTS

- Mass pricing updates
- Product import/export functionality
- Category tree operations
- Stock level calculations

## INVENTORY

- Stock reservation endpoints
- Inventory aging reports
- Movement validation rules
- Batch operations for movements

## SALES

Batch shipping operations
Advanced tracking integrations
Return label generation
Shipping cost calculations
Order analytics endpoints
Customer payment tracking

- Enhanced shipment rate calculations
- Batch processing for returns

Add specific carrier integrations?
Implement the package calculation logic?
Add more tracking functionality?
Add batch shipping capabilities?

## PURCHASING

Supplier quotation management
Advanced receipt validations
Batch order processing
Price comparison tools

## OPERATIONS

Task prioritization algorithms
Advanced route optimization
Batch picking operations
Performance metrics tracking

## CORE

User activity tracking
Advanced company statistics
Role-based access control
Company data export/import

1. adjust customer endpoints (contacts, addresses, new fields)
2. add customer category endpoints
when user creates/updates customer, they should be able to select country code in a drop down menu
