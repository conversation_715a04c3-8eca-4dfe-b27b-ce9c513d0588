from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer
from . import views

router = DefaultRouter()
router.register(
    r'companies',
    views.CompanyViewSet,
    basename='company'
)
router.register(
    r'users',
    views.UserViewSet,
    basename='user'
)
router.register(
    r'employees',
    views.EmployeeViewSet,
    basename='employee'
)
router.register(
    r'warehouses',
    views.WarehouseViewSet,
    basename='warehouse'
)
router.register(
    r'audit-logs',
    views.AuditLogViewSet,
    basename='audit-log'
)
router.register(
    r'units',
    views.UnitOfMeasureViewSet,
    basename='unit-of-measure'
)
router.register(
    r'currencies',
    views.CurrencyViewSet,
    basename='currency'
)
router.register(
    r'payment-terms',
    views.PaymentTermViewSet,
    basename='payment-term'
)
router.register(
    r'delivery-terms',
    views.DeliveryTermViewSet,
    basename='delivery-term'
)
router.register(
    r'languages',
    views.LanguageViewSet,
    basename='language'
)
router.register(
    r'number-sequences',
    views.NumberSequenceViewSet,
    basename='number-sequence'
)
router.register(
    r'delivery-methods',
    views.DeliveryMethodViewSet,
    basename='delivery-method'
)
router.register(
    r'translations',
    views.TranslationViewSet,
    basename='translation'
)
router.register(r'custom-fields', views.CustomFieldViewSet, basename='custom-field')

urlpatterns = [
    path('core/', include(router.urls)),
]