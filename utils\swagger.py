from drf_yasg.generators import OpenAPISchemaGenerator
from drf_yasg.inspectors import SwaggerAutoSchema

class CustomSwaggerAutoSchema(SwaggerAutoSchema):
    def get_tags(self, operation_keys=None):
        """
        Get tags from view's swagger_tags attribute
        """
        tags = self.overrides.get('tags', None)
        if not tags:
            tags = getattr(self.view, 'swagger_tags', [])
        if not tags and operation_keys:
            tags = [operation_keys[0]]
        return tags

class CustomSchemaGenerator(OpenAPISchemaGenerator):
    def get_schema(self, request=None, public=False):
        """
        Generate schema with properly organized tags
        """
        schema = super().get_schema(request, public)
        
        # Create a unique set of tags from all endpoints
        tags = set()
        if 'paths' in schema:
            for path in schema['paths'].values():
                for operation in path.values():
                    if 'tags' in operation:
                        for tag in operation['tags']:
                            tags.add(tag)
        
        # Add tags to schema in sorted order
        schema['tags'] = [{'name': tag} for tag in sorted(tags)]
        
        return schema