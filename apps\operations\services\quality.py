from typing import Dict, List, Optional
from datetime import datetime
from django.db import transaction
from django.db.models import Count, Sum, F, Q
from django.utils import timezone

from apps.inventory.models import Location, InventoryItem, Lot
from apps.inventory.services import MovementService
from apps.sales.models import ReturnOrder, ReturnLine, ReturnInspection
from .base import BaseOperationService
from utils.enums import InspectionResult, ReturnStatus

class QualityControlService(BaseOperationService):
    @transaction.atomic
    def create_inspection_task(
        self,
        items: List[Dict],
        reason: str,
        priority: int = 0,
        notes: str = None,
        due_date: datetime = None
    ) -> Dict:
        """
        Create quality inspection task for inventory items
        """
        # Get QC location
        qc_location = Location.objects.get(
            company=self.company,
            is_quality_control=True
        )

        # Move items to QC location
        movement_service = MovementService(self.user, self.company)
        
        inspections = []
        for item in items:
            # Create movement to QC
            movement = movement_service.create_internal_movement(
                from_location=item['location'],
                to_location=qc_location,
                lines=[{
                    'product': item['product'],
                    'variant': item.get('variant'),
                    'lot': item.get('lot'),
                    'quantity': item['quantity']
                }],
                notes=f"Quality inspection: {reason}",
                reference_number=f"QC-{item['product'].number}"
            )

            # Create inspection record
            inspection = ReturnInspection.objects.create(
                company=self.company,
                inspector=None,  # Will be assigned later
                result=InspectionResult.PENDING.name,
                notes=notes
            )
            inspections.append(inspection)

        return {
            'qc_location': qc_location,
            'inspections': inspections,
            'movements': [movement]
        }

    @transaction.atomic
    def record_inspection_result(
        self,
        inspection_id: int,
        result: str,
        notes: str = None,
        measurements: Dict = None,
        images: List[str] = None
    ) -> ReturnInspection:
        """
        Record the results of a quality inspection
        """
        inspection = ReturnInspection.objects.get(
            company=self.company,
            id=inspection_id
        )
        
        if inspection.inspector and inspection.inspector != self.user:
            raise ValueError("Cannot modify inspection by another inspector")

        inspection.inspector = self.user
        inspection.result = result
        inspection.notes = notes
        if measurements:
            inspection.measurements = measurements
        if images:
            inspection.images = images
        inspection.save()

        # If this is a return inspection, update return line
        if inspection.return_line:
            inspection.return_line.inspection_result = result
            inspection.return_line.inspection_notes = notes
            inspection.return_line.save()

            # If all lines are inspected, update return order status
            return_order = inspection.return_line.return_order
            if not return_order.lines.filter(
                inspection_result=InspectionResult.PENDING.name
            ).exists():
                return_order.status = ReturnStatus.INSPECTED.name
                return_order.save()

        return inspection

    def get_inspection_history(
        self,
        product_id: Optional[int] = None,
        lot_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> List[Dict]:
        """
        Get history of quality inspections with filtering options
        """
        query = ReturnInspection.objects.filter(
            company=self.company
        )

        if product_id:
            query = query.filter(return_line__sales_order_line__product_id=product_id)
        if lot_id:
            query = query.filter(return_line__lot_id=lot_id)
        if start_date:
            query = query.filter(inspection_date__gte=start_date)
        if end_date:
            query = query.filter(inspection_date__lte=end_date)

        return [
            {
                'inspection': inspection,
                'product': inspection.return_line.sales_order_line.product if inspection.return_line else None,
                'lot': inspection.return_line.lot if inspection.return_line else None,
                'result': inspection.result,
                'inspector': inspection.inspector,
                'date': inspection.inspection_date,
                'notes': inspection.notes,
                'measurements': inspection.measurements,
                'images': inspection.images
            }
            for inspection in query.select_related(
                'inspector',
                'return_line__sales_order_line__product',
                'return_line__lot'
            )
        ]

    @transaction.atomic
    def manage_quality_holds(
        self,
        action: str,
        items: List[Dict],
        reason: str
    ) -> List[Dict]:
        """
        Place or release quality holds on inventory items
        """
        movement_service = MovementService(self.user, self.company)
        qc_location = Location.objects.get(
            company=self.company,
            is_quality_control=True
        )

        results = []
        for item in items:
            inventory_item = InventoryItem.objects.get(
                company=self.company,
                id=item['inventory_item_id']
            )

            if action == 'HOLD':
                # Move to QC location
                movement = movement_service.create_internal_movement(
                    from_location=inventory_item.location,
                    to_location=qc_location,
                    lines=[{
                        'product': inventory_item.product,
                        'variant': inventory_item.variant,
                        'lot': inventory_item.lot,
                        'quantity': item['quantity']
                    }],
                    notes=f"Quality hold: {reason}",
                    reference_number=f"QC-{inventory_item.product.number}"
                )
                
                results.append({
                    'item': inventory_item,
                    'action': 'HOLD',
                    'quantity': item['quantity'],
                    'movement': movement
                })

            elif action == 'RELEASE':
                # Move back to storage location
                storage_location = Location.objects.filter(
                    company=self.company,
                    is_storage=True
                ).first()

                movement = movement_service.create_internal_movement(
                    from_location=qc_location,
                    to_location=storage_location,
                    lines=[{
                        'product': inventory_item.product,
                        'variant': inventory_item.variant,
                        'lot': inventory_item.lot,
                        'quantity': item['quantity']
                    }],
                    notes=f"Quality hold release: {reason}",
                    reference_number=f"QC-{inventory_item.product.number}"
                )

                results.append({
                    'item': inventory_item,
                    'action': 'RELEASE',
                    'quantity': item['quantity'],
                    'movement': movement
                })

        return results

    def get_quality_metrics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict:
        """
        Calculate quality control metrics
        """
        if not start_date:
            start_date = timezone.now() - timezone.timedelta(days=30)
        if not end_date:
            end_date = timezone.now()

        inspections = ReturnInspection.objects.filter(
            company=self.company,
            inspection_date__range=(start_date, end_date)
        )

        total_inspections = inspections.count()
        failed_inspections = inspections.filter(
            result=InspectionResult.REJECT.name
        ).count()

        returns = ReturnOrder.objects.filter(
            company=self.company,
            created_at__range=(start_date, end_date)
        )

        return {
            'inspection_stats': {
                'total_inspections': total_inspections,
                'pass_rate': ((total_inspections - failed_inspections) / total_inspections * 100) 
                            if total_inspections > 0 else 0,
                'average_inspection_time': self._calculate_avg_inspection_time(inspections),
                'inspections_by_result': self._get_inspections_by_result(inspections)
            },
            'returns_analysis': {
                'total_returns': returns.count(),
                'return_rate': self._calculate_return_rate(returns),
                'top_return_reasons': self._get_top_return_reasons(returns)
            },
            'quality_holds': {
                'current_holds': self._get_current_holds_count(),
                'average_hold_duration': self._calculate_avg_hold_duration()
            }
        }

    def _calculate_avg_inspection_time(self, inspections) -> float:
        """Calculate average time taken for inspections"""
        completed_inspections = [
            insp for insp in inspections
            if insp.result != InspectionResult.PENDING.name
        ]
        
        if not completed_inspections:
            return 0

        total_time = sum(
            (insp.updated_at - insp.created_at).total_seconds()
            for insp in completed_inspections
        )
        return total_time / len(completed_inspections) / 3600  # Convert to hours

    def _get_inspections_by_result(self, inspections) -> Dict:
        """Get breakdown of inspection results"""
        return inspections.values('result').annotate(
            count=Count('id')
        )

    def _calculate_return_rate(self, returns) -> float:
        """Calculate return rate as percentage of total orders"""
        total_orders = returns.count()  # You'd want to get this from sales orders
        if total_orders == 0:
            return 0
        return (returns.count() / total_orders) * 100

    def _get_top_return_reasons(self, returns) -> List[Dict]:
        """Get most common return reasons"""
        return ReturnLine.objects.filter(
            return_order__in=returns
        ).values('reason').annotate(
            count=Count('id')
        ).order_by('-count')[:5]

    def _get_current_holds_count(self) -> int:
        """Get count of items currently on quality hold"""
        qc_location = Location.objects.get(
            company=self.company,
            is_quality_control=True
        )
        return InventoryItem.objects.filter(
            company=self.company,
            location=qc_location,
            quantity__gt=0
        ).count()

    def _calculate_avg_hold_duration(self) -> float:
        """Calculate average duration of quality holds"""
        # This would require tracking when items are moved to/from QC
        # Implementation would depend on your movement history tracking
        raise NotImplementedError("_calculate_avg_hold_duration not implemented")
        return 0  # Placeholder