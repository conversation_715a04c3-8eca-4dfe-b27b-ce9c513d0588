# operations/services/movement.py
from decimal import Decimal
from typing import List
from django.db import transaction
from apps.inventory.models import (
    InventoryMovement, InventoryMovementLine, 
    InventoryItem, Location
)
from .base import BaseOperationService
from datetime import datetime
from django.utils import timezone 
from typing import Dict
from utils.enums import InventoryMovementType, InventoryMovementStatus



class MovementService(BaseOperationService):
    @transaction.atomic
    def create_internal_movement(
        self,
        from_location: Location,
        to_location: Location,
        lines: List[Dict],
        notes: str = None,
        scheduled_date: datetime = None,
        reference_number: str = None,
        movement_type: InventoryMovementType = None,
        **kwargs
    ) -> InventoryMovement:
        """Create an internal movement between locations"""
        # Validate locations belong to same company
        if from_location and to_location:
            if from_location.company != self.company or to_location.company != self.company:
                raise ValueError("Invalid locations for this company")

        # Create movement
        movement = InventoryMovement.objects.create(
            company=self.company,
            from_location=from_location,
            to_location=to_location,
            notes=notes,
            scheduled_date=scheduled_date,
            status=InventoryMovementStatus.DRAFT.name,
            reference_number=reference_number,
            movement_type=movement_type
        )

        # Create movement lines
        self._create_movement_lines(movement, lines)
        
        return movement

    def process_movement(self, movement: InventoryMovement) -> None:
        """Process a movement, updating inventory accordingly"""
        if movement.status != InventoryMovementStatus.PENDING.name:
            raise ValueError("Movement must be in PENDING status")

        with transaction.atomic():
            # Update source location inventory
            self._decrease_inventory(movement.from_location, movement.lines.all())
            # Update destination location inventory
            self._increase_inventory(movement.to_location, movement.lines.all())
            # Update movement status
            movement.status = InventoryMovementStatus.COMPLETED.name
            movement.completed_date = timezone.now()
            movement.save()

    def _decrease_inventory(self, location: Location, lines: List[InventoryMovementLine]) -> None:
        """Decrease inventory at source location"""
        for line in lines:
            try:
                inventory_item = InventoryItem.objects.get(
                    company=self.company,
                    location=location,
                    product=line.product,
                    variant=line.variant,
                    lot=line.lot
                )
                if inventory_item.quantity < line.quantity:
                    raise ValueError(f"Insufficient stock for product {line.product.name}")
                
                inventory_item.quantity -= line.quantity
                inventory_item.save()
            except InventoryItem.DoesNotExist:
                raise ValueError(f"Inventory item not found for product {line.product.name} and location {location.name}")

    def _increase_inventory(self, location: Location, lines: List[InventoryMovementLine]) -> None:
        """Increase inventory at destination location"""
        for line in lines:
            inventory_item, created = InventoryItem.objects.get_or_create(
                company=self.company,
                location=location,
                product=line.product,
                variant=line.variant,
                lot=line.lot,
                defaults={'quantity': 0, 'cost_price': line.cost_price}
            )
            inventory_item.quantity += line.quantity
            inventory_item.save()

    def validate_movement(self, movement: InventoryMovement) -> bool:
        """Validate if movement can be processed"""
        if movement.status != InventoryMovementStatus.DRAFT.name:
            raise ValueError("Movement must be in DRAFT status to validate")
        
        # Check if source location has enough stock
        for line in movement.lines.all():
            inventory_item = InventoryItem.objects.filter(
                company=self.company,
                location=movement.from_location,
                product=line.product,
                variant=line.variant,
                lot=line.lot
            ).first()
            
            if not inventory_item or inventory_item.quantity < line.quantity:
                raise ValueError(f"Insufficient stock for product {line.product.name} at location {movement.from_location.name}")
        
        movement.status = InventoryMovementStatus.PENDING.name
        movement.save()
        return True
    
    def await_processing(self, movement: InventoryMovement) -> None:
        """Await processing of a movement"""
        movement.status = InventoryMovementStatus.PENDING.name
        movement.save()
        return True

    def cancel_movement(self, movement: InventoryMovement) -> None:
        """Cancel a movement that hasn't been processed"""
        if movement.status in [InventoryMovementStatus.COMPLETED.name, InventoryMovementStatus.IN_PROGRESS.name]:
            raise ValueError("Cannot cancel completed or already cancelled movement")
        
        movement.status = InventoryMovementStatus.CANCELLED.name
        movement.save()

    def _create_movement_lines(
        self,
        movement: InventoryMovement,
        movement_lines: List[Dict]
    ) -> None:
        """Create movement lines for an inventory movement"""
        for line in movement_lines:
            # Convert quantity to Decimal if it isn't already
            quantity = Decimal(str(line['quantity']))
            cost_price = Decimal(str(line.get('cost_price', 0)))

            InventoryMovementLine.objects.create(
                company=self.company,
                movement=movement,
                product=line['product'],
                variant=line.get('variant'),
                lot=line.get('lot'),
                quantity=quantity,
                cost_price=cost_price
            )
            
    @transaction.atomic
    def update_internal_movement(
        self,
        movement: InventoryMovement,
        from_location: Location = None,
        to_location: Location = None,
        lines: List[Dict] = None,
        notes: str = None,
        scheduled_date: datetime = None,
        movement_type: InventoryMovementType = None,
        **kwargs
    ) -> InventoryMovement:
        """
        Update an existing internal movement.
        This only works for movements in DRAFT status.
        """
        # Verify movement is in DRAFT status
        if movement.status != InventoryMovementStatus.DRAFT.name:
            raise ValueError("Only movements in DRAFT status can be updated")
            
        # Validate locations belong to same company
        if from_location and from_location.company != self.company:
            raise ValueError("From location does not belong to this company")
        
        if to_location and to_location.company != self.company:
            raise ValueError("To location does not belong to this company")
        
        # Update movement header
        if from_location:
            movement.from_location = from_location
        
        if to_location:
            movement.to_location = to_location
            
        if notes is not None:  # Allow empty string for notes
            movement.notes = notes
            
        if scheduled_date:
            movement.scheduled_date = scheduled_date
            
        if movement_type:
            movement.movement_type = movement_type

        movement.save()
        
        # If lines are provided, delete existing lines and create new ones
        if lines:
            # Delete existing lines
            movement.lines.all().delete()
            
            # Create new lines
            self._create_movement_lines(movement, lines)
        
        return movement