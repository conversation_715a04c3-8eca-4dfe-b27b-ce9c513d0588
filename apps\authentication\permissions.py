# authentication/permissions.py
from rest_framework.permissions import BasePermission
from apps.core.models import Employee, Company

class CompanyAccessPermission(BasePermission):
    def has_permission(self, request, view):
        if not request.user:
            return False
        # Handle case where middleware hasn't run yet
        if not hasattr(request, 'company'):
            return True  # Let the middleware handle this case
            
        # If we have company attribute but it's None, deny access
        if request.company is None:
            return False

        return Employee.objects.filter(
            user=request.user,
            company=request.company,
            is_active=True
        ).exists()
    
    def has_object_permission(self, request, view, obj):
        if isinstance(obj, Company):
            if Employee.objects.filter(
                user=request.user,
                company=obj,
                is_active=True
            ).exists():
                return True
            return False
        if not hasattr(obj, 'company'):
            return False
            
        if not hasattr(request, 'company'):
            return False
        
        return obj.company == request.company

class RoleRequiredPermission(BasePermission):
    def __init__(self, required_roles):
        self.required_roles = required_roles

    def has_permission(self, request, view):
        if not request.user or not request.company:
            return False
        return Employee.objects.filter(
            user=request.user,
            company=request.company,
            role__in=self.required_roles,
            is_active=True
        ).exists()