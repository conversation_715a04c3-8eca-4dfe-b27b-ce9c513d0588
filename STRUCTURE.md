# WMS STRUCTURE

## Customer-Related Endpoints:

#### Customers: Core endpoint for managing customer records
#### Customer Addresses, Customer Contacts: Managing customer location and contact information
#### Customer Invoices, Customer Notes: Documentation and billing
#### Customer Products: Likely manages customer-specific products/pricing
#### Customer Discount Groups, Customer Discounts, Customer Tiered Discounts: Hierarchical discount system where customers can have different pricing levels
#### Customer Custom Fields: Allows for custom attributes on customer records
#### Customer Groups: Probably for categorizing customers (e.g., retail, wholesale, VIP)

## Inventory Management:

#### Internal Movements, Internal Movement Lines, Internal Movement Receipts, Internal Movement Receipt Lines: Handles stock transfers between warehouse locations
#### Inventory Adjustments: For stock corrections, damages, write-offs
#### Inventory Movements: Tracks all stock movements
#### Inventory Transactions: Likely the audit trail of all inventory changes
#### Inventory Regulation Types: Probably defines rules/reasons for inventory changes
#### Locations: Warehouse/storage locations
#### Lots: Batch/lot tracking for products
#### Items: Core product catalog
#### Serial Numbers: For tracking individual items with unique identifiers

## Order Processing:

#### Orders, Order Lines: Core sales order management
#### Order Custom Fields: Custom attributes for orders
#### Order Notes: Order-specific documentation
#### Production Orders: Manufacturing/assembly orders
#### Purchase Orders: Supplier ordering system

## Supplier Management:

#### Suppliers: Core supplier records
#### Supplier Contacts, Supplier Groups: Organization and contact management
#### Supplier Invoices, Supplier Invoice Notes: Managing incoming bills
#### Supplier Products: Products associated with specific suppliers
#### Supplier Notes: Supplier documentation

## System Configuration:

#### Currencies: Multi-currency support
#### Languages: Internationalization
#### Units: Unit of measure definitions
#### Payment Terms, Delivery Terms: Standard business terms
#### Layouts: Probably document templates
#### Custom Fields: System-wide custom field definitions
#### Batches: Likely for batch processing operations
#### Employees: User/staff management



## Details

#### Collections API:


- Appears to be a way to group items together
- Has endpoints for CRUD operations (Create, Read, Update, Delete)
- Can include number, barcode, name and description
- Supports images and can have collection lines (sub-items)
- Likely used for organizing products/items into logical groups


#### Iframes API:


- Allows creation and management of embedded interfaces
- Parameters include name, icon, URL, active status
- Has options for sidebar and sorting
- Likely used for integrating external content or tools into the Rackbeat interface


#### Internal Movement & Inventory Movement APIs:


- Internal Movements handle stock transfers between locations
- Parameters include:

    - from_location and to_location IDs
    - transport_location (possibly temporary holding)
    - reason for movement
    - expected_arrival_date for planning

- Used for tracking physical movement of stock within warehouse(s)


#### Inventory Adjustments API:


- Used for correcting stock levels
- Includes:

    - location identifier
    - quantity adjustments
    - regulation type references
    - reason documentation
    - serial number tracking


- Critical for inventory accuracy and audit trails


#### Regulation Types API:


- Defines rules/reasons for inventory changes
- Has name and type parameters
- Type "sale" shown in example
- Used to categorize and standardize inventory adjustment reasons


#### Layout API:


- Manages document/interface layouts
- Parameters include:

    - name, locale (language/region)
    - logo and positioning
    - bank account details
    - Various formatting options (position, sizing)


- Likely used for customizing documents like invoices/reports


#### Locations API:


- Manages warehouse/storage locations
- Parameters include:

name
parent_id (hierarchical structure)
    - is_barred (possibly for restricted areas)
    - is_default flag

- Fundamental for warehouse organization


#### Lots API:


- Manages product batches/lots
- Includes:

    - sales_price
    - recommended_cost_price
    - min_order/sales/stock levels
    - Various stock management parameters


- Used for batch tracking and inventory management


# LAGERLY DJANGO PROJECT STRUCTURE 

##   core
- Base app for company-wide functionality
- Will handle multi-tenancy
- Core user/employee management
- Company settings


##  inventory
- Core inventory management
- Stock tracking
- Warehouse locations
- Movement tracking


##  products
- Product catalog
- Product categories/groups
- Product variants
- Pricing


## operations
- Internal movements
- Stock adjustments
- Inventory counts
- Warehouse operations


## sales
- Orders
- Customers
- Pricing rules
- Discounts


## purchasing
- Purchase orders
- Suppliers
- Receiving
- Supplier pricing


## authentication
- Custom authentication
- Permissions
- Role management
- API tokens


##  documents
- Document templates
- PDF generation
- Export functionality
- Layouts


##  reporting
- Analytics
- Reports
- Dashboards
- Statistics