from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from rest_framework import status
from apps.authentication.permissions import CompanyAccessPermission
from rest_framework import viewsets, filters
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from apps.authentication.permissions import CompanyAccessPermission
from apps.core.services.audit import AuditLogService
from rest_framework.exceptions import PermissionDenied
from .request import CompanyAwareRequest
from django.core.exceptions import ImproperlyConfigured
from apps.core.models import Company, User, Employee

from drf_yasg.inspectors import SwaggerAutoSchema

class CompanyAwareSwaggerSchema(SwaggerAutoSchema):
    def get_view_request(self):
        request = super().get_view_request()
        if hasattr(request, '_is_swagger_request'):
            request._is_swagger_request = True
        return request

class BaseViewSet(viewsets.ModelViewSet):
    """
    Base viewset that implements company isolation and common functionality
    """
    permission_classes = [IsA<PERSON><PERSON>icated, CompanyAccessPermission]
    filter_backends = [
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter
    ]
    
    # Default to empty lists - can be overridden in child viewsets
    select_related_fields = []
    prefetch_related_fields = []


    def initialize_request(self, request, *args, **kwargs):
        """Ensure we're using our custom request class"""
        if isinstance(request, CompanyAwareRequest):
            return request
            
        request = super().initialize_request(request, *args, **kwargs)
        
        if getattr(self, 'swagger_fake_view', False):
            request._is_swagger_request = True
            
        return request
    
    def get_serializer_context(self):
        # Skip company context during schema generation
        if getattr(self, 'swagger_fake_view', False):
            return {'request': self.request, 'format': self.format_kwarg, 'view': self}
            
        context = super().get_serializer_context()
        if hasattr(self.request, 'company'):
            context['company'] = self.request.company
        return context

    def get_queryset(self):
        """
        Filter queryset by company and handle common annotations
        """
        queryset = super().get_queryset()
        
        if hasattr(self.request, 'company'):
            # Check if the model has a company field
            model = queryset.model
            if model == Company:
                pass 
            elif model == User:
                pass
            elif model == Employee:
                pass
            elif hasattr(model, 'company') or any(field.name == 'company' for field in model._meta.fields):
                queryset = queryset.filter(company=self.request.company)
            elif hasattr(model, 'user') or any(field.name == 'user' for field in model._meta.fields):
                queryset = queryset.filter(user=self.request.user)
            elif hasattr(model, 'employee') or any(field.name == 'employee' for field in model._meta.fields):
                queryset = queryset.filter(employee=self.request.employee)
            else:
                raise ImproperlyConfigured(
                    f'Model {model.__name__} does not have a company, user or employee field. '
                    'All models used with BaseViewSet must have a company, user or employee field for multi-tenancy.'
                )
            
        # Add select_related for common foreign keys
        if self.select_related_fields:
            queryset = queryset.select_related(*self.select_related_fields)
            
        # Add prefetch_related for M2M and reverse relationships
        if self.prefetch_related_fields:
            queryset = queryset.prefetch_related(*self.prefetch_related_fields)
            
        return queryset

    
    def perform_create(self, serializer):
        """
        Inject company into object creation and log audit
        """
        instance = serializer.save(company=self.request.company)
        AuditLogService.log_action(
            company=self.request.company,
            user=self.request.user,
            instance=instance,
            action='create',
            data=serializer.validated_data
        )
    
    def perform_update(self, serializer):
        """
        Update instance and log audit
        """
        instance = serializer.save()
        AuditLogService.log_action(
            company=self.request.company,
            user=self.request.user,
            instance=instance,
            action='update',
            data=serializer.validated_data
        )
    
    def perform_destroy(self, instance):
        """
        Delete instance and log audit
        """
        AuditLogService.log_action(
            company=self.request.company,
            user=self.request.user,
            instance=instance,
            action='delete',
            data=None
        )
        instance.delete()

class BaseServiceViewSet(viewsets.ViewSet):
    """
    Base viewset for service-based views that don't directly map to models
    """
    permission_classes = [IsAuthenticated, CompanyAccessPermission]
    service_class = None
    
    def get_service(self):
        """
        Get instance of the service class with current user and company
        """
        if not self.service_class:
            raise NotImplementedError(
                "service_class must be defined in the viewset"
            )
        return self.service_class(
            user=self.request.user,
            company=self.request.company
        )
    
    def handle_service_error(self, error):
        """
        Standard error handling for service operations
        """
        return Response(
            {'error': str(error)},
            status=status.HTTP_400_BAD_REQUEST
        )
