# purchasing/models.py
from django.db import models
from apps.base.models import CompanyFKRel
from apps.core.models import Warehouse
from django.contrib.contenttypes.fields import GenericRelation
from utils.enums import PurchaseOrderStatus, GoodsReceiptStatus, QualityStatus
from apps.base.models import BaseTransaction, BaseInvoice, BaseTransactionLine, RelationBase, ContactBase, AddressBase, CategoryBase




class SupplierCategory(CategoryBase):
    class Meta:
        unique_together = ['company', 'number']

class Supplier(CompanyFKRel,RelationBase):
    category = models.ForeignKey(
        SupplierCategory,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='suppliers'
    )
    extra_cost_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0, help_text='Extra cost percentage for the supplier (e.g. 10% means the actual cost price will be cost price + 10%)')
    payment_term = models.ForeignKey(
        'core.PaymentTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='supplier_payment_terms'
    )
    delivery_term = models.ForeignKey(
        'core.DeliveryTerm',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='supplier_delivery_terms'
    )
    language = models.ForeignKey(
        'core.Language',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='suppliers'
    )
    vat_zone = models.CharField(max_length=50, blank=True)
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='supplier'
    )
    

    class Meta:
        unique_together = ['company', 'number']

    def save(self, *args, **kwargs):
        if not self.pk:  # Only set defaults for new records
            settings = self.company.company_settings
            if not self.payment_term and settings.default_supplier_payment_term:
                self.payment_term = settings.default_supplier_payment_term
            if not self.delivery_term and settings.default_supplier_delivery_term:
                self.delivery_term = settings.default_supplier_delivery_term
            if not self.language and settings.default_supplier_language:
                self.language = settings.default_supplier_language
            if not self.vat_zone and settings.default_supplier_vat_zone:
                self.vat_zone = settings.default_supplier_vat_zone
            if not self.category and settings.default_supplier_category:
                self.category = settings.default_supplier_category
            if not self.currency and settings.default_currency:
                self.currency = settings.default_currency
        super().save(*args, **kwargs)

class SupplierContact(CompanyFKRel, ContactBase):
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='contacts'
    )

    def save(self, *args, **kwargs):
        if self.is_primary:
            SupplierContact.objects.filter(supplier=self.supplier, company=self.company).update(is_primary=False)
        super().save(*args, **kwargs)

class SupplierAddress(CompanyFKRel, AddressBase):
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='addresses'
    )

    def save(self, *args, **kwargs):
        if self.is_default:
            SupplierAddress.objects.filter(supplier=self.supplier, company=self.company).update(is_default=False)
        super().save(*args, **kwargs)



class SupplierProduct(CompanyFKRel):
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.CASCADE,
        related_name='products'
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.CASCADE,
        related_name='supplier_products'
    )
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='supplier_products'
    )
    supplier_sku = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=15, decimal_places=2)
    previous_price = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    min_order_quantity = models.DecimalField(  # Minimum Order Quantity
        max_digits=15,
        decimal_places=2,
        default=1
    )
    lead_time_days = models.IntegerField(default=1)
    is_preferred = models.BooleanField(default=False)

    class Meta:
        unique_together = ['company', 'supplier', 'product', 'variant']


class PurchaseOrder(CompanyFKRel, BaseTransaction):

    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='purchase_orders'
    )
    status = models.CharField(
        max_length=40,
        choices=PurchaseOrderStatus.choices(),
        default=PurchaseOrderStatus.DRAFT.name
    )
    receiving_location = models.ForeignKey(
        'inventory.Location',
        on_delete=models.PROTECT,
        related_name='purchase_orders'
    )
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='purchase_order'
    )

    class Meta:
        unique_together = ['company', 'number']


class PurchaseOrderLine(CompanyFKRel, BaseTransactionLine):
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    received_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0
    )
    expected_delivery_date = models.DateField(null=True, blank=True)


class GoodsReceipt(CompanyFKRel):
    warehouse = models.ForeignKey(
        Warehouse,
        on_delete=models.PROTECT,
        related_name='goods_receipts'
    )
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='receipts'
    )
    receipt_date = models.DateField()
    reference_number = models.CharField(max_length=50)
    notes = models.TextField(blank=True, null=True)
    receiver = models.ForeignKey(
        'core.Employee',
        on_delete=models.PROTECT,
        related_name='received_goods'
    )
    
    # New fields
    status = models.CharField(
        max_length=40,
        choices=GoodsReceiptStatus.choices(),
        default=GoodsReceiptStatus.STARTED.name
    )
    quality_control_required = models.BooleanField(default=False)
    quality_control_by = models.ForeignKey(
        'core.Employee',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='quality_controlled_receipts'
    )
    quality_control_date = models.DateTimeField(null=True, blank=True)
    quality_status = models.CharField(
        max_length=40,
        choices=QualityStatus.choices(),
        null=True,
        blank=True
    )

    @property
    def quality_issues(self):
        return self.quality_status == QualityStatus.DAMAGED.name

    class Meta:
        unique_together = ['company', 'reference_number']


class GoodsReceiptLine(CompanyFKRel):
    receipt = models.ForeignKey(
        GoodsReceipt,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    purchase_order_line = models.ForeignKey(
        PurchaseOrderLine,
        on_delete=models.PROTECT,
        related_name='receipt_lines'
    )
    quantity = models.DecimalField(max_digits=15, decimal_places=2)
    lot_number = models.CharField(max_length=100, blank=True)
    expiry_date = models.DateField(null=True, blank=True)

class SupplierInvoice(BaseInvoice, CompanyFKRel):
    supplier = models.ForeignKey(
        Supplier,
        on_delete=models.PROTECT,
        related_name='invoices'
    )
    purchase_order = models.ForeignKey(
        PurchaseOrder,
        on_delete=models.PROTECT,
        related_name='invoices'
    )
    invoice_number = models.CharField(max_length=50)  # Supplier's invoice number
    is_credit_note = models.BooleanField(default=False)
    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='supplier_invoice'
    )
    
    class Meta:
        unique_together = [
            ['company', 'number'],  # Our internal number
            ['company', 'supplier', 'invoice_number']  # Supplier's number must be unique per supplier
        ]

class SupplierInvoiceLine(BaseTransactionLine, CompanyFKRel):
    invoice = models.ForeignKey(
        SupplierInvoice,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    purchase_order_line = models.ForeignKey(
        PurchaseOrderLine,
        on_delete=models.PROTECT,
        related_name='invoice_lines'
    )
    goods_receipt_line = models.ForeignKey(
        'purchasing.GoodsReceiptLine',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='invoice_lines'
    )