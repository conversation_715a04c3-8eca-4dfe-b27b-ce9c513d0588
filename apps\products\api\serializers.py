from rest_framework import serializers
from django.db.models import Sum
from utils.serializers import BaseModelSerializer
from apps.products.models import (
    Product, ProductCategory, ProductVariant,
    ProductPricing, ProductBundle, BundleComponent,
    BundlePricing, ProductType, PriceHistoryEntry
)
from apps.core.api.serializers import LanguageSerializer
from apps.core.models import Translation
from utils.constants import EU_COUNTRIES_CODE_LIST
from decimal import Decimal
from apps.core.models import Currency, Employee, CustomFieldValue, Language
from apps.core.api.serializers import CurrencySerializer, EmployeeSerializer, CustomFieldValueSerializer

class ProductTypeSerializer(BaseModelSerializer):
    product_count = serializers.SerializerMethodField()

    def get_product_count(self, obj):
        return obj.products.count()
    class Meta:
        model = ProductType
        fields = [
            'id', 'uuid', 'name', 'number', 'description', 'domestic_rate', 
            'domestic_exception_rate', 'europe_rate', 'export_rate', 
            'country_specific_rates', 'product_count'
        ]

    def validate_number(self, value):
        if self.instance is None:
            if ProductType.objects.filter(company=self.context['request'].company, number=value).exists():
                raise serializers.ValidationError("Product type number must be unique.")
        else:
            if ProductType.objects.filter(company=self.context['request'].company, number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product type number must be unique.")
        return value
    
    def validate_europe_country_specific_rates(self, value):
        if not isinstance(value, dict):
            raise serializers.ValidationError("Must be a dictionary of country codes and VAT rates.")

        validated_data = {}
        for country_code, rate in value.items():
            # Validate country code
            if country_code not in EU_COUNTRIES_CODE_LIST:
                raise serializers.ValidationError(
                    f"Invalid country code: {country_code}. Must be one of {EU_COUNTRIES_CODE_LIST}"
                )

            # Convert rate to Decimal for precise comparison
            try:
                rate_decimal = Decimal(str(rate))
            except (TypeError, ValueError):
                raise serializers.ValidationError(
                    f"Invalid rate value for {country_code}: {rate}. Must be a number."
                )

            # Validate rate range
            if rate_decimal < 1 or rate_decimal > 100:
                raise serializers.ValidationError(
                    f"Invalid rate for {country_code}: {rate}. Must be between 1 and 100."
                )

            validated_data[country_code] = float(rate_decimal)

        return validated_data

class ProductCategorySerializer(BaseModelSerializer):
    children = serializers.SerializerMethodField()
    parent_name = serializers.CharField(source='parent.name', read_only=True)
    type_name = serializers.CharField(source='type.name', read_only=True)
    product_count = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = ProductCategory
        fields = [
            'id', 'uuid', 'name', 'number', 'description', 
            'type', 'type_name', 'parent', 'parent_name',
            'children', 'can_be_sold', 'product_count'
        ]
        read_only_fields = ['id']

    def get_children(self, obj):
        if hasattr(obj, 'children'):
            return ProductCategorySerializer(
                obj.children.all(),
                many=True,
                context=self.context
            ).data
        return []
    
    def validate_number(self, value):
        if ProductCategory.objects.filter(company=self.context['request'].company, number=value).exists() or (self.instance is not None and self.instance.number != value):
            raise serializers.ValidationError("Product category number must be unique.")
        return value

class ProductPricingSerializer(BaseModelSerializer):
    currency = serializers.PrimaryKeyRelatedField(queryset=Currency.objects.all())
    currency_code = serializers.CharField(source='currency.currency', read_only=True)

    representation_fields = {
        'currency': CurrencySerializer
    }

    class Meta:
        model = ProductPricing
        exclude = ['company', 'product', 'variant']
        read_only_fields = ['id']

class ProductVariantSerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    full_sku = serializers.SerializerMethodField()
    prices = ProductPricingSerializer(many=True, read_only=True)
    total_stock = serializers.DecimalField(
        max_digits=15, decimal_places=2, read_only=True
    )

    class Meta:
        model = ProductVariant
        fields = [
            'id', 'uuid', 'product', 'product_name', 'name',
            'sku_suffix', 'full_sku', 'barcode',
            'is_active', 'prices', 'total_stock',
            'weight', 'weight_unit', 'width',
            'height', 'depth', 'size_unit', 'number'
        ]
        read_only_fields = ['id', 'full_sku', 'total_stock']

    def get_full_sku(self, obj):
        return f"{obj.product.sku}-{obj.sku_suffix}"
    
    def validate_sku_suffix(self, value):
        if self.instance is None:
            # New instance - check if SKU suffix exists
            if ProductVariant.objects.filter(sku_suffix=value).exists():
                raise serializers.ValidationError("Product variant SKU suffix must be unique.")
        else:
            # Existing instance - check if SKU suffix exists for other variants
            if ProductVariant.objects.filter(sku_suffix=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product variant SKU suffix must be unique.")
        return value
    
    def validate_barcode(self, value):
        if self.instance is None:
            # New instance - check if barcode exists
            if ProductVariant.objects.filter(barcode=value).exists():
                raise serializers.ValidationError("Product variant barcode must be unique.")
        else:
            # Existing instance - check if barcode exists for other variants
            if ProductVariant.objects.filter(barcode=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product variant barcode must be unique.")
        return value
    
    def validate_number(self, value):
        if self.instance is None:
            # New instance - check if number exists
            if ProductVariant.objects.filter(number=value).exists():
                raise serializers.ValidationError("Product variant number must be unique.")
        else:
            # Existing instance - check if number exists for other variants
            if ProductVariant.objects.filter(number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product variant number must be unique.")
        return value

class ProductSerializer(BaseModelSerializer):
    type = serializers.PrimaryKeyRelatedField(queryset=ProductType.objects.all())
    category_name = serializers.CharField(source='category.name', read_only=True)
    type_name = serializers.CharField(source='type.name', read_only=True)
    unit_of_measure_abbr = serializers.CharField(
        source='unit_of_measure.abbreviation',
        read_only=True
    )
    variants = ProductVariantSerializer(many=True, read_only=True)
    prices = ProductPricingSerializer(many=True, read_only=True)
    total_variants = serializers.IntegerField(read_only=True)
    total_stock = serializers.DecimalField(
        max_digits=15, decimal_places=2, read_only=True
    )
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )

    representation_fields = {
        'type': ProductTypeSerializer,
        'category': ProductCategorySerializer
    }

    class Meta:
        model = Product
        fields = [
            'id', 'uuid', 'name', 'number', 'sku', 'barcode',
            'description', 'type', 'type_name',
            'category', 'category_name',
            'unit_of_measure', 'unit_of_measure_abbr',
            'is_active', 'variants', 'total_variants',
            'prices', 'total_stock', 'image', 'pdf',
            'min_stock_level', 'max_stock_level',
            'lot_tracking_required',
            'weight', 'weight_unit', 'width', 'height',
            'depth', 'size_unit', 'min_order_quantity',
            'min_purchase_quantity', 'min_inventory_level',
            'custom_fields'
        ]
        read_only_fields = ['id', 'total_stock', 'total_variants']

    def validate_number(self, value):
        if self.instance is None:
            # New instance - check if number exists
            if Product.objects.filter(company=self.context['request'].company, number=value).exists():
                raise serializers.ValidationError("Product number must be unique.")
        else:
            # Existing instance - check if number exists for other products
            if Product.objects.filter(company=self.context['request'].company, number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product number must be unique.")
        return value
    
    def validate_sku(self, value):
        if self.instance is None:
            # New instance - check if SKU exists
            if Product.objects.filter(company=self.context['request'].company, sku=value).exists():
                raise serializers.ValidationError("Product SKU must be unique.")
        else:
            # Existing instance - check if SKU exists for other products
            if Product.objects.filter(company=self.context['request'].company, sku=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product SKU must be unique.")
        return value

    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        product = super().create(validated_data)
        
        # Create custom field values
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=product.company,
                content_object=product,
                **field_data
            )
        
        return product

    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        
        # Update the product instance with the remaining data
        instance = super().update(instance, validated_data)
        
        # Handle custom field values
        for field_data in custom_fields_data:
            CustomFieldValue.objects.update_or_create(
                company=instance.company,
                content_object=instance,
                field=field_data.get('field'),  # Assuming you have a field identifier
                defaults={'value': field_data.get('value')}
            )
        
        return instance

class BundleComponentSerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)
    
    class Meta:
        model = BundleComponent
        fields = [
            'id', 'uuid', 'bundle', 'product', 'product_name', 'variant',
            'variant_name', 'quantity', 'is_optional'
        ]
        read_only_fields = ['id']

class BundlePricingSerializer(BaseModelSerializer):
    currency_code = serializers.CharField(source='currency.currency', read_only=True)
    
    class Meta:
        model = BundlePricing
        fields = [
            'id', 'uuid', 'currency', 'currency_code',
            'base_selling_price', 'cost_price',
            'is_active', 'bundle'
        ]
        read_only_fields = ['id']

class ProductBundleSerializer(BaseModelSerializer):
    components = BundleComponentSerializer(many=True, read_only=True)
    prices = BundlePricingSerializer(many=True, read_only=True)
    total_components = serializers.IntegerField(read_only=True)
    
    class Meta:
        model = ProductBundle
        fields = [
            'id', 'uuid', 'name', 'number', 'description', 'sku',
            'barcode', 'is_active', 'price_override',
            'discount_percentage', 'image', 'components',
            'prices', 'total_components'
        ]
        read_only_fields = ['id']

    def validate_number(self, value):
        if self.instance is None:
            # New instance - check if number exists
            if ProductBundle.objects.filter(company=self.context['request'].company, number=value).exists():
                raise serializers.ValidationError("Product bundle number must be unique.")
        else:
            # Existing instance - check if number exists for other bundles
            if ProductBundle.objects.filter(company=self.context['request'].company, number=value).exclude(id=self.instance.id).exists():
                raise serializers.ValidationError("Product bundle number must be unique.")
        return value
    
class RemoveOrAddBundleComponentSerializer(serializers.Serializer):
    component_id = serializers.PrimaryKeyRelatedField(queryset=BundleComponent.objects.all())

class PriceHistoryEntrySerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)
    currency_code = serializers.CharField(source='currency.currency', read_only=True)
    changed_by_name = serializers.CharField(source='changed_by.full_name', read_only=True)
    changed_by = serializers.PrimaryKeyRelatedField(read_only=True)

    representation_fields = {
        'changed_by': EmployeeSerializer
    }
    
    class Meta:
        model = PriceHistoryEntry
        fields = [
            'id', 'uuid', 'product', 'product_name', 'variant', 'variant_name',
            'currency', 'currency_code', 'base_purchasing_price',
            'actual_purchasing_price', 'base_selling_price',
            'recommended_selling_price', 'changed_at', 'changed_by',
            'changed_by_name', 'change_reason', 'change_type'
        ]
        read_only_fields = ['id', 'changed_at', 'changed_by']

class ProductPriceUpdateSerializer(BaseModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)  # For existing components
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all(), required=False)
    variant = serializers.PrimaryKeyRelatedField(queryset=ProductVariant.objects.all(), required=False)

    representation_fields = {
        'product': ProductSerializer,
        'variant': ProductVariantSerializer,
    }
    
    class Meta:
        model = ProductPricing
        exclude = ['company']

class ProductBundlePriceUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False, allow_null=True)  # For existing components
    currency = serializers.PrimaryKeyRelatedField(queryset=Currency.objects.all())
    base_selling_price = serializers.DecimalField(max_digits=15, decimal_places=2)
    cost_price = serializers.DecimalField(max_digits=15, decimal_places=2, required=False, allow_null=True)
    is_active = serializers.BooleanField(default=True, required=False)

class BundlePriceUpdateListSerializer(serializers.Serializer):
    prices = ProductBundlePriceUpdateSerializer(many=True)

class ProductPriceUpdateListSerializer(serializers.Serializer):
    prices = ProductPriceUpdateSerializer(many=True)

class ProductTranslationSerializer(BaseModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)  # For existing translations
    language = serializers.PrimaryKeyRelatedField(queryset=Language.objects.all())

    representation_fields = {
        'language': LanguageSerializer
    }

    class Meta:
        model = Translation
        fields = ['id', 'uuid', 'language', 'name', 'description', 'abbreviation']

class ProductTranslationListSerializer(serializers.Serializer):
    translations = ProductTranslationSerializer(many=True)

class BundleComponentUpdateSerializer(serializers.Serializer):
    id = serializers.IntegerField(required=False, allow_null=True)  # For existing components
    product = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(), 
        required=False, 
        allow_null=True
    )
    variant = serializers.PrimaryKeyRelatedField(
        queryset=ProductVariant.objects.all(), 
        required=False, 
        allow_null=True
    )
    quantity = serializers.DecimalField(max_digits=10, decimal_places=2)
    is_optional = serializers.BooleanField(default=False)

    def validate(self, data):
        """
        Check that only product or variant is provided, not both or neither.
        """
        if ('product' in data and data['product'] is not None and 
            'variant' in data and data['variant'] is not None):
            raise serializers.ValidationError("Only provide product OR variant, not both")
        
        if (('product' not in data or data['product'] is None) and 
            ('variant' not in data or data['variant'] is None)):
            raise serializers.ValidationError("Either product or variant must be provided")
        
        return data

class BundleComponentUpdateListSerializer(serializers.Serializer):
    components = BundleComponentUpdateSerializer(many=True)