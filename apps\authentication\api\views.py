# authentication/views.py
from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.views import TokenObtainPairView
from django.db import transaction
from .serializers import CustomTokenObtainPairSerializer, CompanySwitchSerializer
from apps.core.models import ActiveCompanySession
from apps.core.models import Company, User, Employee, ActiveCompanySession
from django.core.cache import cache
from django.conf import settings
from apps.core.api.serializers import UserMeSerializer

class CustomTokenObtainPairView(TokenObtainPairView):
    swagger_tags = ['Authentication'] 
    serializer_class = CustomTokenObtainPairSerializer

    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        if response.status_code == status.HTTP_200_OK:
            user = User.objects.get(email=request.data['email'])
            
            # Check if user has any active company session
            active_session = ActiveCompanySession.objects.filter(
                user=user,
                is_active=True
            ).first()
            if not active_session:
                # Get first company where user is an active employee
                first_company = Company.objects.filter(
                    company_employee__user=user,  # Using the default related name from the Employee model
                    company_employee__is_active=True  # Note: employee (singular) not employees
                ).first()
                if first_company:
                    # Create active session for first company
                    active_session = ActiveCompanySession.objects.create(
                        user=user,
                        company=first_company,
                        is_active=True
                    )
                    response.data['company_id'] = first_company.id
                    request.company = first_company
            else:
                response.data['company_id'] = active_session.company.id
                request.company = active_session.company
                
        return response

class SwitchCompanyView(APIView):
    swagger_tags = ['Authentication'] 
    permission_classes = [IsAuthenticated]
    serializer_class = CompanySwitchSerializer
    
    def _get_rate_limit_key(self, user):
        return f"company_switch_rate_limit_{user.id}"

    @transaction.atomic
    def post(self, request):
        # Rate limiting
        rate_limit_key = self._get_rate_limit_key(request.user)
        if cache.get(rate_limit_key):
            return Response(
                {"error": "Please wait before switching companies again"},
                status=status.HTTP_429_TOO_MANY_REQUESTS
            )

        serializer = self.serializer_class(
            data=request.data,
            context={'request': request}
        )
        serializer.is_valid(raise_exception=True)
        company_id = serializer.validated_data['company_id']

        # Verify user is an active employee of the target company
        if not Employee.objects.filter(
            user=request.user,
            company_id=company_id,
            is_active=True
        ).exists():
            return Response(
                {"error": "You don't have access to this company"},
                status=status.HTTP_403_FORBIDDEN
            )
        
        company = Company.objects.get(id=company_id)

        # Deactivate current active company session
        ActiveCompanySession.objects.filter(
            user=request.user,
            is_active=True
        ).update(is_active=False)

        # Create new active company session
        ActiveCompanySession.objects.create(
            user=request.user,
            company_id=company_id,
            is_active=True
        )

        # Set rate limit
        cache.set(rate_limit_key, True, settings.COMPANY_SWITCH_RATE_LIMIT_SECONDS)

        return Response({
            'message': 'Company switched successfully',
            'company_uuid': company.uuid,
            'company_id': company.id
        })

class UserMeView(APIView):
    """
    API endpoint that provides detailed information about the currently logged-in user.
    """
    swagger_tags = ['Authentication']
    permission_classes = [IsAuthenticated]

    def get(self, request):
        """
        Get detailed information about the currently logged-in user.
        Includes profile information, associated companies, roles, and active session.
        """
        serializer = UserMeSerializer(request.user)
        return Response(serializer.data)