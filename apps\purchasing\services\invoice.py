from datetime import date, timedelta, datetime
from decimal import Decimal
from typing import Optional, List
from django.db import transaction
from django.db.models import Sum
from ..models import PurchaseOrder, SupplierInvoice, SupplierInvoiceLine
from .base import BasePurchasingService
from utils.enums import PurchaseOrderStatus, TermType, InvoiceStatus
from django.utils import timezone


class SupplierInvoiceService(BasePurchasingService):
    
    @transaction.atomic
    def create_invoice_from_order(
        self,
        po_id: int,
        invoice_number: str,
        invoice_date: date,
        use_received_quantities: bool = True
    ) -> SupplierInvoice:
        """
        Create a supplier invoice from a purchase order.
        
        Args:
            po_id: Purchase order ID
            invoice_number: Supplier's invoice number
            invoice_date: Date of the invoice
            use_received_quantities: If True, create invoice based on received quantities,
                                   if False, use ordered quantities
        """
        po = PurchaseOrder.objects.get(
            company=self.company,
            id=po_id
        )
        
        # Validate order status
        if po.status == PurchaseOrderStatus.CANCELLED.name:
            raise ValueError("Cannot create invoice for cancelled order")
        
        if use_received_quantities and not po.receipts.exists():
            raise ValueError("No goods receipts found for this order")
            
        # Calculate due date based on payment terms
        if po.payment_term:
            if po.payment_term.type == TermType.NET.name:
                # Convert to date if we got a string
                if isinstance(invoice_date, str):
                    invoice_date = datetime.strptime(invoice_date, '%Y-%m-%d').date()
                due_date = invoice_date + timedelta(days=po.payment_term.days)
            else:
                due_date = invoice_date
        else:
            due_date = invoice_date
        
        # Create invoice
        invoice = SupplierInvoice.objects.create(
            company=self.company,
            supplier=po.supplier,
            purchase_order=po,
            number=self._generate_invoice_number(),
            invoice_number=invoice_number,
            invoice_date=invoice_date,
            due_date=due_date,
            currency=po.currency,
            currency_rate=po.currency_rate,
            payment_term=po.payment_term,
            delivery_term=po.delivery_term,
            delivery_method=po.delivery_method,
            our_reference=po.our_reference,
            your_reference=po.your_reference,
            notes=po.notes,
            order_date=po.order_date
        )
        
        # Create invoice lines
        total_net = Decimal('0')
        total_tax = Decimal('0')
        
        for po_line in po.lines.all():
            quantity = (
                po_line.received_quantity if use_received_quantities 
                else po_line.quantity
            )
            
            if quantity > 0:
                net_amount = quantity * po_line.unit_price
                tax_amount = net_amount * (po_line.tax_rate / 100)
                
                SupplierInvoiceLine.objects.create(
                    company=self.company,
                    invoice=invoice,
                    purchase_order_line=po_line,
                    product=po_line.product,
                    variant=po_line.variant,
                    description=po_line.product.name,
                    quantity=quantity,
                    unit_price=po_line.unit_price,
                    tax_rate=po_line.tax_rate,
                    net_amount=net_amount,
                    tax_amount=tax_amount,
                    total_amount=net_amount + tax_amount
                )
                
                total_net += net_amount
                total_tax += tax_amount
        
        # Update invoice totals
        invoice.total_net_amount = total_net
        invoice.total_tax_amount = total_tax
        invoice.total_amount = total_net + total_tax
        invoice.save()
        
        return invoice

    def _generate_invoice_number(self) -> str:
        """Generate next invoice number sequence"""
        prefix = 'SINV'
        current_year = timezone.now().year
        
        last_invoice = SupplierInvoice.objects.filter(
            company=self.company,
            number__startswith=f'{prefix}-{current_year}'
        ).order_by('-number').first()
        
        if last_invoice:
            last_sequence = int(last_invoice.number.split('-')[-1])
            new_sequence = last_sequence + 1
        else:
            new_sequence = 1
        
        return f'{prefix}-{current_year}-{str(new_sequence).zfill(5)}'

    @transaction.atomic
    def send_invoice(self, invoice: SupplierInvoice) -> SupplierInvoice:
        """Mark invoice as sent"""
        
        if invoice.status != InvoiceStatus.DRAFT.name:
            raise ValueError("Only draft invoices can be sent")
        
        invoice.status = InvoiceStatus.SENT.name
        invoice.save()
        return invoice

    @transaction.atomic
    def mark_as_paid(
        self,
        invoice: SupplierInvoice,
        payment_date: date = None,
        payment_reference: str = None,
        partial: bool = False
    ) -> SupplierInvoice:
        """Mark invoice as paid or partially paid"""
        
        if invoice.status == InvoiceStatus.CANCELLED.name:
            raise ValueError("Cannot pay cancelled invoices")
        
        if invoice.status == InvoiceStatus.PAID.name:
            raise ValueError("Invoice is already paid")
        
        invoice.status = InvoiceStatus.PARTIAL_PAID.name if partial else InvoiceStatus.PAID.name
        invoice.final_paid_date = payment_date or timezone.now().date()
        invoice.payment_reference = payment_reference
        invoice.save()
        return invoice

    @transaction.atomic
    def cancel_invoice(self, invoice: SupplierInvoice, reason: str = None) -> SupplierInvoice:
        """Cancel an invoice"""
        
        if invoice.status in [InvoiceStatus.PAID.name, InvoiceStatus.PARTIAL_PAID.name]:
            raise ValueError("Cannot cancel paid invoices")
        
        invoice.status = InvoiceStatus.CANCELLED.name
        if reason:
            invoice.notes = f"{invoice.notes}\nCancellation reason: {reason}" if invoice.notes else f"Cancellation reason: {reason}"
        invoice.save()
        return invoice 