# core/serializers.py
from rest_framework import serializers
from django.contrib.auth.password_validation import validate_password
from utils.serializers import BaseModelSerializer
from apps.core.models import (
    Company, User, Employee, Warehouse,
    CompanySetting, AuditLog, ActiveCompanySession,
    UnitOfMeasure, Currency, Language, Translation,
    PaymentTerm, DeliveryTerm, NumberSequence,
    CustomField, CustomFieldValue, DeliveryMethod
)
from apps.purchasing.models import PurchaseOrder, SupplierInvoice
from apps.sales.models import SalesOrder, SalesInvoice
from apps.inventory.api.serializers import LocationSerializer
from utils.enums import NumberSequenceType, VATZone
import re
from datetime import datetime
from apps.products.models import ProductType, ProductCategory
from apps.purchasing.models import Supplier
from apps.sales.models import CustomerCategory


class CompanySettingSerializer(BaseModelSerializer):
    default_product_type = serializers.PrimaryKeyRelatedField(
        queryset=ProductType.objects.all(),
        required=False,
        allow_null=True
    )   
    default_product_supplier = serializers.PrimaryKeyRelatedField(
        queryset=Supplier.objects.all(),
        required=False,
        allow_null=True
    )
    default_product_category = serializers.PrimaryKeyRelatedField(
        queryset=ProductCategory.objects.all(),
        required=False,
        allow_null=True
    )
    default_unit_of_measure = serializers.PrimaryKeyRelatedField(
        queryset=UnitOfMeasure.objects.all(),
        required=False,
        allow_null=True
    )
    default_customer_payment_term = serializers.PrimaryKeyRelatedField(
        queryset=PaymentTerm.objects.all(),
        required=False,
        allow_null=True
    )
    default_customer_delivery_term = serializers.PrimaryKeyRelatedField(
        queryset=DeliveryTerm.objects.all(),
        required=False,
        allow_null=True
    )
    default_customer_language = serializers.PrimaryKeyRelatedField(
        queryset=Language.objects.all(),
        required=False,
        allow_null=True
    )
    default_customer_category = serializers.PrimaryKeyRelatedField(
        queryset=CustomerCategory.objects.all(),
        required=False,
        allow_null=True
    )
    default_supplier_payment_term = serializers.PrimaryKeyRelatedField(
        queryset=PaymentTerm.objects.all(),
        required=False,
        allow_null=True
    )
    default_supplier_delivery_term = serializers.PrimaryKeyRelatedField(
        queryset=DeliveryTerm.objects.all(),
        required=False,
        allow_null=True
    )

    class Meta:
        model = CompanySetting
        fields = [
            'id', 'default_currency', 'default_warehouse',
            'inventory_valuation_method', 'timezone',
            # Product defaults
            'default_product_type', 'default_product_supplier',
            'default_product_category', 'default_unit_of_measure',
            # Customer defaults
            'default_customer_payment_term', 'default_customer_delivery_term',
            'default_customer_language', 'default_customer_vat_zone',
            'default_customer_category',
            # Supplier defaults
            'default_supplier_payment_term', 'default_supplier_delivery_term',
            'default_supplier_language', 'default_supplier_vat_zone',
            'default_supplier_category'
        ]
        read_only_fields = ['id']


class NumberSequenceSerializer(serializers.ModelSerializer):
    class Meta:
        model = NumberSequence
        fields = ['id', 'sequence_type', 'next_number']
        read_only_fields = ['company']

    def validate(self, data):
        company = self.context['request'].company
        instance = self.instance
        next_number = data.get('next_number')
        sequence_type = data.get('sequence_type')

        # If this is an update, check the sequence type hasn't changed
        if instance and sequence_type != instance.sequence_type:
            raise serializers.ValidationError(
                "Sequence type cannot be changed after creation"
            )

        if next_number:
            # Check for existing records with higher or equal numbers
            existing_number = None
            if sequence_type == NumberSequenceType.PURCHASE_ORDER.name:
                existing_number = PurchaseOrder.objects.filter(
                    company=company,
                    number__gte=next_number
                ).order_by('-number').first()
            elif sequence_type == NumberSequenceType.SUPPLIER_INVOICE.name:
                existing_number = SupplierInvoice.objects.filter(
                    company=company,
                    number__gte=next_number
                ).order_by('-number').first()
            elif sequence_type == NumberSequenceType.SALES_ORDER.name:
                existing_number = SalesOrder.objects.filter(
                    company=company,
                    number__gte=next_number
                ).order_by('-number').first()
            elif sequence_type == NumberSequenceType.SALES_INVOICE.name:
                existing_number = SalesInvoice.objects.filter(
                    company=company,
                    number__gte=next_number
                ).order_by('-number').first()

            if existing_number:
                raise serializers.ValidationError(
                    f"Next number cannot be lower than existing numbers. "
                    f"Found existing record with number: {existing_number.number}"
                )

        return data


class CompanySerializer(BaseModelSerializer):
    settings = CompanySettingSerializer(
        source='company_settings',
        required=False
    )
    number_sequences = NumberSequenceSerializer(
        many=True,
        required=False
    )
    employee_count = serializers.IntegerField(read_only=True)

    class Meta:
        model = Company
        fields = [
            'id', 'uuid', 'name', 'registration_number', 'vat_number',
            'address', 'phone', 'email', 'website', 'is_active',
            'created_at', 'updated_at', 'settings', 'employee_count',
            'number_sequences'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at'
        ]

    def update(self, instance, validated_data):
        # Handle settings update
        settings_data = validated_data.pop('company_settings', None)
        if settings_data:
            settings = instance.company_settings
            for key, value in settings_data.items():
                setattr(settings, key, value)
            settings.save()

        # Handle number sequences update
        number_sequences_data = validated_data.pop('number_sequences', None)
        if number_sequences_data:
            for sequence_data in number_sequences_data:
                sequence_type = sequence_data.get('sequence_type')
                sequence, created = NumberSequence.objects.get_or_create(
                    company=instance,
                    sequence_type=sequence_type,
                    defaults={'next_number': sequence_data.get('next_number')}
                )
                if not created:
                    sequence.next_number = sequence_data.get('next_number')
                    sequence.save()

        return super().update(instance, validated_data)


class EmployeeSerializer(BaseModelSerializer):
    user_email = serializers.EmailField(source='user.email', read_only=True)
    user_name = serializers.CharField(
        source='user.full_name',
        read_only=True
    )

    class Meta:
        model = Employee
        fields = [
            'id', 'uuid', 'user', 'user_email', 'user_name', 'company',
            'role', 'is_active', 'joined_at', 'left_at'
        ]
        read_only_fields = ['id', 'joined_at']

class UserSerializer(BaseModelSerializer):
    companies = CompanySerializer(many=True, read_only=True)
    roles = serializers.SerializerMethodField()
    password = serializers.CharField(
        write_only=True,
        required=False,
        style={'input_type': 'password'}
    )

    class Meta:
        model = User
        fields = [
            'id', 'uuid', 'email', 'username', 'first_name', 'last_name',
            'phone', 'is_employee', 'companies', 'roles',
            'is_active', 'is_verified', 'password'
        ]
        read_only_fields = [
            'id', 'uuid', 'is_active', 'is_verified'
        ]
    
    def get_roles(self, obj):
        return list(Employee.objects.filter(user=obj).values_list('role', flat=True))

    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = super().create(validated_data)
        user.set_password(password)
        user.save()
        return user

class UserMeSerializer(UserSerializer):
    """
    Detailed serializer for the currently logged-in user.
    Includes additional information about companies, roles, and active session.
    """
    active_company = serializers.SerializerMethodField()
    employee_details = serializers.SerializerMethodField()
    permissions = serializers.SerializerMethodField()

    class Meta(UserSerializer.Meta):
        fields = UserSerializer.Meta.fields + [
            'active_company', 'employee_details', 'permissions'
        ]
    
    def get_active_company(self, obj):
        active_session = ActiveCompanySession.objects.filter(user=obj, is_active=True).first()
        if active_session:
            return {
                'id': active_session.company.id,
                'uuid': active_session.company.uuid,
                'name': active_session.company.name
            }
        return None
    
    def get_employee_details(self, obj):
        result = []
        for employee in Employee.objects.filter(user=obj).select_related('company', 'primary_warehouse'):
            result.append({
                'id': employee.id,
                'uuid': employee.uuid,
                'company_id': employee.company.id,
                'company_name': employee.company.name,
                'role': employee.role,
                'primary_warehouse': employee.primary_warehouse.name if employee.primary_warehouse else None,
                'is_active': employee.is_active,
                'joined_at': employee.joined_at
            })
        return result
    
    def get_permissions(self, obj):
        # Return user permissions based on their role
        # This can be extended based on your permission structure
        permissions = []
        for employee in Employee.objects.filter(user=obj):
            if employee.role == 'ADMIN':
                permissions.append('ADMIN')
            elif employee.role == 'MANAGER':
                permissions.append('MANAGE_ORDERS')
                permissions.append('MANAGE_INVENTORY')
            # Add more role-based permissions as needed
        return list(set(permissions))  # Return unique permissions

class WarehouseSerializer(BaseModelSerializer):
    location_count = serializers.IntegerField(read_only=True)

    representation_fields = {
        'locations': LocationSerializer
    }

    class Meta:
        model = Warehouse
        fields = [
            'id', 'uuid', 'name', 'number', 'address', 'phone',
            'email', 'is_active', 'created_at', 'updated_at',
            'location_count'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

class AuditLogSerializer(BaseModelSerializer):
    user_email = serializers.CharField(
        source='user.email',
        read_only=True,
        allow_null=True
    )
    content_type_name = serializers.CharField(
        source='content_type.model',
        read_only=True
    )

    class Meta:
        model = AuditLog
        fields = [
            'id', 'uuid', 'user', 'user_email', 'content_type',
            'content_type_name', 'object_id', 'action',
            'data', 'created_at'
        ]
        read_only_fields = [
            'id', 'user', 'content_type', 'created_at'
        ]

class LanguageSerializer(BaseModelSerializer):
    class Meta:
        model = Language
        fields = ['id', 'uuid', 'language', 'is_active']
        read_only_fields = ['id']
        
class TranslationSerializer(BaseModelSerializer):
    language_code = serializers.CharField(source='language.language', read_only=True)


    representation_fields = {
        'language': LanguageSerializer
    }

    class Meta:
        model = Translation
        fields = ['id', 'uuid', 'language', 'language_code', 'name', 'description', 'abbreviation']
        read_only_fields = ['id']


class UnitOfMeasureSerializer(BaseModelSerializer):

    class Meta:
        model = UnitOfMeasure
        fields = ['id', 'uuid', 'name', 'abbreviation', 'is_active']
        read_only_fields = ['id']

class CurrencySerializer(BaseModelSerializer):
    class Meta:
        model = Currency
        fields = ['id', 'uuid', 'currency', 'is_active']
        read_only_fields = ['id']




class PaymentTermSerializer(BaseModelSerializer):

    class Meta:
        model = PaymentTerm
        fields = ['id', 'uuid', 'name', 'type', 'days', 'description']
        read_only_fields = ['id']


class DeliveryTermSerializer(BaseModelSerializer):

    class Meta:
        model = DeliveryTerm
        fields = ['id', 'uuid', 'name', 'description', 'type', 'is_active', 'days']
        read_only_fields = ['id']


class DeliveryMethodSerializer(BaseModelSerializer):

    class Meta:
        model = DeliveryMethod
        fields = ['id', 'uuid', 'name', 'description', 'is_active']
        read_only_fields = ['id']



class CustomFieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomField
        fields = [
            'id', 'uuid', 'name', 'field_type', 'model_type', 'description',
            'is_required', 'is_active', 'select_options', 'default_value',
            'validation_regex', 'order'
        ]
        read_only_fields = ['company']

class CustomFieldValueSerializer(serializers.ModelSerializer):
    field_name = serializers.CharField(source='custom_field.name', read_only=True)
    field_type = serializers.CharField(source='custom_field.field_type', read_only=True)

    class Meta:
        model = CustomFieldValue
        fields = ['id', 'uuid', 'custom_field', 'field_name', 'field_type', 'value']
        read_only_fields = ['company']

    def validate(self, data):
        custom_field = data['custom_field']
        value = data['value']

        # Validate based on field type
        if custom_field.field_type == 'number':
            try:
                float(value)
            except (TypeError, ValueError):
                raise serializers.ValidationError("Value must be a number")
        elif custom_field.field_type == 'boolean':
            if not isinstance(value, bool):
                raise serializers.ValidationError("Value must be a boolean")
        elif custom_field.field_type == 'date':
            try:
                datetime.strptime(value, '%Y-%m-%d')
            except (TypeError, ValueError):
                raise serializers.ValidationError("Value must be a valid date (YYYY-MM-DD)")
        elif custom_field.field_type == 'select':
            if value not in custom_field.select_options:
                raise serializers.ValidationError("Value must be one of the select options")

        # Validate regex if specified
        if custom_field.validation_regex and isinstance(value, str):
            if not re.match(custom_field.validation_regex, value):
                raise serializers.ValidationError("Value does not match validation pattern")

        return data



