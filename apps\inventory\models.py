# inventory/models.py
from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from apps.base.models import CompanyFKRel
from apps.products.models import Product, ProductVariant
from utils.enums import InventoryMovementType, InventoryMovementStatus, AdjustmentType, AdjustmentStatus, QualityStatus
from django.utils import timezone

class Location(CompanyFKRel):
    """
    Represents physical locations within a warehouse where inventory can be stored.
    Supports hierarchical structure and different location types.
    """
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=50)
    parent = models.ForeignKey(
        'self',
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='children',
        help_text="Parent location, if this is a sub-location"
    )
    warehouse = models.ForeignKey(
        'core.Warehouse',
        on_delete=models.CASCADE,
        related_name='locations'
    )
    
    # Location type flags
    is_receiving = models.BooleanField(
        default=False,
        help_text="Whether this is a receiving location"
    )
    is_shipping = models.BooleanField(
        default=False,
        help_text="Whether this is a shipping location"
    )
    is_storage = models.BooleanField(
        default=True,
        help_text="Whether this is a storage location"
    )
    is_quality_control = models.BooleanField(
        default=False,
        help_text="Whether this is a quality control location"
    )
    is_returns = models.BooleanField(
        default=False,
        help_text="Whether this is a returns processing location"
    )
    
    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['warehouse', 'number']
        constraints = [
            models.UniqueConstraint(
                fields=['company'],
                condition=models.Q(is_quality_control=True),
                name='unique_quality_control_location_per_company'
            ),
            models.UniqueConstraint(
                fields=['company'],
                condition=models.Q(is_returns=True),
                name='unique_returns_location_per_company'
            )
        ]
        ordering = ['-id']

    def __str__(self):
        return f"{self.warehouse.name} - {self.name} ({self.number})"

class Lot(CompanyFKRel):
    """
    Represents a batch or lot of products, used for tracking product groups
    with shared properties like manufacturing date or expiry date.
    """
    number = models.CharField(
        max_length=100,
        help_text="Lot/batch number"
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='lots',
        help_text="Product this lot belongs to"
    )
    variant = models.ForeignKey(
        ProductVariant,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='lots',
        help_text="Product variant this lot belongs to, if applicable"
    )
    manufactured_date = models.DateField(
        null=True,
        blank=True,
        help_text="Date of manufacture"
    )
    expiry_date = models.DateField(
        null=True,
        blank=True,
        help_text="Expiry date if applicable"
    )
    supplier_lot_number = models.CharField(
        max_length=100,
        blank=True,
        help_text="Supplier's original lot number"
    )
    notes = models.TextField(blank=True)
    is_blocked = models.BooleanField(
        default=False,
        help_text="Whether this lot is blocked from use"
    )

    class Meta:
        unique_together = ['company', 'number']

    def __str__(self):
        return f"{self.product.name} - Lot {self.number}"

class InventoryItem(CompanyFKRel):
    """
    Represents actual physical inventory at a specific location.
    Tracks quantities, reservations, and cost information.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='inventory_items'
    )
    variant = models.ForeignKey(
        ProductVariant,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='inventory_items'
    )
    lot = models.ForeignKey(
        Lot,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        related_name='inventory_items',
        help_text="Lot/batch number if applicable"
    )
    location = models.ForeignKey(
        Location,
        on_delete=models.CASCADE,
        related_name='inventory_items'
    )
    
    # Quantities
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Current physical quantity at this location"
    )
    reserved_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Quantity reserved for orders or other purposes"
    )
    
    # Cost tracking
    cost_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Actual cost price for this specific inventory batch"
    )
    
    # Status tracking
    quality_status = models.CharField(
        max_length=40,
        choices=QualityStatus.choices(),
        default='GOOD',
        help_text="Current quality status of the inventory"
    )
    last_counted_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="Date of last physical inventory count"
    )

    class Meta:
        unique_together = ['company', 'product', 'variant', 'lot', 'location']
        indexes = [
            models.Index(fields=['company', 'product', 'location']),
            models.Index(fields=['quality_status']),
        ]

    def __str__(self):
        base = f"{self.product.name} at {self.location.name}"
        if self.lot:
            base += f" (Lot: {self.lot.number})"
        return base

    def available_quantity(self):
        """Calculate quantity available for use/sale"""
        return self.quantity - self.reserved_quantity

class WarehouseProductSummary(CompanyFKRel):
    """
    Aggregated view of inventory levels by product and warehouse.
    Updated automatically through signals when InventoryItem changes.
    Used for quick access to stock levels without complex queries.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        help_text="Product being summarized"
    )
    variant = models.ForeignKey(
        ProductVariant,
        null=True,
        blank=True,
        on_delete=models.CASCADE,
        help_text="Product variant being summarized, if applicable"
    )
    warehouse = models.ForeignKey(
        'core.Warehouse',
        on_delete=models.CASCADE,
        help_text="Warehouse this summary is for"
    )
    
    # Aggregated quantities
    total_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total quantity across all locations in warehouse"
    )
    reserved_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total reserved quantity across all locations"
    )
    available_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Total available quantity (total - reserved)"
    )
    
    # Status flags for quick filtering
    needs_reorder = models.BooleanField(
        default=False,
        help_text="Whether product needs reordering based on min stock level"
    )
    has_quality_issues = models.BooleanField(
        default=False,
        help_text="Whether any inventory has quality issues"
    )
    
    last_updated = models.DateTimeField(
        auto_now=True,
        help_text="When this summary was last updated"
    )

    class Meta:
        verbose_name_plural = "warehouse product summaries"
        unique_together = ['company', 'product', 'variant', 'warehouse']
        indexes = [
            models.Index(fields=['needs_reorder']),
            models.Index(fields=['has_quality_issues']),
        ]

    def __str__(self):
        base = f"{self.product.name} in {self.warehouse.name}"
        if self.variant:
            base += f" ({self.variant.name})"
        return base

class InventoryMovement(CompanyFKRel):
    """
    Records movement of inventory between locations or status changes.
    Tracks the complete history of inventory movements.
    """
    reference_number = models.CharField(
        max_length=100,
        help_text="Unique reference number for this movement"
    )
    movement_type = models.CharField(
        max_length=40,
        choices=InventoryMovementType.choices(),
        help_text="Type of inventory movement"
    )
    from_location = models.ForeignKey(
        Location,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='outbound_movements',
        help_text="Source location (null for receipts)"
    )
    to_location = models.ForeignKey(
        Location,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='inbound_movements',
        help_text="Destination location (null for issues)"
    )
    status = models.CharField(
        max_length=40,
        choices=InventoryMovementStatus.choices(),
        default=InventoryMovementStatus.DRAFT.name,
        help_text="Current status of the movement"
    )
    notes = models.TextField(
        blank=True,
        null=True,
        help_text="Additional notes about this movement"
    )
    scheduled_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this movement is scheduled to occur"
    )
    completed_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this movement was completed"
    )

    def __str__(self):
        return f"{self.movement_type} - {self.reference_number}"

class InventoryMovementLine(CompanyFKRel):
    """
    Individual line items within an inventory movement.
    Records the specific products and quantities being moved.
    """
    movement = models.ForeignKey(
        InventoryMovement,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    product = models.ForeignKey(
        Product,
        on_delete=models.PROTECT,
        related_name='movement_lines'
    )
    variant = models.ForeignKey(
        ProductVariant,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='movement_lines'
    )
    lot = models.ForeignKey(
        Lot,
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='movement_lines',
        help_text="Lot/batch number if applicable"
    )
    quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Quantity being moved"
    )
    cost_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Cost price of the items being moved"
    )

    def __str__(self):
        return f"{self.movement.reference_number} - {self.product.name}"
    



class InventoryAdjustment(CompanyFKRel):
    """
    Records corrections to inventory quantities that aren't movements between locations.
    Examples: damage, shrinkage, found items, cycle count corrections.
    """
    adjustment_date = models.DateField(
        help_text="Date of the adjustment",
        null=True,
        blank=True
    )
    reference_number = models.CharField(
        max_length=100,
        help_text="Unique reference number for this adjustment"
    )
    location = models.ForeignKey(
        'Location',
        on_delete=models.PROTECT,
        related_name='inventory_adjustments',
        help_text="Location where adjustment occurred"
    )
    adjustment_type = models.CharField(
        max_length=40,
        choices=AdjustmentType.choices(),
        help_text="Reason for the adjustment"
    )
    status = models.CharField(
        max_length=40,
        choices=AdjustmentStatus.choices(),
        default=AdjustmentStatus.DRAFT.name,
        help_text="Current status of the adjustment"
    )
    notes = models.TextField(
        blank=True,
        help_text="Additional notes about this adjustment"
    )
    approved_by = models.ForeignKey(
        'core.Employee',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='approved_adjustments',
        help_text="Employee who approved this adjustment"
    )
    approved_at = models.DateTimeField(
        null=True,
        blank=True,
        help_text="When this adjustment was approved"
    )

    class Meta:
        indexes = [
            models.Index(fields=['company', 'reference_number']),
            models.Index(fields=['created_at']),
            models.Index(fields=['status']),
        ]

    def __str__(self):
        return f"Adjustment {self.reference_number} - {self.adjustment_type}"
    
    def save(self, *args, **kwargs):
        if not self.adjustment_date:
            self.adjustment_date = timezone.now().date()
        super().save(*args, **kwargs)
    
    

class InventoryAdjustmentLine(CompanyFKRel):
    """
    Individual line items within an inventory adjustment.
    Records the specific changes to inventory quantities.
    """
    adjustment = models.ForeignKey(
        InventoryAdjustment,
        on_delete=models.CASCADE,
        related_name='lines'
    )
    inventory_item = models.ForeignKey(
        'InventoryItem',
        on_delete=models.PROTECT,
        related_name='adjustment_lines',
        help_text="Inventory item being adjusted"
    )
    product = models.ForeignKey(
        'products.Product',
        on_delete=models.PROTECT,
        related_name='adjustment_lines'
    )
    variant = models.ForeignKey(
        'products.ProductVariant',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='adjustment_lines'
    )
    lot = models.ForeignKey(
        'Lot',
        null=True,
        blank=True,
        on_delete=models.PROTECT,
        related_name='adjustment_lines'
    )
    previous_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Quantity before adjustment"
    )
    new_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Quantity after adjustment"
    )
    cost_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Cost price of the items for value calculation"
    )

    class Meta:
        indexes = [
            models.Index(fields=['company', 'product']),
            models.Index(fields=['company', 'variant']),
        ]

    def __str__(self):
        return f"{self.adjustment.reference_number} - {self.product.name}"

    @property
    def adjustment_quantity(self):
        """Calculate the net change in quantity"""
        return self.new_quantity - self.previous_quantity

    @property
    def adjustment_value(self):
        """Calculate the value impact of the adjustment"""
        return self.adjustment_quantity * self.cost_price

