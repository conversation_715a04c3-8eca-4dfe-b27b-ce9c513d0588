<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{% block title %}Invoice{% endblock %}</title>
  <style>
    /* Base styles */
    :root {
      --primary-color: #4A6FDC;
      --secondary-color: #f8f9fa;
      --border-color: #e9ecef;
      --text-color: #212529;
      --light-text: #6c757d;
      --success-color: #28a745;
      --warning-color: #ffc107;
      --danger-color: #dc3545;
    }

    body {
      font-family: 'Helvetica Neue', Arial, sans-serif;
      font-size: 14px;
      line-height: 1.5;
      color: var(--text-color);
      margin: 0;
      padding: 0;
    }

    .invoice-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px;
      border: 1px solid var(--border-color);
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
    }

    .invoice-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 40px;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 20px;
    }

    .invoice-logo {
      height: 60px;
      width: auto;
    }

    .invoice-company {
      text-align: right;
    }

    .invoice-title {
      font-size: 28px;
      color: var(--primary-color);
      margin-bottom: 10px;
      font-weight: 600;
    }

    .invoice-subtitle {
      color: var(--light-text);
      font-size: 16px;
      margin-top: 0;
    }

    .invoice-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 30px;
    }

    .invoice-party {
      max-width: 48%;
    }

    .invoice-party h3 {
      color: var(--primary-color);
      font-size: 16px;
      border-bottom: 1px solid var(--border-color);
      padding-bottom: 5px;
      margin-bottom: 10px;
    }

    .invoice-info {
      margin-bottom: 30px;
      display: flex;
      justify-content: space-between;
    }

    .invoice-metadata {
      min-width: 200px;
    }

    .invoice-metadata table {
      width: 100%;
    }

    .invoice-metadata th {
      text-align: left;
      padding: 5px 10px 5px 0;
      color: var(--light-text);
    }

    .invoice-metadata td {
      text-align: right;
      padding: 5px 0 5px 10px;
    }

    .invoice-items {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 30px;
    }

    .invoice-items th {
      background-color: var(--primary-color);
      color: white;
      padding: 12px 8px;
      text-align: left;
    }

    .invoice-items td {
      padding: 12px 8px;
      border-bottom: 1px solid var(--border-color);
    }

    .invoice-items .text-right {
      text-align: right;
    }

    .invoice-items tr:nth-child(even) {
      background-color: var(--secondary-color);
    }

    .invoice-summary {
      display: flex;
      justify-content: flex-end;
      margin-bottom: 30px;
    }

    .invoice-totals {
      width: 250px;
    }

    .invoice-totals table {
      width: 100%;
    }

    .invoice-totals th {
      text-align: left;
      padding: 5px 0;
      font-weight: normal;
      color: var(--light-text);
    }

    .invoice-totals td {
      text-align: right;
      padding: 5px 0;
    }

    .invoice-totals .total-row th,
    .invoice-totals .total-row td {
      font-size: 18px;
      font-weight: bold;
      color: var(--primary-color);
      border-top: 1px solid var(--primary-color);
      padding-top: 10px;
    }

    .invoice-notes {
      margin-bottom: 30px;
      padding: 15px;
      background-color: var(--secondary-color);
      border-radius: 4px;
    }

    .invoice-notes h3 {
      margin-top: 0;
      color: var(--primary-color);
      font-size: 16px;
    }

    .invoice-payment {
      margin-bottom: 30px;
      border-top: 1px solid var(--border-color);
      padding-top: 20px;
    }

    .invoice-payment h3 {
      color: var(--primary-color);
      font-size: 16px;
      margin-bottom: 10px;
    }

    .invoice-footer {
      text-align: center;
      color: var(--light-text);
      font-size: 12px;
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid var(--border-color);
    }

    .badge {
      display: inline-block;
      padding: 5px 10px;
      font-size: 12px;
      border-radius: 4px;
      font-weight: bold;
      text-transform: uppercase;
    }

    .badge-success {
      background-color: #e3f9e5;
      color: #1a7431;
    }

    .badge-warning {
      background-color: #fff8e6;
      color: #a76800;
    }

    .badge-danger {
      background-color: #feebee;
      color: #b00020;
    }

    .badge-info {
      background-color: #e6f7ff;
      color: #0060b6;
    }

    @media print {
      body {
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
      }

      .invoice-container {
        box-shadow: none;
        border: none;
        padding: 20px;
      }

      @page {
        margin: 0.5cm;
      }
    }
  </style>
  {% block extra_styles %}{% endblock %}
</head>

<body>
  <div class="invoice-container">
    <div class="invoice-header">
      <div class="invoice-branding">
        <img src="{% block company_logo %}{% endblock %}" alt="Company Logo" class="invoice-logo">
        <h1 class="invoice-title">{% block invoice_type %}Invoice{% endblock %}</h1>
        <p class="invoice-subtitle">{% block invoice_subtitle %}{% endblock %}</p>
      </div>
      <div class="invoice-company">
        <h2>{{ company.name }}</h2>
        <p>{{ company.address }}</p>
        <p>{{ company.city }}, {{ company.state }} {{ company.zip_code }}</p>
        <p>{{ company.country }}</p>
        <p>{{ company.email }} | {{ company.phone }}</p>
        {% if company.vat_number %}<p>VAT: {{ company.vat_number }}</p>{% endif %}
      </div>
    </div>

    <div class="invoice-details">
      <div class="invoice-party">
        <h3>{% block from_title %}From{% endblock %}</h3>
        {% block from_details %}{% endblock %}
      </div>
      <div class="invoice-party">
        <h3>{% block to_title %}To{% endblock %}</h3>
        {% block to_details %}{% endblock %}
      </div>
    </div>

    <div class="invoice-info">
      <div class="invoice-metadata">
        <table>
          <tr>
            <th>{% block invoice_number_label %}Invoice Number{% endblock %}:</th>
            <td>{% block invoice_number %}{% endblock %}</td>
          </tr>
          <tr>
            <th>{% block invoice_date_label %}Invoice Date{% endblock %}:</th>
            <td>{% block invoice_date %}{% endblock %}</td>
          </tr>
          <tr>
            <th>Due Date:</th>
            <td>{% block due_date %}{% endblock %}</td>
          </tr>
          <tr>
            <th>Status:</th>
            <td>{% block status %}{% endblock %}</td>
          </tr>
        </table>
      </div>
      <div class="invoice-metadata">
        <table>
          <tr>
            <th>Payment Terms:</th>
            <td>{% block payment_terms %}{% endblock %}</td>
          </tr>
          {% if show_po_number %}
          <tr>
            <th>PO Number:</th>
            <td>{% block po_number %}{% endblock %}</td>
          </tr>
          {% endif %}
          <tr>
            <th>Currency:</th>
            <td>{% block currency %}{% endblock %}</td>
          </tr>
          {% block extra_metadata %}{% endblock %}
        </table>
      </div>
    </div>

    <table class="invoice-items">
      <thead>
        <tr>
          <th>Item</th>
          <th>Description</th>
          <th>Quantity</th>
          <th>Unit Price</th>
          <th>Tax (%)</th>
          <th class="text-right">Amount</th>
        </tr>
      </thead>
      <tbody>
        {% block invoice_items %}{% endblock %}
      </tbody>
    </table>

    <div class="invoice-summary">
      <div class="invoice-totals">
        <table>
          <tr>
            <th>Subtotal:</th>
            <td>{% block subtotal %}{% endblock %}</td>
          </tr>
          <tr>
            <th>Tax:</th>
            <td>{% block tax_amount %}{% endblock %}</td>
          </tr>
          {% if show_discount %}
          <tr>
            <th>Discount:</th>
            <td>{% block discount %}{% endblock %}</td>
          </tr>
          {% endif %}
          <tr class="total-row">
            <th>Total:</th>
            <td>{% block total %}{% endblock %}</td>
          </tr>
        </table>
      </div>
    </div>

    {% if notes %}
    <div class="invoice-notes">
      <h3>Notes</h3>
      <p>{{ notes }}</p>
    </div>
    {% endif %}

    <div class="invoice-payment">
      <h3>Payment Information</h3>
      {% block payment_info %}{% endblock %}
    </div>

    <div class="invoice-footer">
      <p>{{ company.name }} | {{ company.registration_number }}</p>
      <p>{{ company.website }}</p>
      <p>Thank you for your business!</p>
    </div>
  </div>
</body>

</html>