# PDF Generation Module

This module provides functionality to generate professional-looking PDF documents for sales and supplier invoices using WeasyPrint.

## Features

- Modern, responsive invoice templates with clean layout
- Support for both sales invoices and supplier invoices
- Style customization options
- Logo and branding integration
- Automatic filename generation based on invoice details

## Requirements

- WeasyPrint library (https://weasyprint.org/)
- Django templating system

## Usage

### Generating PDFs from Code

```python
from apps.documents.services.pdf_generator import PDFGenerator
from apps.sales.models import SalesInvoice

# Get your invoice
invoice = SalesInvoice.objects.get(pk=1)

# Generate the PDF
pdf_content, filename = PDFGenerator.generate_sales_invoice_pdf(invoice)

# Save to file or serve via HTTP
with open(filename, 'wb') as f:
    f.write(pdf_content)
```

### API Endpoints

- Sales Invoice PDF: `/api/sales/customer-invoices/{uuid}/pdf/`
- Supplier Invoice PDF: `/api/purchasing/supplier-invoices/{uuid}/pdf/`

## Customization

You can customize the invoice templates by:

1. Modifying the HTML templates in `templates/invoices/`
2. Adjusting the CSS in the templates
3. Extending the templates for specific needs

## Template Structure

- `base.html` - Base template with common structure and styles
- `sales_invoice.html` - Template for sales invoices
- `supplier_invoice.html` - Template for supplier invoices

## Adding New Document Types

To add support for a new document type:

1. Create a new HTML template extending `base.html`
2. Add a new method to the `PDFGenerator` class
3. Create an API endpoint to serve the PDF

## Styling Reference

The templates use CSS variables for consistent styling:

```css
:root {
  --primary-color: #4a6fdc;
  --secondary-color: #f8f9fa;
  --border-color: #e9ecef;
  --text-color: #212529;
  --light-text: #6c757d;
}
```

You can customize these variables to match your branding.
