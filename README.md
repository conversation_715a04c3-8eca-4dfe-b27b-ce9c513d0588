# WMS (Warehouse Management System)

A comprehensive warehouse and inventory management system built with Django and Django REST Framework. The system handles everything from inventory management to sales, purchasing, and customer relations.

## Core Features

- **Multi-tenant Architecture**: Each company has its own isolated data
- **Inventory Management**: Track stock levels, locations, and movements
- **Sales Management**: Handle orders, shipments, returns, and invoicing
- **Purchase Management**: Manage suppliers, purchase orders, and goods receipt
- **Customer Management**: Track customers, pricing, and credit management
- **Supplier Management**: Manage suppliers, contracts, and performance

## Pricing System

The system implements a flexible pricing structure that allows for complex pricing scenarios while maintaining clarity and ease of use.

### Price Determination Hierarchy

Prices are determined in the following order:

1. **Customer-specific Price Lists**: Individual prices for specific customers
2. **Variant-specific Pricing**: Special pricing for product variants
3. **Product Current Price**: Standard current product price
4. **Product Base Price**: Default product price

### Price Lists

Price lists allow for customer-specific pricing:

```python
CustomerPriceList:
name: "VIP Customer Prices"
customers: [Customer1, Customer2] # M2M relationship
is_active: True
valid_from: Date
valid_to: Date (optional)
```

Individual prices within a price list:

```python
PriceListItem:
price_list: CustomerPriceList
product: Product
variant: ProductVariant (optional)
unit_price: Decimal
min_quantity: Decimal
```

### Discount System

The system supports multiple types of discounts that can be combined:

#### Discount Types

- **Percentage**: e.g., 10% off
- **Fixed Amount**: e.g., $50 off
- **Fixed Price**: e.g., fixed price of $99

#### Discount Rules

Discounts can be applied to:

- Specific customers
- Customer categories
- Specific products
- Product categories
- Combinations of the above

#### Discount Conditions

- Valid date range
- Minimum order value
- Maximum order value
- Usage limits (per customer or total)

### Usage Example

```python
# Get price for a customer
price_info = pricing_service.get_customer_price(
customer=customer,
product=product,
variant=variant,
quantity=5,
order_value=500.00
)
Response
{
'base_price': Decimal('100.00'),
'discounts': [
{
'rule': discount_rule,
'amount': Decimal('10.00'),
'type': 'PERCENTAGE',
'description': '10% VIP Discount'
}
],
'final_price': Decimal('90.00'),
'total_discount_amount': Decimal('10.00'),
'currency': 'USD'
}
```

## Technical Architecture

- **Backend**: Django & Django REST Framework
- **Database**: PostgreSQL
- **API**: RESTful API with comprehensive endpoints
- **Authentication**: JWT-based authentication
- **Permissions**: Role-based access control

### Key Models

- `Company`: Base model for multi-tenancy
- `Product`: Core product information
- `ProductVariant`: Product variations
- `Customer`: Customer information
- `Supplier`: Supplier information
- `SalesOrder`: Sales order processing
- `PurchaseOrder`: Purchase order management
- `Inventory`: Stock tracking
- `Location`: Warehouse locations

### Services

The system uses service-based architecture for business logic:

- `PricingService`: Handles price calculations and discounts
- `OrderService`: Manages order processing
- `InventoryService`: Handles stock movements
- `ShippingService`: Manages shipments
- `ReturnsService`: Handles return processing

## API Endpoints

The system provides comprehensive REST API endpoints for all functionality:

- `/api/products/`: Product management
- `/api/sales/`: Sales operations
- `/api/purchasing/`: Purchase operations
- `/api/inventory/`: Inventory management
- `/api/customers/`: Customer management
- `/api/suppliers/`: Supplier management

## Getting Started

1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Run migrations: `python manage.py migrate`
4. Create a superuser: `python manage.py createsuperuser`
5. Run the development server: `python manage.py runserver`
