# sales/services/returns.py
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.utils import timezone
from ..models import SalesOrder, ReturnOrder, ReturnLine
from apps.operations.services.movement import MovementService
from apps.inventory.models import Location, InventoryItem
from .base import BaseSalesService
from utils.enums import SalesOrderStatus, ReturnCreditNoteStatus, ReturnStatus, InspectionResult

class ReturnsService(BaseSalesService):
    @transaction.atomic
    def create_return(
        self,
        order_id: int,
        return_lines: List[Dict],
        reason: str,
        notes: str = None
    ) -> ReturnOrder:
        """Create a return order for a sales order"""
        sales_order = SalesOrder.objects.get(
            company=self.company,
            id=order_id
        )
        
        if sales_order.status not in [SalesOrderStatus.SHIPPED.name, SalesOrderStatus.DELIVERED.name]:
            raise ValueError("Can only create returns for shipped or delivered orders")

        return_order = ReturnOrder.objects.create(
            company=self.company,
            sales_order=sales_order,
            status=ReturnStatus.PENDING.name,
            reason=reason,
            notes=notes,
            created_by=self.user
        )

        # Process return lines
        for line in return_lines:
            order_line = sales_order.lines.get(id=line['order_line_id'])
            
            if line['quantity'] > order_line.shipped_quantity:
                raise ValueError(
                    f"Return quantity exceeds shipped quantity for {order_line.product.name}"
                )

            ReturnLine.objects.create(
                company=self.company,
                return_order=return_order,
                sales_order_line=order_line,
                quantity=line['quantity'],
                reason=line.get('reason'),
                condition=line.get('condition')
            )

        return return_order

    @transaction.atomic
    def process_return_receipt(
        self,
        return_id: int,
        received_lines: List[Dict]
    ) -> ReturnOrder:
        """Process the receipt of returned items"""
        return_order = ReturnOrder.objects.get(
            company=self.company,
            id=return_id
        )
        
        if return_order.status != ReturnStatus.PENDING.name:
            raise ValueError("Return must be in pending status")
        
        # Get or create quality control location
        quality_location = Location.objects.filter(
            company=self.company,
            is_quality_control=True
        ).first()
        
        if not quality_location:
            raise ValueError(
                "No quality control location defined for this company. "
                "Please set up a quality control location first."
            )

        movement_service = MovementService(self.user, self.company)
        quality_location = Location.objects.get(
            company=self.company,
            is_quality_control=True
        )

        for line in received_lines:
            return_line = return_order.lines.get(id=line['return_line_id'])
            
            # Create movement to quality control location
            movement_service.create_internal_movement(
                from_location=None,  # External return
                to_location=quality_location,
                lines=[{
                    'product': return_line.sales_order_line.product,
                    'variant': return_line.sales_order_line.variant,
                    'quantity': line['received_quantity'],
                    'lot': line.get('lot_number')
                }],
                reference_number=f"RET-{return_order.number}"
            )

            return_line.received_quantity = line['received_quantity']
            return_line.received_condition = line['condition']
            return_line.save()

        return_order.status = ReturnStatus.RECEIVED.name
        return_order.received_date = timezone.now()
        return_order.save()

        return return_order

    @transaction.atomic
    def process_quality_check(
        self,
        return_id: int,
        inspection_results: List[Dict]
    ) -> ReturnOrder:
        """Process quality inspection of returned items"""
        return_order = ReturnOrder.objects.get(
            company=self.company,
            id=return_id
        )
        
        if return_order.status != ReturnStatus.RECEIVED.name:
            raise ValueError("Return must be in received status")
        
        # Get or create quality control location
        quality_location = Location.objects.filter(
            company=self.company,
            is_quality_control=True
        ).first()
        
        if not quality_location:
            raise ValueError(
                "No quality control location defined for this company. "
                "Please set up a quality control location first."
            )

        movement_service = MovementService(self.user, self.company)
        
        stock_location = Location.objects.get(
            company=self.company,
            is_returns=True
        )

        for result in inspection_results:
            return_line = return_order.lines.get(id=result['return_line_id'])
            
            if result['decision'] == InspectionResult.RETURN_TO_STOCK.name:
                # Move to stock location
                movement_service.create_internal_movement(
                    from_location=quality_location,
                    to_location=stock_location,
                    lines=[{
                        'product': return_line.sales_order_line.product,
                        'variant': return_line.sales_order_line.variant,
                        'quantity': result['quantity'],
                        'lot': result.get('lot_number')
                    }],
                    reference_number=f"RET-{return_order.number}"
                )
            
            return_line.inspection_result = result['decision']
            return_line.inspection_notes = result.get('notes')
            return_line.save()

        return_order.status = ReturnStatus.INSPECTED.name
        return_order.save()

        # If refund is needed, trigger refund process
        if any(line.inspection_result in [InspectionResult.RETURN_TO_STOCK.name, InspectionResult.DAMAGED_REFUND.name] 
               for line in return_order.lines.all()):
            self._process_refund(return_order)

        return return_order

    def _process_refund(self, return_order: ReturnOrder) -> None:
        """Process refund for eligible returned items"""
        refund_amount = Decimal('0')
        
        for line in return_order.lines.all():
            if line.inspection_result in [InspectionResult.RETURN_TO_STOCK.name, InspectionResult.DAMAGED_REFUND.name]:
                original_line = line.sales_order_line
                line_refund = (
                    original_line.unit_price - original_line.discount
                ) * line.received_quantity
                refund_amount += line_refund

        if refund_amount > 0:
            # Create credit note or trigger refund process
            pass