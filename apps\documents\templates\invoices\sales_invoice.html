{% extends "invoices/base.html" %}

{% block title %}Sales Invoice #{{ invoice.number }}{% endblock %}

{% block invoice_type %}Sales Invoice{% endblock %}

{% block invoice_subtitle %}
{% if invoice.is_credit_note %}Credit Note{% endif %}
{% endblock %}

{% block company_logo %}{{ company_logo_url|default:"" }}{% endblock %}

{% block from_title %}Seller{% endblock %}

{% block from_details %}
<div>
  <p><strong>{{ company.name }}</strong></p>
  <p>{{ company.address|linebreaksbr }}</p>
  {% if company.registration_number %}<p>Reg. No: {{ company.registration_number }}</p>{% endif %}
  {% if company.vat_number %}<p>VAT ID: {{ company.vat_number }}</p>{% endif %}
</div>
{% endblock %}

{% block to_title %}Customer{% endblock %}

{% block to_details %}
<div>
  <p><strong>{{ invoice.customer.name }}</strong></p>
  <p>{{ invoice.customer.address|linebreaksbr }}</p>
  {% if invoice.customer.tax_number %}<p>Tax ID: {{ invoice.customer.tax_number }}</p>{% endif %}
  {% if invoice.customer.vat_zone %}<p>VAT Zone: {{ invoice.customer.vat_zone }}</p>{% endif %}

  {% if invoice.customer.contact_person %}
  <p>Contact: {{ invoice.customer.contact_person.name }}{% if invoice.customer.contact_person.role %}, {{
    invoice.customer.contact_person.role }}{% endif %}</p>
  <p>{{ invoice.customer.contact_person.email }}{% if invoice.customer.contact_person.phone %} | {{
    invoice.customer.contact_person.phone }}{% endif %}</p>
  {% endif %}
</div>
{% endblock %}

{% block invoice_number_label %}Invoice Number{% endblock %}
{% block invoice_number %}{{ invoice.number }}{% endblock %}

{% block invoice_date_label %}Invoice Date{% endblock %}
{% block invoice_date %}{{ invoice.invoice_date|date:"F j, Y" }}{% endblock %}

{% block due_date %}{{ invoice.due_date|date:"F j, Y" }}{% endblock %}

{% block status %}
{% if invoice.status == 'draft' %}
<span class="badge badge-info">Draft</span>
{% elif invoice.status == 'sent' %}
<span class="badge badge-warning">Sent</span>
{% elif invoice.status == 'paid' %}
<span class="badge badge-success">Paid</span>
{% elif invoice.status == 'overdue' %}
<span class="badge badge-danger">Overdue</span>
{% elif invoice.status == 'cancelled' %}
<span class="badge badge-danger">Cancelled</span>
{% else %}
<span class="badge">{{ invoice.status|title }}</span>
{% endif %}
{% endblock %}

{% block payment_terms %}
{{ invoice.payment_term.name|default:"" }}
{% endblock %}

{% block currency %}{{ invoice.currency.currency }}{% endblock %}

{% block extra_metadata %}
{% if invoice.sales_order %}
<tr>
  <th>Order Reference:</th>
  <td>{{ invoice.sales_order.number }}</td>
</tr>
{% endif %}
{% if invoice.customer_reference %}
<tr>
  <th>Customer Reference:</th>
  <td>{{ invoice.customer_reference }}</td>
</tr>
{% endif %}
{% endblock %}

{% block invoice_items %}
{% for line in invoice.lines.all %}
<tr>
  <td>{{ line.product.name|default:"-" }}</td>
  <td>{{ line.description }}</td>
  <td>{{ line.quantity }} {{ line.product.unit|default:"" }}</td>
  <td>{{ line.unit_price|floatformat:2 }}</td>
  <td>{{ line.tax_rate|floatformat:2 }}%</td>
  <td class="text-right">{{ line.total_amount|floatformat:2 }}</td>
</tr>
{% endfor %}
{% endblock %}

{% block subtotal %}{{ invoice.total_net_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block tax_amount %}{{ invoice.total_tax_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block discount %}{{ invoice.total_discount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}
{% block total %}{{ invoice.total_amount|floatformat:2 }} {{ invoice.currency.currency }}{% endblock %}

{% block payment_info %}
<div>
  <p><strong>Bank Details:</strong></p>
  <p>Account Name: {{ company.bank_account_name|default:"" }}</p>
  <p>Account Number: {{ company.bank_account_number|default:"" }}</p>
  <p>Bank: {{ company.bank_name|default:"" }}</p>
  <p>IBAN: {{ company.iban|default:"" }}</p>
  <p>SWIFT/BIC: {{ company.swift|default:"" }}</p>

  {% if invoice.payment_reference %}
  <p><strong>Payment Reference:</strong> {{ invoice.payment_reference }}</p>
  {% endif %}
</div>
{% endblock %}