# sales/services/shipping.py
from decimal import Decimal
from typing import Dict, List
from django.db import transaction
from django.db.models import F, Sum
from django.utils import timezone
from ..models import (
    SalesOrder, SalesOrderLine, Shipment,
    ShipmentLine, ShipmentTracking
)
from apps.operations.services.movement import MovementService
from apps.inventory.models import Location, InventoryItem
from .base import BaseSalesService
from utils.enums import SalesOrderStatus, ShipmentStatus

class ShippingService(BaseSalesService):
    @transaction.atomic
    def plan_shipment(
        self,
        order_id: int,
        shipping_method: str = None,
        preferred_carrier: str = None
    ) -> Dict:
        """Plan shipment and change order status to SHIPPING"""
        order = SalesOrder.objects.get(
            company=self.company,
            id=order_id
        )
        
        # Validate order is ready
        if order.status not in [SalesOrderStatus.PACKED.name, SalesOrderStatus.PARTIAL_SHIPPED.name]:
            raise ValueError("Can only plan shipping for packed or partial shipped orders")
        
        # Change status to SHIPPING if not already
        if order.status == SalesOrderStatus.PACKED.name:
            order.status = SalesOrderStatus.SHIPPING.name
            order.save()
        
        # Get shipping options from carriers
        shipping_options = self._get_shipping_options(
            order,
            shipping_method,
            preferred_carrier
        )
        
        return {
            "order_status": order.status,
            "options": shipping_options
        }

    @transaction.atomic
    def create_shipment(
        self,
        order_id: int,
        shipping_method: str,
        shipping_lines: List[Dict],
        carrier: str = None,
        tracking_number: str = None
    ) -> Shipment:
        """Create shipment and handle inventory movements"""
        order = SalesOrder.objects.get(
            company=self.company,
            id=order_id
        )
        
        if order.status not in [SalesOrderStatus.SHIPPING.name, SalesOrderStatus.PARTIAL_SHIPPED.name]:
            raise ValueError("Order must be in SHIPPING status or PARTIAL_SHIPPED")
        # Get picking task to get locations
        from apps.operations.models import PickingTask
        picking_task = PickingTask.objects.get(
            company=self.company,
            sales_order=order
        )

        # Create shipment record
        shipment = Shipment.objects.create(
            company=self.company,
            sales_order=order,
            number=self._generate_shipment_number(),
            shipping_method=shipping_method,
            carrier=carrier,
            tracking_number=tracking_number or self._generate_tracking_number(),
            status=ShipmentStatus.PENDING.name,
            shipped_by=self.user,
            shipped_at=timezone.now()
        )

        movement_service = MovementService(self.user, self.company)

        # Process each shipping line
        for line in shipping_lines:
            order_line = order.lines.get(id=line['order_line_id'])
            
            # Get location from picking record
            picking_line = picking_task.lines.get(
                sales_order_line=order_line
            )

            shipment_line = ShipmentLine.objects.create(
                company=self.company,
                shipment=shipment,
                sales_order_line=order_line,
                quantity=line['quantity'],
                picked_from=picking_line.location,
                lot_number=line.get('lot_number'),
                serial_numbers=line.get('serial_numbers')
            )

            # Remove stock and release reservations
            self._remove_shipped_stock(
                order_line,
                line['quantity'],
                picking_line.location.id,
                movement_service
            )

            # Update order line shipped quantity
            order_line.shipped_quantity = (order_line.shipped_quantity or 0) + line['quantity']
            order_line.save()

        # Update order status
        if self._is_order_fully_shipped(order):
            order.status = SalesOrderStatus.SHIPPED.name
        else:
            order.status = SalesOrderStatus.PARTIAL_SHIPPED.name
        order.save()

        return shipment

    def update_shipment_status(
        self,
        shipment_id: int,
        status: str,
        tracking_info: Dict = None
    ) -> Shipment:
        """Update shipment status and tracking information"""
        shipment = Shipment.objects.get(
            company=self.company,
            id=shipment_id
        )
        
        shipment.status = status
        if tracking_info:
            ShipmentTracking.objects.create(
                company=self.company,
                shipment=shipment,
                event_date=tracking_info.get('date', timezone.now()),
                event_type=tracking_info.get('type'),
                location=tracking_info.get('location'),
                description=tracking_info.get('description'),
                carrier_reference=tracking_info.get('carrier_reference', '')
            )
        shipment.save()
        
        # If delivered, update order status
        if status == ShipmentStatus.DELIVERED.name:
            order = shipment.sales_order
            if self._is_order_fully_delivered(order):
                order.status = SalesOrderStatus.DELIVERED.name
                order.save()
        
        return shipment

    def _remove_shipped_stock(
        self,
        order_line: SalesOrderLine,
        ship_quantity: Decimal,
        location_id: int,
        movement_service: MovementService
    ) -> None:
        """
        Removes shipped stock from inventory and releases reservation
        """
        inventory_items = InventoryItem.objects.filter(
            company=self.company,
            product=order_line.product,
            variant=order_line.variant,
            location_id=location_id,
            reserved_quantity__gt=0
        ).order_by('created_at')
        
        remaining = ship_quantity
        for item in inventory_items:
            if remaining <= 0:
                break
                
            ship_from_item = min(remaining, item.reserved_quantity)
            
            # Create stock movement
            movement_service.create_internal_movement(
                from_location=Location.objects.get(
                    company=self.company,
                    is_storage=True,
                    id=location_id
                ),
                to_location=None,  # Shipping movement
                lines=[{
                    'product': order_line.product,
                    'variant': order_line.variant,
                    'quantity': ship_from_item,
                    'lot': item.lot
                }],
                reference_number=f"SHIP-{order_line.sales_order.number}"
            )
            
            # Update inventory item
            item.quantity = F('quantity') - ship_from_item
            item.reserved_quantity = F('reserved_quantity') - ship_from_item
            item.save()
            
            remaining -= ship_from_item

        if remaining > 0:
            raise ValueError(
                f"Insufficient reserved stock for product {order_line.product.name}"
            )

    def _is_order_fully_shipped(self, order: SalesOrder) -> bool:
        """Check if all order lines are fully shipped"""
        return not order.lines.filter(
            quantity__gt=F('shipped_quantity')
        ).exists()

    def _is_order_fully_delivered(self, order: SalesOrder) -> bool:
        """Check if all shipments for the order are delivered"""
        return not order.shipments.exclude(
            status=ShipmentStatus.DELIVERED.name
        ).exists()

    def _calculate_package_info(self, order: SalesOrder) -> Dict:
        """Calculate package dimensions and weight"""
        # Implementation would depend on your product specifications
        raise NotImplementedError("Package info calculation is not implemented")
        return {
            'weight': 0,  # Calculate based on products
            'dimensions': {
                'length': 0,
                'width': 0,
                'height': 0
            }
        }

    def _get_shipping_rates(
        self,
        shipping_address: str,
        package_info: Dict,
        preferred_carrier: str = None
    ) -> List[Dict]:
        """Get shipping rates from various carriers"""
        # Implementation would integrate with shipping carriers' APIs
        #raise NotImplementedError("Shipping rates are not implemented")
        return [{
            'carrier': 'Sample Carrier',
            'method': 'Standard',
            'rate': 10.00,
            'estimated_days': 3
        }]

    def _get_shipping_options(self, order: SalesOrder, shipping_method: str, preferred_carrier: str) -> List[Dict]:
        """Get shipping options from carriers"""
        # Implementation would integrate with shipping carriers' APIs
        #raise NotImplementedError("Shipping options are not implemented")
        return [{
            'carrier': 'Sample Carrier',
            'method': 'Standard',
            'rate': 10.00,
            'estimated_days': 3
        }]

    def _generate_shipment_number(self) -> str:
        """Generate unique shipment number"""
        # Implementation of shipment number generation
        #raise NotImplementedError("Shipment number generation is not implemented")
        return f"SHIP-{timezone.now().strftime('%Y%m%d%H%M%S')}"

    def _generate_tracking_number(self) -> str:
        """Generate unique tracking number"""
        # Implementation of tracking number generation
        #raise NotImplementedError("Tracking number generation is not implemented")
        return f"TRK-{timezone.now().strftime('%Y%m%d%H%M%S')}"