# authentication/urls.py
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from .views import CustomTokenObtainPairView, SwitchCompanyView, UserMeView

urlpatterns = [
    path('auth/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('auth/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('auth/switch-company/', SwitchCompanyView.as_view(), name='switch_company'),
    path('auth/me/', UserMeView.as_view(), name='user-me'),
]