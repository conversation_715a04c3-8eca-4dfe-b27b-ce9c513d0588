from django_filters import rest_framework as filters
from apps.operations.models import PickingTask

class PickingTaskFilter(filters.FilterSet):
    started_after = filters.DateTimeFilter(
        field_name='started_at',
        lookup_expr='gte'
    )
    started_before = filters.DateTimeFilter(
        field_name='started_at',
        lookup_expr='lte'
    )

    class Meta:
        model = PickingTask
        fields = {
            'status': ['exact'],
            'priority': ['exact', 'gte', 'lte'],
            'assigned_to': ['exact', 'isnull'],
            'sales_order': ['exact']
        }