from django_filters import rest_framework as filters
from apps.core.models import Warehouse, AuditLog, User, Employee, Company

class CompanyFilter(filters.FilterSet):
    class Meta:
        model = Company
        fields = {
            'name': ['exact', 'icontains'],
            'registration_number': ['exact', 'icontains'],
            'vat_number': ['exact', 'icontains'],
            'is_active': ['exact']
        }

class WarehouseFilter(filters.FilterSet):
    class Meta:
        model = Warehouse
        fields = {
            'name': ['exact', 'icontains'],
            'number': ['exact', 'icontains'],
            'is_active': ['exact']
        }


class AuditLogFilter(filters.FilterSet):
    date_from = filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='gte'
    )
    date_to = filters.DateTimeFilter(
        field_name='created_at',
        lookup_expr='lte'
    )

    class Meta:
        model = AuditLog
        fields = {
            'user': ['exact'],
            'content_type': ['exact'],
            'action': ['exact']
        }


class UserFilter(filters.FilterSet):
    company = filters.NumberFilter(
        field_name='companies',
        method='filter_by_company'
    )

    class Meta:
        model = User
        fields = {
            'email': ['exact', 'icontains'],
            'is_employee': ['exact'],
            'is_active': ['exact'],
            'is_verified': ['exact']
        }

    def filter_by_company(self, queryset, name, value):
        return queryset.filter(
            company_employee__company_id=value,
            company_employee__is_active=True
        )
    
class EmployeeFilter(filters.FilterSet):
    joined_after = filters.DateFilter(
        field_name='joined_at',
        lookup_expr='gte'
    )
    joined_before = filters.DateFilter(
        field_name='joined_at',
        lookup_expr='lte'
    )

    class Meta:
        model = Employee
        fields = {
            'role': ['exact'],
            'is_active': ['exact'],
            'user': ['exact'],
            'company': ['exact']
        }