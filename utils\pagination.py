from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.exceptions import APIException

class PaginationWarning(APIException):
    status_code = 400
    default_detail = 'Pagination parameter conflict'
    default_code = 'pagination_warning'

class CustomPageNumberPagination(PageNumberPagination):
    page_size = 50  # Set a default page size
    page_size_query_param = 'limit'
    max_page_size = 100

    def __init__(self):
        super().__init__()
        self.warnings = []

    def get_page_size(self, request):
        try:
            return int(request.query_params.get(self.page_size_query_param, self.page_size))
        except ValueError:
            return self.page_size

    def get_page_number(self, request, paginator):
        page = request.query_params.get(self.page_query_param)
        offset = request.query_params.get('offset')
        
        if page is not None:
            if offset is not None:
                self.warnings.append("Both 'page' and 'offset' parameters provided. 'offset' will be ignored.")
            return int(page)
        
        if offset is None:
            return 1  # Default to first page if neither page nor offset is provided
        
        page_size = self.get_page_size(request)
        try:
            offset = int(offset)
            return (offset // page_size) + 1
        except (TypeError, ValueError):
            return 1  # Default to first page if there's an error parsing offset or page_size

    def get_paginated_response(self, data):
        response_data = {
            'count': self.page.paginator.count,
            'next': self.get_next_link(),
            'previous': self.get_previous_link(),
            'current_page': self.page.number,
            'offset': (self.page.number - 1) * self.get_page_size(self.request),
            'total_count': self.page.paginator.count,
            'total_pages': self.page.paginator.num_pages,
            'items_per_page': self.get_page_size(self.request),
            'results': data,
        }
        
        if self.warnings:
            response_data['warnings'] = self.warnings

        response = Response(response_data)
        
        if self.warnings:
            response['X-Pagination-Warning'] = '; '.join(self.warnings)
        
        return response