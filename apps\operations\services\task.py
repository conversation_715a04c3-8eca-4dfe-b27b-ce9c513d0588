from typing import Dict, List, Optional
from itertools import cycle
from datetime import datetime, timedelta
from django.db import transaction
from django.db.models import Count, Sum, F, Q, Avg
from django.utils import timezone

from apps.core.models import User, Employee
from apps.operations.models import PickingTask
from apps.inventory.models import Location
from .base import BaseOperationService
from utils.enums import PickingTaskStatus, EmployeeRole

class TaskManagementService(BaseOperationService):
    @transaction.atomic
    def assign_tasks(
        self,
        task_ids: List[int],
        user_id: Optional[int] = None
    ) -> List[Dict]:
        """
        Assign tasks to a specific user or auto-assign based on workload
        """
        tasks = PickingTask.objects.filter(
            company=self.company,
            id__in=task_ids,
            status=PickingTaskStatus.PENDING.name
        )

        if not tasks:
            raise ValueError("No valid tasks to assign")

        if user_id:
            assignee = User.objects.get(id=user_id)
            # Verify user is active warehouse worker
            if not Employee.objects.filter(
                company=self.company,
                user=assignee,
                role=EmployeeRole.WAREHOUSE_WORKER.name,
                is_active=True
            ).exists():
                raise ValueError("Invalid assignee")
        else:
            assignee = self._get_optimal_assignee(tasks)

        assignments = []
        for task in tasks:
            task.assigned_to = assignee
            task.save()
            assignments.append({
                'task': task,
                'assignee': assignee,
                'assignment_time': timezone.now()
            })

        return assignments

    def prioritize_tasks(self, criteria: Dict = None) -> List[Dict]:
        """
        Prioritize pending tasks based on various criteria
        """
        tasks = PickingTask.objects.filter(
            company=self.company,
            status=PickingTaskStatus.PENDING.name
        ).select_related(
            'sales_order', 'assigned_to'
        )

        prioritized_tasks = []
        for task in tasks:
            priority_score = 0
            
            # Order age
            age_hours = (timezone.now() - task.created_at).total_seconds() / 3600
            priority_score += min(age_hours / 24, 5)  # Cap at 5 points

            # Customer priority (if implemented)
            if task.sales_order.customer.priority:
                priority_score += task.sales_order.customer.priority * 2

            # Order value
            order_value = task.sales_order.total_amount
            priority_score += min(order_value / 1000, 3)  # Cap at 3 points

            # Custom criteria
            if criteria:
                if criteria.get('express_delivery') and task.sales_order.is_express:
                    priority_score += 5
                if criteria.get('same_day_shipping') and task.sales_order.same_day_shipping:
                    priority_score += 3

            prioritized_tasks.append({
                'task': task,
                'priority_score': priority_score,
                'factors': {
                    'age_hours': age_hours,
                    'order_value': order_value,
                    'customer_priority': task.sales_order.customer.priority
                }
            })

        # Sort by priority score
        return sorted(
            prioritized_tasks,
            key=lambda x: x['priority_score'],
            reverse=True
        )

    def track_task_progress(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[int] = None
    ) -> Dict:
        """
        Track progress of tasks and worker performance
        """
        query = PickingTask.objects.filter(company=self.company)
        
        if start_date:
            query = query.filter(created_at__gte=start_date)
        if end_date:
            query = query.filter(created_at__lte=end_date)
        if user_id:
            query = query.filter(assigned_to_id=user_id)

        total_tasks = query.count()
        completed_tasks = query.filter(status=PickingTaskStatus.COMPLETED.name)
        in_progress = query.filter(status=PickingTaskStatus.IN_PROGRESS.name)

        avg_completion_time = completed_tasks.aggregate(
            avg_time=Avg(F('completed_at') - F('started_at'))
        )['avg_time']

        return {
            'total_tasks': total_tasks,
            'completed_tasks': completed_tasks.count(),
            'in_progress': in_progress.count(),
            'completion_rate': (completed_tasks.count() / total_tasks * 100) if total_tasks > 0 else 0,
            'avg_completion_time': avg_completion_time.total_seconds() / 3600 if avg_completion_time else 0,  # hours
            'by_status': self._get_tasks_by_status(query),
            'productivity_metrics': self._get_productivity_metrics(query)
        }

    def get_worker_performance(
        self,
        user_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict:
        """
        Get detailed performance metrics for warehouse workers
        """
        query = Employee.objects.filter(
            company=self.company,
            role=EmployeeRole.WAREHOUSE_WORKER.name,
            is_active=True
        )

        if user_id:
            query = query.filter(user_id=user_id)

        performance_data = []
        for employee in query:
            tasks = PickingTask.objects.filter(
                company=self.company,
                assigned_to=employee.user
            )

            if start_date:
                tasks = tasks.filter(created_at__gte=start_date)
            if end_date:
                tasks = tasks.filter(created_at__lte=end_date)

            completed_tasks = tasks.filter(status=PickingTaskStatus.COMPLETED.name)
            
            performance_data.append({
                'employee': employee,
                'metrics': {
                    'total_tasks': tasks.count(),
                    'completed_tasks': completed_tasks.count(),
                    'avg_completion_time': self._calculate_avg_completion_time(completed_tasks),
                    'lines_per_hour': self._calculate_lines_per_hour(completed_tasks),
                    'accuracy_rate': self._calculate_accuracy_rate(completed_tasks)
                }
            })

        return performance_data

    def plan_daily_workload(
        self,
        target_date: datetime = None,
        shift_hours: int = 8
    ) -> Dict:
        """
        Plan daily workload and estimate resource needs
        """
        if not target_date:
            target_date = timezone.now().date()

        # Get pending tasks
        pending_tasks = PickingTask.objects.filter(
            company=self.company,
            status=PickingTaskStatus.PENDING.name
        ).select_related('sales_order')

        # Get available workers
        available_workers = Employee.objects.filter(
            company=self.company,
            role=EmployeeRole.WAREHOUSE_WORKER.name,
            is_active=True
        )

        # Calculate workload
        total_lines = sum(task.lines.count() for task in pending_tasks)
        avg_lines_per_hour = self._get_avg_lines_per_hour()
        
        estimated_hours = total_lines / avg_lines_per_hour if avg_lines_per_hour > 0 else 0
        workers_needed = round(estimated_hours / shift_hours + 0.5)  # Round up

        return {
            'date': target_date,
            'pending_tasks': pending_tasks.count(),
            'total_lines': total_lines,
            'available_workers': available_workers.count(),
            'workers_needed': workers_needed,
            'estimated_hours': estimated_hours,
            'shift_hours': shift_hours,
            'workload_distribution': self._plan_workload_distribution(
                pending_tasks,
                available_workers,
                shift_hours
            )
        }

    def _get_optimal_assignee(self, tasks: List[PickingTask]) -> User:
        """Find the optimal worker to assign tasks to"""
        workers = Employee.objects.filter(
            company=self.company,
            role=EmployeeRole.WAREHOUSE_WORKER.name,
            is_active=True
        )

        worker_scores = []
        for worker in workers:
            active_tasks = PickingTask.objects.filter(
                company=self.company,
                assigned_to=worker.user,
                status=PickingTaskStatus.IN_PROGRESS.name
            ).count()

            # Calculate worker's current workload and performance
            score = self._calculate_worker_score(worker, active_tasks)
            worker_scores.append((worker.user, score))

        # Return user with best score
        return max(worker_scores, key=lambda x: x[1])[0]

    def _calculate_worker_score(self, worker: Employee, active_tasks: int) -> float:
        """Calculate a score for worker availability and performance"""
        base_score = 100

        # Penalize for each active task
        base_score -= active_tasks * 10

        # Consider historical performance
        completed_tasks = PickingTask.objects.filter(
            company=self.company,
            assigned_to=worker.user,
            status=PickingTaskStatus.COMPLETED.name
        )

        if completed_tasks.exists():
            avg_completion_time = completed_tasks.aggregate(
                avg_time=Avg(F('completed_at') - F('started_at'))
            )['avg_time']
            
            if avg_completion_time:
                # Adjust score based on average completion time
                hours = avg_completion_time.total_seconds() / 3600
                if hours <= 1:
                    base_score += 20
                elif hours <= 2:
                    base_score += 10

        return base_score

    def _get_tasks_by_status(self, tasks) -> Dict:
        """Get breakdown of tasks by status"""
        return dict(tasks.values_list('status').annotate(count=Count('id')))

    def _get_productivity_metrics(self, tasks) -> Dict:
        """Calculate productivity metrics"""
        completed = tasks.filter(status=PickingTaskStatus.COMPLETED.name)
        
        return {
            'lines_per_hour': self._calculate_lines_per_hour(completed),
            'accuracy_rate': self._calculate_accuracy_rate(completed),
            'completion_time_trend': self._get_completion_time_trend(completed)
        }

    def _calculate_avg_completion_time(self, tasks) -> float:
        """Calculate average task completion time in hours"""
        if not tasks:
            return 0
        
        total_time = sum(
            (task.completed_at - task.started_at).total_seconds()
            for task in tasks
            if task.completed_at and task.started_at
        )
        return total_time / tasks.count() / 3600

    def _calculate_lines_per_hour(self, tasks) -> float:
        """Calculate average lines picked per hour"""
        if not tasks:
            return 0
            
        total_lines = sum(task.lines.count() for task in tasks)
        total_hours = sum(
            (task.completed_at - task.started_at).total_seconds() / 3600
            for task in tasks
            if task.completed_at and task.started_at
        )
        
        return total_lines / total_hours if total_hours > 0 else 0

    def _calculate_accuracy_rate(self, tasks) -> float:
        """Calculate picking accuracy rate"""
        if not tasks:
            return 100
            
        total_lines = 0
        accurate_lines = 0
        
        for task in tasks:
            for line in task.lines.all():
                total_lines += 1
                if line.quantity == line.picked_quantity:
                    accurate_lines += 1
                    
        return (accurate_lines / total_lines * 100) if total_lines > 0 else 100

    def _get_completion_time_trend(self, tasks) -> List[Dict]:
        """Get trend of completion times over time"""
        return list(tasks.annotate(
            completion_time=(F('completed_at') - F('started_at'))
        ).values('created_at__date', 'completion_time').order_by('created_at__date'))

    def _get_avg_lines_per_hour(self) -> float:
        """Get historical average of lines picked per hour"""
        completed_tasks = PickingTask.objects.filter(
            company=self.company,
            status=PickingTaskStatus.COMPLETED.name
        )
        return self._calculate_lines_per_hour(completed_tasks)

    def _plan_workload_distribution(
        self,
        tasks,
        workers,
        shift_hours: int
    ) -> List[Dict]:
        """Plan how to distribute tasks among available workers"""
        worker_assignments = []
        
        # Simple round-robin distribution for now
        worker_cycle = cycle(workers)
        for task in tasks:
            worker = next(worker_cycle)
            worker_assignments.append({
                'worker': worker,
                'task': task,
                'estimated_duration': self._estimate_task_duration(task)
            })
            
        return worker_assignments

    def _estimate_task_duration(self, task: PickingTask) -> float:
        """Estimate how long a task will take based on historical data"""
        lines_count = task.lines.count()
        avg_time_per_line = 3/60  # 3 minutes per line as default
        
        similar_tasks = PickingTask.objects.filter(
            company=self.company,
            status=PickingTaskStatus.COMPLETED.name,
            lines__count=lines_count
        )
        
        if similar_tasks.exists():
            avg_time = similar_tasks.aggregate(
                avg_time=Avg(F('completed_at') - F('started_at'))
            )['avg_time']
            if avg_time:
                return avg_time.total_seconds() / 3600
        
        return lines_count * avg_time_per_line  # hours