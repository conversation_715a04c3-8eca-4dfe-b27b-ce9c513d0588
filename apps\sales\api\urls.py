from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(
    r'customers',
    views.CustomerViewSet,
    basename='customer'
)
router.register(
    r'price-lists',
    views.PriceListViewSet,
    basename='price-list'
)
router.register(
    r'discount-rules',
    views.DiscountRuleViewSet,
    basename='discount-rule'
)
router.register(
    r'orders',
    views.SalesOrderViewSet,
    basename='sales-order'
)
router.register(
    r'shipments',
    views.ShipmentViewSet,
    basename='shipment'
)
router.register(
    r'returns',
    views.ReturnOrderViewSet,
    basename='return-order'
)
router.register(
    r'customer-invoices',
    views.SalesInvoiceViewSet,
    basename='sales-invoice'
)
router.register(
    r'customer-categories',
    views.CustomerCategoryViewSet,
    basename='customer-category'
)

urlpatterns = [
    path('sales/', include(router.urls)),
    # Removing the standalone PDF endpoint as it's now part of the SalesInvoiceViewSet as an action
    # path('invoices/<int:invoice_id>/pdf/', views.SalesInvoicePDFView.as_view(), name='sales-invoice-pdf'),
]