from django.contrib.contenttypes.models import ContentType
from apps.core.models import Company
from apps.core.models import User
from typing import Dict, Any
from django.db import models
from django.core.serializers.json import DjangoJSONEncoder
from decimal import Decimal
import json
from django.db.models import Model

class AuditLogJSONEncoder(DjangoJSONEncoder):
    def default(self, obj):
        if isinstance(obj, Model):
            return str(obj.pk)
        if isinstance(obj, Decimal):
            return float(obj)
        # Skip ModelState and other non-serializable objects
        if hasattr(obj, '__dict__'):
            return str(obj)
        return super().default(obj)
    
class AuditLogService:
    """
    Service for handling audit log operations
    """
    @staticmethod
    def log_action(
        company: Company,
        instance: models.Model,
        action: str,
        user: User = None,
        data: Dict[str, Any] = None
    ) -> None:
        from apps.core.models import AuditLog
        
        content_type = ContentType.objects.get_for_model(instance)
        
        AuditLog.objects.create(
            company=company,
            user=user,
            content_type=content_type,
            object_id=instance.id,
            action=action,
            data=json.dumps(instance.__dict__, cls=AuditLogJSONEncoder) if instance else None,
        )