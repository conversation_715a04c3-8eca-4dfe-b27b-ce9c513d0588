from django.db import transaction
from django.utils import timezone
from apps.inventory.models import (
    InventoryAdjustment, InventoryAdjustmentLine,
    InventoryItem, Location
)
from apps.core.models import User
from apps.core.models import Employee
from .base import BaseOperationService
from decimal import Decimal
from typing import Optional, List, Dict
from django.db.models import QuerySet
from utils.enums import AdjustmentStatus
from datetime import datetime
from apps.products.models import Product, ProductVariant
from apps.inventory.models import Lot
from django.core.exceptions import ValidationError

class AdjustmentService(BaseOperationService):

    def _generate_reference_number(self) -> str:
        """Generate a unique reference number for the adjustment"""
        prefix = "ADJ"
        date_str = timezone.now().strftime("%y%m%d")
        count = InventoryAdjustment.objects.filter(
            company=self.company,
            reference_number__startswith=f"{prefix}{date_str}"
        ).count()
        return f"{prefix}{date_str}{str(count + 1).zfill(4)}"

    @transaction.atomic
    def create_adjustment(
        self,
        location_id: int,
        adjustment_type: str,
        lines: List[Dict],
        notes: str = ""
    ) -> InventoryAdjustment:
        """Create a new inventory adjustment"""
        # Create adjustment header
        location = Location.objects.get(
            company=self.company,
            id=location_id
        )
        
        adjustment = InventoryAdjustment.objects.create(
            company=self.company,
            reference_number=self._generate_reference_number(),
            location=location,
            adjustment_type=adjustment_type,
            notes=notes,
            status=AdjustmentStatus.DRAFT.name
        )

        print(f"LINES {lines}")
        # Process each line
        for line_data in lines:
            inventory_item = self._get_or_create_inventory_item(
                location=location,
                product=line_data.get('product'),
                variant=line_data.get('variant'),
                lot=line_data.get('lot')
            )

            InventoryAdjustmentLine.objects.create(
                company=self.company,
                adjustment=adjustment,
                inventory_item=inventory_item,
                product=line_data.get('product'),
                variant=line_data.get('variant'),
                lot=line_data.get('lot'),
                previous_quantity=inventory_item.quantity,
                new_quantity=line_data['new_quantity'],
                cost_price=inventory_item.cost_price
            )

        return adjustment

    @transaction.atomic
    def update_adjustment(
        self,
        adjustment: InventoryAdjustment,
        location: Location = None,
        adjustment_type: str = None,
        lines: List[Dict] = None,
        notes: str = "", 
    ) -> InventoryAdjustment:
        """Update an existing inventory adjustment in DRAFT status"""
        try:
            print(location)
            print(adjustment)
            print(adjustment_type)
            print(lines)
            print(notes)
            print("::::::::::::::::::::::::")
            # Get the adjustment and verify it's in DRAFT status
            adjustment = InventoryAdjustment.objects.select_for_update().get(
                company=self.company,
                uuid=adjustment.uuid,
                status=AdjustmentStatus.DRAFT.name
            )
            
            # Update the adjustment header
            adjustment.location = location
            adjustment.adjustment_type = adjustment_type
            adjustment.notes = notes
            adjustment.save()
            
            # Delete existing lines
            adjustment.lines.all().delete()
            
            # Create new lines
            for line_data in lines:
                inventory_item = self._get_or_create_inventory_item(
                    location=location,
                    product=line_data.get('product'),
                    variant=line_data.get('variant'),
                    lot=line_data.get('lot')
                )
                
                InventoryAdjustmentLine.objects.create(
                    company=self.company,
                    adjustment=adjustment,
                    inventory_item=inventory_item,
                    product=line_data.get('product'),
                    variant=line_data.get('variant'),
                    lot=line_data.get('lot'),
                    previous_quantity=inventory_item.quantity,
                    new_quantity=line_data['new_quantity'],
                    cost_price=inventory_item.cost_price
                )
        except Exception as e:
            from traceback_with_variables import format_exc
            print(format_exc(e))
            raise e
            
        return adjustment

    def _get_or_create_inventory_item(
        self,
        location: Location,
        product: Product,
        variant: Optional[ProductVariant] = None,
        lot: Optional[Lot] = None
    ) -> InventoryItem:
        """Get or create an inventory item record"""
        item, created = InventoryItem.objects.get_or_create(
            company=self.company,
            location=location,
            product=product,
            variant=variant,
            lot=lot,
            defaults={
                'quantity': Decimal('0'),
                'reserved_quantity': Decimal('0'),
                'cost_price': Decimal('0')
            }
        )
        return item

    @transaction.atomic
    def approve_adjustment(
        self,
        adjustment: InventoryAdjustment,
        user: User
    ) -> InventoryAdjustment:
        """Approve and process an inventory adjustment"""
        employee = Employee.objects.get(
            company=self.company,
            user=user
        )

        # Update inventory quantities
        for line in adjustment.lines.all():
            inventory_item = line.inventory_item
            inventory_item.quantity = line.new_quantity
            inventory_item.save()

        # Update adjustment status
        adjustment.status = AdjustmentStatus.COMPLETED.name
        adjustment.approved_by = employee
        adjustment.approved_at = timezone.now()
        adjustment.save()

        return adjustment

    @transaction.atomic
    def cancel_adjustment(
        self,
        adjustment: InventoryAdjustment
    ) -> InventoryAdjustment:
        """Cancel a pending adjustment"""
        
        adjustment.status = AdjustmentStatus.CANCELLED.name
        adjustment.save()
        
        return adjustment
    
    @transaction.atomic
    def await_approval(self, adjustment: InventoryAdjustment) -> InventoryAdjustment:
        adjustment.status = AdjustmentStatus.PENDING.name
        adjustment.save()
        return adjustment

    def get_adjustment_history(
        self,
        location_id: Optional[int] = None,
        product_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        status: Optional[str] = None
    ) -> QuerySet:
        """Get filtered adjustment history"""
        query = InventoryAdjustment.objects.filter(
            company=self.company
        ).select_related(
            'location',
            'approved_by'
        ).prefetch_related(
            'lines__product',
            'lines__variant',
            'lines__lot'
        )
        
        if location_id:
            query = query.filter(location_id=location_id)
        if product_id:
            query = query.filter(lines__product_id=product_id)
        if start_date:
            query = query.filter(created_at__gte=start_date)
        if end_date:
            query = query.filter(created_at__lte=end_date)
        if status:
            query = query.filter(status=status)
        
        return query.order_by('-created_at')