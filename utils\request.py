# utils/request.py
from rest_framework.request import Request
from dataclasses import dataclass

@dataclass
class EmptyCompany:
    """A null object pattern for company during schema generation"""
    id = None
    name = "Schema Company"
    # Add any other commonly accessed company attributes with safe defaults

class CompanyAwareRequest(Request):
    def __init__(self, request, parsers=None, authenticators=None, negotiator=None, 
                 parser_context=None, **kwargs):
        super().__init__(request, parsers, authenticators, negotiator, parser_context)
        self._company = None
        
    @property
    def company(self):
        if getattr(self, '_is_swagger_request', False):
            return EmptyCompany()
        return self._company
    
    @company.setter
    def company(self, value):
        self._company = value