from rest_framework import serializers
from decimal import Decimal
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import importlib
from django.db.models import Manager



class DecimalEncoder(DjangoJSONEncoder):
    """Custom JSON encoder that handles Decimal types"""
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)  # Convert Decimal to string
        return super().default(obj)

class BaseModelSerializer(serializers.ModelSerializer):
    """
    Base serializer for models that are company-specific
    Handles common validation and company injection
    Includes decimal serialization
    """

    representation_fields = {}
    class Meta:
        encoder = DecimalEncoder

    def validate(self, attrs):
        attrs = super().validate(attrs)
        if not getattr(self.context.get('request'), '_is_swagger_request', False):
            # Ensure company is not manually set
            if 'company' in attrs:
                raise ValidationError({
                    'company': _('Company cannot be manually set')
                })
        return attrs
    
    def create(self, validated_data):
        # Inject company from request context
        if hasattr(self.context['request'], 'company'):
            validated_data['company'] = self.context['request'].company
        
        return super().create(validated_data)

    def to_representation(self, instance):
        """
        Convert Decimal fields to floats in the representation

        Overwrite the primary key value with a full serialization of some FK instances in the to_representation method. 
        This method is only used when serializing instances to JSON, not when validating or deserializing JSON to instances, 
        so it won't affect the payload you need to send when creating or updating instances.
        It will be applied to the fields in the representation_fields dictionary that are set in each serializer inheriting from this serializer.
        """
        representation = super().to_representation(instance)
        # Get all Decimal fields from the model
        decimal_fields = [
            field.name for field in self.Meta.model._meta.fields 
            if isinstance(field, models.DecimalField)
        ]
        # Convert Decimal values to strings
        for field in decimal_fields:
            if field in representation and representation[field] is not None:
                representation[field] = float(representation[field])
        
        # Check if the child serializer has 'representation_fields' attribute
        if hasattr(self, 'representation_fields'):
            for field, serializer in self.representation_fields.items():
                value = getattr(instance, field)

                if isinstance(serializer, str):  # If the serializer is a string (module path)
                    module_name, class_name = serializer.rsplit(".", 1)
                    SerializerClass = getattr(importlib.import_module(module_name), class_name)
                else:  # If the serializer is a class
                    SerializerClass = serializer

                if value is not None:
                    if isinstance(value, Manager):  # if 'value' is a related objects manager
                        representation[field] = SerializerClass(value.all(), many=True).data
                    else:  # if 'value' is a single related instance
                        representation[field] = SerializerClass(value).data
                else:
                    representation[field] = None

        return representation
                
    
    def to_internal_value(self, data):
        # First convert the data
        internal_value = super().to_internal_value(data)
        
        # Convert any Decimal objects to strings
        for key, value in internal_value.items():
            if isinstance(value, Decimal):
                internal_value[key] = str(value)
                
        return internal_value
    
class BulkOperationSerializer(serializers.Serializer):
    """
    Serializer for handling bulk operations
    """
    items = serializers.ListField(
        child=serializers.DictField(),
        allow_empty=False
    )
    operation = serializers.ChoiceField(
        choices=['create', 'update', 'delete']
    )

class GetLineTaxRateSerializer(serializers.Serializer):
    """
    Serializer for getting the accurate tax rate for a purchase order line
    """
    from apps.products.models import Product
    from apps.purchasing.models import Supplier
    product = serializers.PrimaryKeyRelatedField(queryset=Product.objects.all())
    supplier = serializers.PrimaryKeyRelatedField(queryset=Supplier.objects.all())

