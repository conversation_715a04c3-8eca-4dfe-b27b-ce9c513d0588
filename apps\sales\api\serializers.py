# sales/serializers.py
from rest_framework import serializers
from utils.serializers import BaseModelSerializer
from apps.sales.models import (
    Customer, CustomerPriceList, PriceListItem, DiscountRule,
    SalesOrder, SalesOrderLine, Shipment, ShipmentLine,
    ReturnOrder, ReturnLine, ReturnInspection, ReturnCreditNote,
    CreditLimitChange, SalesInvoice, SalesInvoiceLine, CustomerAddress, CustomerContact, CustomerCategory,
    CustomerPriceHistory, CategoryPriceHistory, ShipmentTracking
)
from apps.core.models import CustomFieldValue
from apps.core.api.serializers import (
    PaymentTermSerializer, CustomFieldValueSerializer
)
from django.db import transaction



class ShipmentTrackingSerializer(BaseModelSerializer):
    class Meta:
        model = ShipmentTracking
        fields = ['id', 'uuid', 'shipment', 'event_date', 'event_type', 'location', 'description', 'carrier_reference']
        read_only_fields = ['id']


class CustomerAddressSerializer(BaseModelSerializer):
    """
    Serializer for customer addresses with type (billing, shipping, etc)
    """
    class Meta:
        model = CustomerAddress
        fields = [
            'id', 'uuid', 'address_type', 'name', 'street_address1', 
            'street_address2', 'city', 'state', 'zip_code',
            'country', 'is_default','notes'
        ]
        read_only_fields = ['id']

class CustomerContactSerializer(BaseModelSerializer):
    """
    Serializer for customer contacts with role and contact details
    """
    class Meta:
        model = CustomerContact
        fields = [
            'id', 'uuid', 'name', 'role', 'email', 'phone',
            'mobile', 'is_primary', 'notes'
        ]
        read_only_fields = ['id']

    def validate(self, data):
        """
        Check that at least one contact method is provided
        """
        if not any([data.get('email'), data.get('phone'), data.get('mobile')]):
            raise serializers.ValidationError(
                "At least one contact method (email, phone, or mobile) is required"
            )
        return data


class CustomerCategorySerializer(BaseModelSerializer):
    customer_count = serializers.SerializerMethodField()

    def get_customer_count(self, obj):
        return obj.customers.count()

    class Meta:
        model = CustomerCategory
        fields = [
            'id', 'uuid', 'name', 'number', 'description', 'customer_count'
        ]
        read_only_fields = ['id']

    def validate_number(self, value):
        if CustomerCategory.objects.filter(
            company=self.context['request'].company,
            number=value
        ).exists() and (self.instance is None or self.instance.number != value):
            raise serializers.ValidationError("Category number must be unique.")
        return value

class CustomerSerializer(BaseModelSerializer):
    credit_status = serializers.SerializerMethodField()
    active_price_lists = serializers.SerializerMethodField()
    contacts = CustomerContactSerializer(many=True, read_only=True)
    addresses = CustomerAddressSerializer(many=True, read_only=True)
    payment_terms_name = serializers.CharField(source='payment_terms.name', read_only=True)
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_number = serializers.CharField(source='category.number', read_only=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )
    

    representation_fields = {
        'payment_terms': PaymentTermSerializer,
        'category': CustomerCategorySerializer
    }

    class Meta:
        model = Customer
        fields = [
            'id', 'uuid', 'name', 'number', 'tax_number', 'email', 'phone',
            'street_address1', 'street_address2', 'city', 'state', 'zip_code',
            'country', 'payment_terms', 'payment_terms_name', 'credit_limit', 'is_active',
            'notes', 'credit_status', 'active_price_lists',
            'contacts',
            'addresses',
            'category',
            'category_name',
            'category_number',
            'custom_fields'
        ]
        read_only_fields = ['id']

    def get_credit_status(self, obj):
        service = self.context.get('credit_service')
        if service:
            return service.get_credit_status(obj.id)
        return None

    def get_active_price_lists(self, obj):
        return CustomerPriceListSerializer(
            obj.price_lists.filter(is_active=True),
            many=True
        ).data

    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        customer = super().create(validated_data)
        
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=customer.company,
                content_object=customer,
                **field_data
            )
        
        return customer

    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        customer = super().update(instance, validated_data)
        
        if custom_fields_data:
            # Clear existing values and create new ones
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=customer.company,
                    content_object=customer,
                    **field_data
                )
        
        return customer

class CreditLimitChangeSerializer(BaseModelSerializer):
    changed_by_name = serializers.CharField(
        source='changed_by.get_full_name',
        read_only=True
    )

    class Meta:
        model = CreditLimitChange
        fields = [
            'id', 'customer', 'old_limit', 'new_limit',
            'changed_by', 'changed_by_name', 'reason',
            'created_at'
        ]
        read_only_fields = ['id', 'changed_by', 'created_at']

class PriceListItemSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = PriceListItem
        fields = [
            'id', 'product', 'product_name', 'variant',
            'variant_name', 'unit_price', 'min_quantity'
        ]
        read_only_fields = ['id']

class CustomerPriceListSerializer(BaseModelSerializer):
    items = PriceListItemSerializer(many=True, required=False)
    currency_code = serializers.CharField(source='currency.code', read_only=True)

    class Meta:
        model = CustomerPriceList
        fields = [
            'id', 'name', 'is_active', 'price_valid_from', 
            'price_valid_to', 'customers', 'customer_categories',
            'currency', 'currency_code', 'items'
        ]
        read_only_fields = ['id']

    def to_representation(self, instance):
        data = super().to_representation(instance)
        data['customer_categories'] = CustomerCategorySerializer(
            instance.customer_categories.all(),
            many=True
        ).data
        return data

    @transaction.atomic
    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        price_list = super().create(validated_data)
        
        for item_data in items_data:
            item_data['company'] = price_list.company
            PriceListItem.objects.create(
                price_list=price_list,
                **item_data
            )
        
        return price_list

class DiscountRuleSerializer(BaseModelSerializer):
    total_uses = serializers.IntegerField(read_only=True)
    total_discount_amount = serializers.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        read_only=True
    )
    product_category_name = serializers.CharField(
        source='product_categories.first.name', 
        read_only=True
    )
    customer_category_name = serializers.CharField(
        source='customer_categories.first.name', 
        read_only=True
    )

    class Meta:
        model = DiscountRule
        fields = [
            'id', 'name', 'description', 'is_active',
            'discount_type', 'value', 'valid_from', 'valid_to',
            'min_order_value', 'max_order_value', 'max_uses',
            'max_uses_per_customer', 'products', 'product_categories',
            'customers', 'customer_categories', 'total_uses',
            'total_discount_amount', 'product_category_name',
            'customer_category_name'
        ]
        read_only_fields = ['id', 'total_uses', 'total_discount_amount']

class SalesOrderLineSerializer(BaseModelSerializer):
    id = serializers.IntegerField(required=False, allow_null=True)
    product_name = serializers.CharField(
        source='product.name',
        read_only=True
    )
    variant_name = serializers.CharField(
        source='variant.name',
        read_only=True,
        allow_null=True
    )
    available_quantity = serializers.DecimalField(
        max_digits=15,
        decimal_places=2,
        read_only=True
    )

    class Meta:
        model = SalesOrderLine
        fields = [
            'id', 'product', 'product_name', 'variant',
            'variant_name', 'quantity', 'shipped_quantity',
            'unit_price', 'discount', 'tax_rate',
            'available_quantity'
        ]
        read_only_fields = [
            'id', 'shipped_quantity', 'available_quantity'
        ]

class SalesOrderSerializer(BaseModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    fulfilling_warehouse_name = serializers.CharField(source='fulfilling_warehouse.name', read_only=True)
    lines = SalesOrderLineSerializer(many=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )
    price_list_name = serializers.CharField(
        source='price_list.name',
        read_only=True,
        allow_null=True
    )

    class Meta:
        model = SalesOrder
        fields = [
            'id', 'uuid', 'number', 'customer', 'customer_name',
            'order_date', 'requested_delivery_date', 'price_list',
            'price_list_name', 'status', 'shipping_address',
            'notes', 'total_amount', 'total_discount', 'lines',
            'currency', 'payment_term', 'delivery_term', 'currency_rate',
            'delivery_method', 'custom_fields', 'our_reference',
            'your_reference', 'expected_delivery_date', 'delivery_term',
            'delivery_method', 'payment_term', 'total_tax_amount',
            'fulfilling_warehouse', 'requested_delivery_date',
            'fulfilling_warehouse_name'
        ]
        read_only_fields = [
            'id', 'number', 'total_amount',
            'total_discount', 'status'
        ]

    @transaction.atomic
    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        lines_data = validated_data.pop('lines', [])
        service = self.context['order_service']
        
        order = service.create_order(
            customer_id=validated_data['customer'].id,
            lines=[{
                'product_id': line['product'].id,
                'variant_id': line.get('variant', {}).get('id'),
                'quantity': line['quantity']
            } for line in lines_data],
            requested_delivery_date=validated_data['requested_delivery_date'],
            shipping_address=validated_data['shipping_address'],
            notes=validated_data.get('notes'),
            currency=validated_data.get('currency'),
            payment_term=validated_data.get('payment_term'),
            delivery_term=validated_data.get('delivery_term'),
            delivery_method=validated_data.get('delivery_method'),
            currency_rate=validated_data.get('currency_rate'),
            our_reference=validated_data.get('our_reference'),
            your_reference=validated_data.get('your_reference')
        )

        # Create custom field values
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=order.company,
                content_object=order,
                **field_data
            )
        
        return order

    @transaction.atomic
    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        lines_data = validated_data.pop('lines', [])
        print(lines_data)
        
        # Update the purchase order instance with header data
        order = super().update(instance, validated_data)
        
        # Handle custom fields
        if custom_fields_data:
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=order.company,
                    content_object=order,
                    **field_data
                )
        
        # Handle lines if provided
        if lines_data:
            line_ids = [line.get('id') for line in lines_data]
            # Create new lines
            for line_data in lines_data:
                line_id = line_data.pop('id', None)
                if line_id:
                    line = SalesOrderLine.objects.get(id=line_id)
                    for key, value in line_data.items():
                        setattr(line, key, value)
                    line.save()
                else:
                    SalesOrderLine.objects.create(
                        company=order.company,
                        sales_order=order,
                        **line_data
                    )
            # Delete lines that are not in the request
            SalesOrderLine.objects.filter(
                company=order.company,
                sales_order=order,
            ).exclude(id__in=line_ids).delete()
            
            order.save()
        
        return order

class ShipmentLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='sales_order_line.product.name',
        read_only=True
    )
    picked_location = serializers.CharField(
        source='picked_from.name',
        read_only=True
    )

    class Meta:
        model = ShipmentLine
        fields = [
            'id', 'sales_order_line', 'product_name',
            'quantity', 'lot_number', 'serial_numbers',
            'picked_from', 'picked_location'
        ]
        read_only_fields = ['id']

class ShipmentSerializer(BaseModelSerializer):
    lines = ShipmentLineSerializer(many=True, read_only=True)
    shipment_trackings = ShipmentTrackingSerializer(
        source='tracking_events',
        many=True,
        read_only=True
    )
    shipped_by_name = serializers.CharField(
        source='shipped_by.get_full_name',
        read_only=True
    )

    class Meta:
        model = Shipment
        fields = [
            'id', 'uuid', 'number', 'sales_order', 'status',
            'tracking_number', 'carrier', 'shipping_cost',
            'shipped_by', 'shipped_by_name', 'shipped_at',
            'lines', 'shipping_method', 'shipment_trackings'
        ]
        read_only_fields = [
            'id', 'number', 'shipped_by', 'shipped_at'
        ]

class ReturnLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(
        source='sales_order_line.product.name',
        read_only=True
    )

    class Meta:
        model = ReturnLine
        fields = [
            'id', 'sales_order_line', 'product_name',
            'quantity', 'received_quantity', 'status',
            'reason', 'condition_on_return',
            'inspection_result', 'inspection_notes',
            'refund_amount'
        ]
        read_only_fields = [
            'id', 'received_quantity', 'status',
            'inspection_result', 'refund_amount'
        ]

class ReturnOrderSerializer(BaseModelSerializer):
    lines = ReturnLineSerializer(many=True)
    created_by_name = serializers.CharField(
        source='created_by.get_full_name',
        read_only=True
    )

    class Meta:
        model = ReturnOrder
        fields = [
            'id', 'number', 'sales_order', 'status',
            'return_reason', 'notes', 'created_by',
            'created_by_name', 'created_at', 'received_date',
            'completed_date', 'rma_number', 'lines'
        ]
        read_only_fields = [
            'id', 'number', 'created_by', 'created_at',
            'received_date', 'completed_date', 'rma_number'
        ]

class SalesInvoiceLineSerializer(BaseModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)

    class Meta:
        model = SalesInvoiceLine
        fields = [
            'id', 'product', 'product_name', 'variant', 'variant_name',
            'description', 'quantity', 'unit_price', 'tax_rate',
            'net_amount', 'tax_amount', 'total_amount'
        ]
        read_only_fields = ['id', 'net_amount', 'tax_amount', 'total_amount']

class SalesInvoiceFromOrderSerializer(serializers.Serializer):  
    sales_order_id = serializers.IntegerField()
    invoice_number = serializers.CharField()
    invoice_date = serializers.DateField()
    use_shipped_quantities = serializers.BooleanField(default=True)

class SalesInvoiceSerializer(BaseModelSerializer):
    lines = SalesInvoiceLineSerializer(many=True, read_only=True)
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    custom_fields = CustomFieldValueSerializer(
        source='custom_field_values',
        many=True,
        required=False
    )

    class Meta:
        model = SalesInvoice
        fields = [
            'id', 'uuid', 'number', 'invoice_number', 'customer', 'customer_name',
            'sales_order', 'invoice_date', 'due_date', 'currency',
            'currency_code', 'currency_rate', 'total_net_amount',
            'total_tax_amount', 'total_amount', 'status', 'final_paid_date',
            'payment_reference', 'notes', 'lines', 'custom_fields',
            'our_reference', 'your_reference', 'delivery_term', 'delivery_method',
            'payment_term',
        ]
        read_only_fields = [
            'id', 'number', 'total_net_amount', 'total_tax_amount',
            'total_amount', 'invoice_number'
        ]

    def create(self, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        invoice = super().create(validated_data)
        
        for field_data in custom_fields_data:
            CustomFieldValue.objects.create(
                company=invoice.company,
                content_object=invoice,
                **field_data
            )
        
        return invoice

    def update(self, instance, validated_data):
        custom_fields_data = validated_data.pop('custom_field_values', [])
        invoice = super().update(instance, validated_data)
        
        if custom_fields_data:
            instance.custom_field_values.all().delete()
            for field_data in custom_fields_data:
                CustomFieldValue.objects.create(
                    company=invoice.company,
                    content_object=invoice,
                    **field_data
                )
        
        return invoice

class CustomerPriceHistorySerializer(BaseModelSerializer):
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)
    price_list_name = serializers.CharField(source='price_list.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)

    class Meta:
        model = CustomerPriceHistory
        fields = [
            'id', 'customer', 'customer_name',
            'product', 'product_name',
            'variant', 'variant_name',
            'price_list', 'price_list_name',
            'unit_price', 'currency', 'currency_code',
            'price_valid_from', 'price_valid_to',
            'changed_by', 'changed_by_name',
            'change_reason', 'is_active'
        ]
        read_only_fields = ['id', 'changed_by']

class CategoryPriceHistorySerializer(BaseModelSerializer):
    category_name = serializers.CharField(source='category.name', read_only=True)
    product_name = serializers.CharField(source='product.name', read_only=True)
    variant_name = serializers.CharField(source='variant.name', read_only=True)
    price_list_name = serializers.CharField(source='price_list.name', read_only=True)
    currency_code = serializers.CharField(source='currency.code', read_only=True)
    changed_by_name = serializers.CharField(source='changed_by.get_full_name', read_only=True)

    class Meta:
        model = CategoryPriceHistory
        fields = [
            'id', 'category', 'category_name',
            'product', 'product_name',
            'variant', 'variant_name',
            'price_list', 'price_list_name',
            'unit_price', 'currency', 'currency_code',
            'price_valid_from', 'price_valid_to',
            'changed_by', 'changed_by_name',
            'change_reason', 'is_active'
        ]
        read_only_fields = ['id', 'changed_by']

