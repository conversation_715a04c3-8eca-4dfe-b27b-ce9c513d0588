from typing import List, Dict, Any
from django.db import transaction
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from .serializers import BulkOperationSerializer
from rest_framework.exceptions import PermissionDenied

class BulkOperationMixin:
    """
    Mixin to add bulk operation capabilities to viewsets
    """
    @action(detail=False, methods=['post'])
    def bulk(self, request):
        serializer = BulkOperationSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        operation = serializer.validated_data['operation']
        items = serializer.validated_data['items']
        
        with transaction.atomic():
            try:
                if operation == 'create':
                    return self._bulk_create(items)
                elif operation == 'update':
                    return self._bulk_update(items)
                elif operation == 'delete':
                    return self._bulk_delete(items)
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
    
    def _bulk_create(self, items: List[Dict[str, Any]]) -> Response:
        from apps.core.services.audit import AuditLogService
        serializer = self.get_serializer(data=items, many=True)
        serializer.is_valid(raise_exception=True)
        instances = serializer.save()
        
        # Log audit for each created instance
        for instance in instances:
            AuditLogService.log_action(
                company=self.request.company,
                user=self.request.user,
                instance=instance,
                action='bulk_create',
                data={'created': True}
            )
        
        return Response(
            self.get_serializer(instances, many=True).data,
            status=status.HTTP_201_CREATED
        )
    
    def _bulk_update(self, items: List[Dict[str, Any]]) -> Response:
        from apps.core.services.audit import AuditLogService
        updated_instances = []
        for item in items:
            instance = self.get_queryset().get(id=item.pop('id'))
            serializer = self.get_serializer(
                instance,
                data=item,
                partial=True
            )
            serializer.is_valid(raise_exception=True)
            updated_instance = serializer.save()
            updated_instances.append(updated_instance)
            
            # Log audit for update
            AuditLogService.log_action(
                company=self.request.company,
                user=self.request.user,
                instance=updated_instance,
                action='bulk_update',
                data={'updated': item}
            )
        
        return Response(
            self.get_serializer(updated_instances, many=True).data
        )
    
    def _bulk_delete(self, items: List[Dict[str, Any]]) -> Response:
        from apps.core.services.audit import AuditLogService
        ids = [item['id'] for item in items]
        instances = self.get_queryset().filter(id__in=ids)
        
        # Log audit before deletion
        for instance in instances:
            AuditLogService.log_action(
                company=self.request.company,
                user=self.request.user,
                instance=instance,
                action='bulk_delete',
                data={'deleted': True}
            )
        
        instances.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

