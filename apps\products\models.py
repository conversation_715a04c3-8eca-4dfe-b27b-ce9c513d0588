# products/models.py
from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from apps.base.models import CompanyFKRel
from apps.core.models import VATRates
from utils.enums import SizeUnits, WeightUnits, PriceChangeType
from apps.base.models import BaseModel, CategoryBase
from django.core.exceptions import ValidationError
from django.contrib.contenttypes.fields import GenericForeignKey, GenericRelation
from django.contrib.contenttypes.models import ContentType

optional = {'null': True, 'blank': True}

class ProductDimensions(BaseModel):
    """
    Abstract base class for product physical attributes.
    Used by both products and variants to maintain consistent dimension tracking.
    """
    weight = models.DecimalField(
        max_digits=10,
        decimal_places=3,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Product weight in specified unit"
    )
    weight_unit = models.CharField(
        max_length=3,
        choices=WeightUnits.choices(),
        default=WeightUnits.KG.name,
        help_text="Unit of measurement for weight"
    )
    width = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Product width in specified unit"
    )
    height = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Product height in specified unit"
    )
    depth = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Product depth in specified unit"
    )
    size_unit = models.CharField(
        max_length=3,
        choices=SizeUnits.choices(),
        default=SizeUnits.CM.name,
        help_text="Unit of measurement for dimensions"
    )

    class Meta:
        abstract = True

class ProductType(VATRates):
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=100)
    description = models.TextField(blank=True)

    class Meta:
        unique_together = ['company', 'number']


class ProductCategory(CategoryBase):
    """
    Hierarchical organization of products.
    Categories can have sub-categories and control whether products can be sold.
    """
    type = models.ForeignKey(ProductType, on_delete=models.CASCADE, related_name='categories')
    can_be_sold = models.BooleanField(
        default=True,
        help_text="Whether products in this category can be sold"
    )

    class Meta:
        verbose_name_plural = "product categories"
        unique_together = ['company', 'name', 'parent']
        indexes = [models.Index(fields=['company', 'name'])]

    def __str__(self):
        return f"{self.name}"


class Product(CompanyFKRel, ProductDimensions):
    """
    Main product model representing items that can be sold and tracked.
    Contains master data about the product but not actual inventory levels.
    """
    number = models.CharField(max_length=100)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    type = models.ForeignKey(ProductType, on_delete=models.CASCADE, related_name='products')
    sku = models.CharField(
        max_length=100,
        help_text="Stock Keeping Unit - unique product identifier"
    )
    barcode = models.CharField(
        max_length=100,
        blank=True,
        db_index=True,
        help_text="Product barcode (e.g., EAN, UPC)"
    )
    primary_supplier = models.ForeignKey(
        'purchasing.Supplier',
        on_delete=models.SET_NULL,
        help_text="Primary supplier for this product",
        related_name='primary_supplier_products',
        **optional
    )
    
    # Classifications
    category = models.ForeignKey(
        ProductCategory,
        on_delete=models.SET_NULL,
        help_text="Product category classification",
        related_name='products',
        **optional
    )
    unit_of_measure = models.ForeignKey(
        'core.UnitOfMeasure',
        on_delete=models.PROTECT,
        help_text="Unit used for selling and tracking this product"
    )
    
    # Inventory rules
    min_stock_level = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Minimum desired stock level across all locations"
    )
    max_stock_level = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Maximum desired stock level across all locations",
        **optional
    )
    lot_tracking_required = models.BooleanField(
        default=False,
        help_text="Whether this product requires lot/batch tracking"
    )

    # Level rules 
    min_order_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Minimum order quantity",
        **optional
    )
    min_purchase_quantity = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Minimum purchase quantity",
        **optional
    )
    min_inventory_level = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Minimum inventory level",
        **optional
    )
    
    # Media
    image = models.ImageField(
        upload_to='products/images/',
        help_text="Product image",
        **optional
    )
    pdf = models.FileField(
        upload_to='products/pdfs/',
        help_text="Product documentation",
        **optional
    )
    
    # Status
    is_active = models.BooleanField(
        default=True,
        db_index=True,
        help_text="Whether this product is active in the system"
    )

    custom_field_values = GenericRelation(
        'core.CustomFieldValue',
        related_query_name='product'
    )

    class Meta:
        unique_together = ['company', 'sku']
        indexes = [
            models.Index(fields=['company', 'sku']),
            models.Index(fields=['barcode']),
        ]

    def __str__(self):
        return f"{self.name} ({self.sku})"

    def save(self, *args, **kwargs):
        try:
            if not self.pk:  # Only set defaults for new records
                settings = self.company.company_settings
                if not self.type and settings.default_product_type:
                    self.type = settings.default_product_type
                if not self.primary_supplier and settings.default_product_supplier:
                    self.primary_supplier = settings.default_product_supplier
                if not self.category and settings.default_product_category:
                    self.category = settings.default_product_category
                if not self.unit_of_measure and settings.default_unit_of_measure:
                    self.unit_of_measure = settings.default_unit_of_measure
            super().save(*args, **kwargs)
        except Exception as e:
            from traceback_with_variables import format_exc
            print(format_exc(e))

class ProductVariant(CompanyFKRel, ProductDimensions):
    """
    Represents variations of a product (e.g., different sizes, colors).
    Inherits physical attributes from ProductDimensions.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='variants',
        help_text="Parent product"
    )
    number = models.CharField(
        max_length=100,
        help_text="Variant number (e.g., '123456')"
    )
    name = models.CharField(
        max_length=255,
        help_text="Variant name (e.g., 'Large Red')"
    )
    sku_suffix = models.CharField(
        max_length=50,
        help_text="Suffix added to product SKU to identify variant"
    )
    barcode = models.CharField(
        max_length=100,
        blank=True,
        db_index=True,
        help_text="Variant-specific barcode if different from product"
    )
    is_active = models.BooleanField(
        default=True,
        db_index=True
    )

    class Meta:
        unique_together = ['company', 'product', 'sku_suffix']
        indexes = [
            models.Index(fields=['company', 'product', 'sku_suffix']),
        ]

    def __str__(self):
        return f"{self.product.name} - {self.name}"

class ProductPricing(CompanyFKRel):
    """
    Handles pricing information for products and variants.
    Supports multiple currencies and pricing rules.
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='prices',
        help_text="Link to the main product"
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        related_name='prices',
        help_text="Optional variant if price is variant-specific",
        **optional
    )
    currency = models.ForeignKey(
        'core.Currency', 
        on_delete=models.CASCADE,
        help_text="Currency for this price set"
    )
    
    # Base prices
    base_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Base purchasing price before any adjustments"
    )
    actual_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Actual current purchasing price including all costs",
        **optional
    )
    suggested_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="System-suggested purchasing price for future procurement",
        **optional
    )
    base_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Base selling price before any adjustments"
    )
    recommended_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Recommended selling price (RSP/RRP)",
        **optional
    )
    
    # Cost and margin fields
    extra_cost_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Additional cost percentage for handling, storage, etc."
    )
    extra_cost_fixed = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0,
        help_text="Fixed additional costs in currency units"
    )
    profit_margin_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Target profit margin percentage"
    )
    contribution_margin_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Contribution margin percentage (täckningsbidrag)"
    )
    
    # Pricing status and metadata
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this price set is currently active"
    )
    last_purchase_date = models.DateTimeField(
        help_text="Date of last purchase at this price",
        **optional
    )
    price_valid_from = models.DateTimeField(
        help_text="Start date for price validity",
        **optional
    )
    price_valid_to = models.DateTimeField(
        help_text="End date for price validity",
        **optional
    )
    
    # Price history tracking
    previous_base_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Previous base purchasing price for tracking changes",
        **optional
    )
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        related_name='product_price_changes',
        **optional
    )
    price_change_reason = models.CharField(
        max_length=255,
        blank=True,
        help_text="Reason for the last price change"
    )

    class Meta:
        unique_together = ['company', 'product', 'variant', 'currency']
        indexes = [
            models.Index(fields=['company', 'product', 'currency']),
            models.Index(fields=['company', 'variant', 'currency']),
            models.Index(fields=['is_active', 'price_valid_from', 'price_valid_to']),
        ]
        verbose_name = "Product Pricing"
        verbose_name_plural = "Product Pricing"

    def __str__(self):
        base = f"{self.product.name} - {self.currency.currency}"
        return f"{base} ({self.variant.name})" if self.variant else base
        
    def clean(self):
        """Validate pricing data"""
        if self.price_valid_to and self.price_valid_from:
            if self.price_valid_to < self.price_valid_from:
                raise ValidationError({
                    'price_valid_to': 'End date cannot be before start date'
                })
    
    # products/models.py in ProductPricing class

    def save(self, *args, **kwargs):
        # Track if this is a new entry or price change
        is_new = self._state.adding
        
        if not is_new:
            # Get the old instance to compare prices
            old_instance = ProductPricing.objects.get(pk=self.pk)
            price_changed = (
                old_instance.base_purchasing_price != self.base_purchasing_price or
                old_instance.actual_purchasing_price != self.actual_purchasing_price or
                old_instance.base_selling_price != self.base_selling_price or
                old_instance.recommended_selling_price != self.recommended_selling_price
            )
            
            if price_changed:
                PriceHistoryEntry.objects.create(
                    company=self.company,
                    product=self.product,
                    variant=self.variant,
                    currency=self.currency,
                    base_purchasing_price=self.base_purchasing_price,
                    actual_purchasing_price=self.actual_purchasing_price,
                    base_selling_price=self.base_selling_price,
                    recommended_selling_price=self.recommended_selling_price,
                    changed_by=self.changed_by if hasattr(self, 'changed_by') else None,
                    change_reason=self.price_change_reason or "Price update",
                    change_type=PriceChangeType.MANUAL.name
                )
        
        super().save(*args, **kwargs)


class ProductBundle(CompanyFKRel):
    """
    A bundle is a group of products sold together as a single unit
    Can have its own pricing and affects inventory
    """
    name = models.CharField(max_length=255)
    number = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    sku = models.CharField(max_length=100, unique=True)
    barcode = models.CharField(max_length=100, blank=True)
    is_active = models.BooleanField(default=True)
    
    price_override = models.BooleanField(
        default=False,
        help_text="If True, use bundle price instead of sum of components"
    )
    discount_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        validators=[MinValueValidator(Decimal('0'))],
        help_text="Discount percentage when price_override is False"
    )
    
    image = models.ImageField(upload_to='bundles/images/', null=True, blank=True)
    
    class Meta:
        unique_together = ['company', 'sku']
        indexes = [
            models.Index(fields=['company', 'sku']),
            models.Index(fields=['barcode']),
        ]

class BundleComponent(CompanyFKRel):
    """Represents a product or variant in a bundle with quantity"""
    bundle = models.ForeignKey(
        ProductBundle,
        on_delete=models.CASCADE,
        related_name='components'
    )
    product = models.ForeignKey(
        'Product',
        on_delete=models.CASCADE,
        **optional
    )
    variant = models.ForeignKey(
        'ProductVariant',
        on_delete=models.CASCADE,
        **optional
    )
    quantity = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0'))]
    )
    is_optional = models.BooleanField(
        default=False,
        help_text="If True, this component is optional in the bundle"
    )
    
    class Meta:
        unique_together = ['company', 'bundle', 'product', 'variant']
        constraints = [
            models.CheckConstraint(
                check=(
                    models.Q(product__isnull=False, variant__isnull=True) |
                    models.Q(product__isnull=True, variant__isnull=False)
                ),
                name='bundle_product_or_variant_not_both'
            )
        ]

class BundlePricing(CompanyFKRel):
    """Separate model for bundle pricing"""
    bundle = models.ForeignKey(
        ProductBundle,
        on_delete=models.CASCADE,
        related_name='prices'
    )
    currency = models.ForeignKey('core.Currency', on_delete=models.CASCADE)
    
    base_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Override price when bundle.price_override is True"
    )
    cost_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Cost price for the bundle",
        **optional
    )
    
    is_active = models.BooleanField(default=True)
    
    class Meta:
        unique_together = ['company', 'bundle', 'currency']
        indexes = [
            models.Index(fields=['company', 'bundle', 'currency']),
        ]



class PriceHistoryEntry(CompanyFKRel):
    """
    Tracks historical changes to product and variant prices
    """
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='price_history',
        help_text="Product reference if price change was for product",
        **optional
    )
    variant = models.ForeignKey(
        ProductVariant,
        on_delete=models.CASCADE,
        related_name='price_history',
        help_text="Variant reference if price change was for variant",
        **optional
    )
    currency = models.ForeignKey(
        'core.Currency',
        on_delete=models.CASCADE,
        help_text="Currency for this price entry"
    )
    
    # Price values
    base_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Base purchasing price at time of change"
    )
    actual_purchasing_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Actual purchasing price at time of change",
        **optional
    )
    base_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Base selling price at time of change"
    )
    recommended_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        help_text="Recommended selling price at time of change"
    )
    
    # Change metadata
    changed_at = models.DateTimeField(auto_now_add=True)
    changed_by = models.ForeignKey(
        'core.Employee',
        on_delete=models.SET_NULL,
        related_name='price_changes',
        **optional
    )
    change_reason = models.CharField(
        max_length=255,
        blank=True,
        help_text="Reason for the price change"
    )
    change_type = models.CharField(
        max_length=40,
        choices=PriceChangeType.choices(),
        default=PriceChangeType.MANUAL.name
    )

    class Meta:
        verbose_name = "Price History Entry"
        verbose_name_plural = "Price History Entries"
        ordering = ['-changed_at']
        indexes = [
            models.Index(fields=['company', 'product', 'changed_at']),
            models.Index(fields=['company', 'variant', 'changed_at']),
            models.Index(fields=['company', 'currency', 'changed_at']),
        ]

    def __str__(self):
        target = self.product.name if self.product else self.variant.name
        return f"Price change for {target} at {self.changed_at}"