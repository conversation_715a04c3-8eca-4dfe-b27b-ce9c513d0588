# operations/services/base.py
from django.db import transaction
from typing import Any, Dict, Optional
from apps.core.models import User, Company

class BaseOperationService:
    def __init__(self, user: User, company: Company):
        self.user = user
        self.company = company

    def validate_access(self) -> bool:
        """Validate user has access to perform operations"""
        return True  # Implement specific validation logic in child classes